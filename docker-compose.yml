version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: sqq-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${DB_DATABASE}
      POSTGRES_USER: ${DB_USERNAME}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - sqq-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USERNAME} -d ${DB_DATABASE}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Flask API (SeQRNG)
  seqrng-api:
    build:
      context: ./sqq-seqrng-api
      dockerfile: Dockerfile
    container_name: sqq-seqrng-api
    restart: unless-stopped
    environment:
      - PORT=3001
      - FLASK_HOST=0.0.0.0
      - FLASK_ENV=production
      - FLASK_DEBUG=false
      - SEQRNG_IP_ADDRESS=${SEQRNG_IP_ADDRESS}
      - SEQRNG_API_TOKEN=${SEQRNG_API_TOKEN}
      - CTM_IP_ADDRESS=${CTM_IP_ADDRESS}
      - CTM_USERNAME=${CTM_USERNAME}
      - CTM_PASSWORD=${CTM_PASSWORD}
      - CTM_DOMAIN=${CTM_DOMAIN}
    ports:
      - "3001:3001"
    networks:
      - sqq-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # NestJS API
  nestjs-api:
    build:
      context: ./sqq-api
      dockerfile: Dockerfile
    container_name: sqq-nestjs-api
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - PORT=3000
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USERNAME=${DB_USERNAME}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_DATABASE=${DB_DATABASE}
      - JWT_SECRET=${JWT_SECRET}
      - JWT_EXPIRES_IN=${JWT_EXPIRES_IN}
      - JWT_REFRESH_SECRET=${JWT_REFRESH_SECRET}
      - JWT_REFRESH_EXPIRES_IN=${JWT_REFRESH_EXPIRES_IN}
      - BCRYPT_ROUNDS=${BCRYPT_ROUNDS}
      - SEQRNG_API_URL=http://seqrng-api:3001
    ports:
      - "3000:3000"
    depends_on:
      postgres:
        condition: service_healthy
      seqrng-api:
        condition: service_healthy
    networks:
      - sqq-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 90s
    volumes:
      # Mount logs directory for debugging if needed
      - ./logs/nestjs:/app/logs

  # React Frontend
  frontend:
    build:
      context: ./sqq-frontend
      dockerfile: Dockerfile
      args:
        - VITE_API_BASE_URL=${FRONTEND_API_URL:-http://localhost:3000}
        - VITE_API_TIMEOUT=30000
        - VITE_NODE_ENV=production
    container_name: sqq-frontend
    restart: unless-stopped
    ports:
      - "80:80"
    depends_on:
      nestjs-api:
        condition: service_healthy
    networks:
      - sqq-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 3s
      retries: 3
      start_period: 10s

volumes:
  postgres_data:
    driver: local

networks:
  sqq-network:
    driver: bridge
