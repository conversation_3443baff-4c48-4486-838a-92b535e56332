# ==============================================
# Docker Compose Environment Configuration
# ==============================================
# Copia este archivo a .env y configura tus valores específicos

# Database Configuration
DB_HOST=postgres
DB_PORT=5432
DB_USERNAME=sqq_user
DB_PASSWORD=CHANGE_THIS_SECURE_PASSWORD
DB_DATABASE=sqq_database

# JWT Configuration - CAMBIAR EN PRODUCCIÓN
JWT_SECRET=CHANGE_THIS_TO_A_SECURE_SECRET_KEY_AT_LEAST_32_CHARACTERS
JWT_EXPIRES_IN=1h
JWT_REFRESH_SECRET=CHANGE_THIS_TO_A_SECURE_REFRESH_SECRET_KEY_AT_LEAST_32_CHARACTERS
JWT_REFRESH_EXPIRES_IN=7d

# Bcrypt Configuration
BCRYPT_ROUNDS=12

# Frontend Configuration
# Cambia localhost por la IP de tu servidor para acceso externo
# Ejemplo: FRONTEND_API_URL=http://*************:3000
FRONTEND_API_URL=http://localhost:3000

# SeQRNG Configuration - CONFIGURAR CON TUS VALORES
SEQRNG_IP_ADDRESS=http://YOUR_SEQRNG_SERVER:PORT
SEQRNG_API_TOKEN=YOUR_SEQRNG_API_TOKEN

# CipherTrust Manager Configuration - CONFIGURAR CON TUS VALORES
CTM_IP_ADDRESS=YOUR_CTM_SERVER_IP
CTM_USERNAME=YOUR_CTM_USERNAME
CTM_PASSWORD=YOUR_CTM_PASSWORD
CTM_DOMAIN=YOUR_CTM_DOMAIN

# ==============================================
# IMPORTANT SECURITY NOTES:
# ==============================================
# 1. Change ALL default passwords and secrets before deploying to production
# 2. Use strong, unique passwords for database and JWT secrets
# 3. JWT secrets should be at least 32 characters long
# 4. Consider using Docker secrets or external secret management for production
# 5. Ensure this .env file is not committed to version control
# 6. Set appropriate file permissions (600) for this file: chmod 600 .env
# ==============================================
