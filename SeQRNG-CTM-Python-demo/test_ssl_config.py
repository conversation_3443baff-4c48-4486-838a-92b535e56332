#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for SSL configuration
Tests the SSL verification setup for both development and production environments
"""

import os
import sys
from pathlib import Path

def test_ssl_configuration():
    """Test SSL configuration for different environments"""
    print("🔒 SSL Configuration Test")
    print("=" * 50)
    
    try:
        from config import get_config
        
        # Test development environment
        print("\n🧪 Testing Development Environment:")
        print("-" * 30)
        
        # Set development environment
        os.environ['ENVIRONMENT'] = 'development'
        os.environ['VERIFY_SSL'] = 'true'
        
        config = get_config()
        
        print(f"Environment: {config.get('ENVIRONMENT', 'development')}")
        print(f"Verify SSL: {config.get('VERIFY_SSL', 'true')}")
        
        # Test SeQRNG SSL config
        seqrng_ssl = config.get_ssl_verify_option('seqrng')
        print(f"SeQRNG SSL Verify: {seqrng_ssl}")
        
        # Test CTM SSL config
        ctm_ssl = config.get_ssl_verify_option('ctm')
        print(f"CTM SSL Verify: {ctm_ssl}")
        
        # Test production environment
        print("\n🏭 Testing Production Environment:")
        print("-" * 30)
        
        # Set production environment
        os.environ['ENVIRONMENT'] = 'production'
        os.environ['VERIFY_SSL'] = 'true'
        
        config = get_config()
        
        print(f"Environment: {config.get('ENVIRONMENT', 'development')}")
        print(f"Verify SSL: {config.get('VERIFY_SSL', 'true')}")
        
        # Test SeQRNG SSL config
        seqrng_ssl = config.get_ssl_verify_option('seqrng')
        print(f"SeQRNG SSL Verify: {seqrng_ssl}")
        
        # Test CTM SSL config
        ctm_ssl = config.get_ssl_verify_option('ctm')
        print(f"CTM SSL Verify: {ctm_ssl}")
        
        # Test with SSL disabled
        print("\n⚠️  Testing SSL Disabled:")
        print("-" * 30)
        
        os.environ['VERIFY_SSL'] = 'false'
        config = get_config()
        
        print(f"Environment: {config.get('ENVIRONMENT', 'development')}")
        print(f"Verify SSL: {config.get('VERIFY_SSL', 'true')}")
        
        # Test SeQRNG SSL config
        seqrng_ssl = config.get_ssl_verify_option('seqrng')
        print(f"SeQRNG SSL Verify: {seqrng_ssl}")
        
        # Test CTM SSL config
        ctm_ssl = config.get_ssl_verify_option('ctm')
        print(f"CTM SSL Verify: {ctm_ssl}")
        
        # Test certificate paths
        print("\n📜 Testing Certificate Paths:")
        print("-" * 30)
        
        # Check if production certificate exists
        prod_cert_path = '/etc/pki/tls/certs/apache-selfsigned.crt'
        if Path(prod_cert_path).exists():
            print(f"✅ Production certificate found: {prod_cert_path}")
        else:
            print(f"❌ Production certificate not found: {prod_cert_path}")
        
        # Test with custom certificate path
        os.environ['ENVIRONMENT'] = 'development'
        os.environ['VERIFY_SSL'] = 'true'
        os.environ['SEQRNG_CERT_PATH'] = '/tmp/test-cert.crt'
        
        config = get_config()
        seqrng_ssl = config.get_ssl_verify_option('seqrng')
        print(f"Custom SeQRNG certificate: {seqrng_ssl}")
        
        print("\n✅ SSL Configuration Test Completed")
        
    except Exception as e:
        print(f"❌ Error testing SSL configuration: {e}")
        return False
    
    return True

def test_certifi_availability():
    """Test if certifi is available"""
    print("\n📦 Testing certifi availability:")
    print("-" * 30)
    
    try:
        import certifi
        cert_path = certifi.where()
        print(f"✅ certifi available: {cert_path}")
        
        if Path(cert_path).exists():
            print(f"✅ Certificate file exists: {cert_path}")
        else:
            print(f"❌ Certificate file not found: {cert_path}")
            
        return True
    except ImportError:
        print("❌ certifi not available")
        print("   Install with: pip install certifi")
        return False

def main():
    """Main function"""
    print("🔒 SSL Configuration Test Suite")
    print("=" * 50)
    
    # Test certifi availability
    certifi_ok = test_certifi_availability()
    
    # Test SSL configuration
    ssl_ok = test_ssl_configuration()
    
    print("\n📊 Test Results:")
    print("-" * 20)
    print(f"certifi: {'✅ OK' if certifi_ok else '❌ FAILED'}")
    print(f"SSL Config: {'✅ OK' if ssl_ok else '❌ FAILED'}")
    
    if certifi_ok and ssl_ok:
        print("\n🎉 All tests passed!")
        print("\n📋 Next steps:")
        print("1. Set up your .env file with proper configuration")
        print("2. Test with: python debug_ctm.py")
        print("3. For production, ensure ENVIRONMENT=production")
    else:
        print("\n⚠️  Some tests failed. Please check the configuration.")
        
        if not certifi_ok:
            print("\n🔧 To fix certifi:")
            print("   pip install certifi")

if __name__ == "__main__":
    main() 