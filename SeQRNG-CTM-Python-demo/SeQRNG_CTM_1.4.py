﻿#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SeQRNG-CTM Integration v1.4
===========================

Enhanced integration between SeQRNG quantum random number generator
and Thales CipherTrust Manager with improved security features.
"""

import sys
import os
import re
from config import get_config
from sq_ctm_modules_v1 import ctm_get_api_key, ctm_upload_key, ctm_key_exists, group_bytes, check_key_length
from interface_seqrng_v2 import sq_get_random_bytes
from secure_key_generator import SecureKeyNameGenerator

def is_valid_key_name(name: str) -> bool:
    """
    Validar que el nombre de la llave no contenga espacios ni caracteres especiales.
    Solo permite caracteres alfanuméricos, guiones (-) y guiones bajos (_).
    """
    if not name:
        print("❌ El nombre no puede estar vacío.")
        return False
    if ' ' in name:
        print("❌ El nombre no puede contener espacios.")
        return False
    # Regex para permitir solo letras, númer<PERSON>, guión y guión bajo
    if not re.match(r'^[a-zA-Z0-9_-]+$', name):
        print("❌ El nombre solo puede contener letras, números, guiones (-) y guiones bajos (_).")
        return False
    return True

def handle_existing_key(keyname):
    """
    Manejar el caso cuando una llave ya existe
    
    Args:
        keyname (str): Nombre de la llave que ya existe
        
    Returns:
        str: Nuevo nombre de llave o None si el usuario cancela
    """
    print(f"\n⚠️  LLAVE YA EXISTE")
    print("=" * 40)
    print(f"❌ La llave '{keyname}' ya existe en CTM")
    print("\n🔒 IMPORTANTE:")
    print("   • No se permite hacer overwrite de llaves existentes")
    print("   • Esto protege contra pérdida accidental de datos")
    print("   • Para actualizar una llave existente, use 'Create new version'")
    
    print("\n📋 OPCIONES DISPONIBLES:")
    print("   1. Ingresar un nuevo nombre de llave")
    print("   2. Ir al menú principal para crear nueva versión")
    print("   3. Cancelar la operación")
    
    while True:
        print("\n❓ ¿Qué desea hacer?")
        print("   [1] Nuevo nombre")
        print("   [2] Ir al menú principal")
        print("   [3] Cancelar")
        
        choice = get_menu_choice([
            (1, "Nuevo nombre"),
            (2, "Ir al menú principal"),
            (3, "Cancelar")
        ])
        
        if choice == 1:
            # Opción 1: Ingresar nuevo nombre
            print("\n📝 Ingrese un nuevo nombre de llave:")
            new_name = input("Nuevo nombre: ").strip()
            
            if not new_name:
                print("❌ El nombre no puede estar vacío")
                continue
                
            if new_name == keyname:
                print("❌ El nuevo nombre no puede ser igual al anterior")
                continue
                
            # Verificar si el nuevo nombre también existe
            try:
                if ctm_key_exists(ctm_base_url, ctm_api_key, new_name, ctm_ssl_verify):
                    print(f"⚠️ El nombre '{new_name}' también existe")
                    print("   Por favor, intente con otro nombre")
                    continue
                else:
                    print(f"✅ El nombre '{new_name}' está disponible")
                    return new_name
            except Exception as e:
                print(f"⚠️ Error verificando el nuevo nombre: {e}")
                print("   Continuando con el nombre ingresado...")
                return new_name
                
        elif choice == 2:
            # Opción 2: Ir al menú principal
            print("\n🔄 Redirigiendo al menú principal...")
            print("   Seleccione 'Create new version of existing key'")
            print("   para actualizar la llave existente con nuevo material cuántico")
            return None
            
        elif choice == 3:
            # Opción 3: Cancelar
            print("\n❌ Operación cancelada por el usuario")
            return None
            
        else:
            print("❌ Opción inválida. Por favor, seleccione 1, 2 o 3.")

def main():
    """Main function"""
    print("🔐 SeQRNG-CTM Integration v1.4")
    print("=" * 40)
    
    # Load configuration
    try:
        config = get_config()
        config.validate_config()
        print("✅ Configuration loaded successfully")
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        print("Please run 'python config.py --setup' to configure the application")
        sys.exit(1)
    
    # Extract configuration values
    seqrng_config = config.get_seqrng_config()
    ctm_config = config.get_ctm_config()
    
    sq_base_url = seqrng_config['ip_address']
    sq_api_token = seqrng_config['api_token']
    sq_ssl_verify = seqrng_config['ssl_verify']
    
    ctm_base_url = ctm_config['ip_address']
    ctm_username = ctm_config['username']
    ctm_password = ctm_config['password']
    ctm_domain = ctm_config['domain']
    ctm_ssl_verify = ctm_config['ssl_verify']
    
    # Get CTM API key
    print(f"\n🔐 Authenticating with CTM...")
    try:
        login_data = {
            'username': ctm_username,
            'password': ctm_password,
            'domain': ctm_domain
        }
        ctm_api_key = ctm_get_api_key(ctm_base_url, login_data, ctm_ssl_verify)
        print("✅ CTM authentication successful")
    except Exception as e:
        print(f"❌ CTM authentication failed: {e}")
        sys.exit(1)
    
    # Operation selection
    operation = get_menu_choice([
        (1, "Create single key"),
        (2, "Create multiple keys")
    ], title="🔧 Operation Selection:")
    
    if operation == 1:
        # Single key generation
        print("\n🔑 Single Key Generation")
        print("-" * 30)
        
        # Get key name with improved validation
        while True:
            keyname = input("Enter the key name: ").strip()
            if not is_valid_key_name(keyname):
                continue
            
            # Check if key already exists
            print(f"\n🔍 Checking if key '{keyname}' exists in CTM...")
            try:
                if ctm_key_exists(ctm_base_url, ctm_api_key, keyname, ctm_ssl_verify):
                    # Handle existing key
                    new_name = handle_existing_key(keyname)
                    if new_name is None:
                        print("\n🔄 Volviendo al menú principal...")
                        print("   Ejecute: python sq-ctm-manager.py")
                        sys.exit(0)
                    else:
                        keyname = new_name
                        break
                else:
                    break  # Key doesn't exist, proceed
            except Exception as e:
                print(f"⚠️ Error checking key existence: {e}")
                break  # Continue anyway if we can't check
        
        # Algorithm key size options based on CTM requirements
        algorithm_sizes = {
            'aes': [16, 24, 32],
            'aria': [16, 24, 32],
            'hmac-sha1': [16, 24, 32],
            'hmac-sha256': [16, 24, 32, 64],
            'hmac-sha384': [24, 36, 48],
            'hmac-sha512': [32, 48, 64],
        }
        
        # Get algorithm
        print("\n🔧 Algorithm Selection:")
        algorithms = ['hmac-sha1', 'hmac-sha256', 'hmac-sha384', 'hmac-sha512', 'aes', 'aria']
        
        algo_options = [(i+1, algo.upper()) for i, algo in enumerate(algorithms)]
        algo_choice = get_menu_choice(algo_options)
        algo = algorithms[algo_choice - 1]
        
        # Get key size for selected algorithm
        available_sizes = algorithm_sizes[algo]
        print(f"\n📏 Key Size Selection for {algo.upper()}:")
        
        size_options = [(i+1, f"{size} bytes") for i, size in enumerate(available_sizes)]
        size_choice = get_menu_choice(size_options)
        numbytes = available_sizes[size_choice - 1]
        
        print(f"📏 Selected key size: {numbytes} bytes")
        
        # Get owner
        owner = input("Enter owner (default: admin): ").strip() or "admin"
        
        # Always set exportable to true
        exportable = True
        
        # Generate quantum random bytes
        print(f"\n🌊 Generating {numbytes} bytes from SeQRNG...")
        try:
            qrng_key, errstr, etystr, etystatus = sq_get_random_bytes(numbytes, 1, sq_base_url, sq_api_token, sq_ssl_verify)
            
            # Validate key length
            if not check_key_length(qrng_key, numbytes):
                print("❌ Key length validation failed")
                sys.exit(1)
            

            
        except Exception as e:
            print(f"❌ Error generating quantum random bytes: {e}")
            sys.exit(1)
        
        # Key name validation already completed in the loop above
        
        # Upload key to CTM
        print(f"\n📤 Uploading key '{keyname}' to CTM...")
        try:
            ctm_upload_key(ctm_base_url, ctm_api_key, keyname, qrng_key, algo, owner, exportable, ctm_ssl_verify)
        except Exception as e:
            print(f"❌ Error uploading key to CTM: {e}")
            sys.exit(1)
        

        
    elif operation == 2:
        # Multiple key generation
        print("\n🔑 Multiple Key Generation")
        print("-" * 30)
        
        # Get number of keys
        num_keys = get_integer_input(1, 100, "Enter number of keys to generate")
        
        # Get key base name
        while True:
            key_base = input("Enter the key base name: ").strip()
            if is_valid_key_name(key_base):
                break
        
        # Get naming strategy
        print("\n📝 Naming Strategy:")
        
        naming_options = [
            (1, "Random names"),
            (2, "Timestamped names"),
            (3, "Sequential numerical names")
        ]
        naming_choice = get_menu_choice(naming_options, default=1)
        
        naming_methods = {
            1: 'secure',
            2: 'timestamped',
            3: 'sequential'
        }
        
        naming_method = naming_methods.get(naming_choice, 'secure')
        print(f"📝 Using naming method: {naming_method}")
        
        # Get start value for sequential naming
        start_value = 1
        if naming_method == 'sequential':
            print(f"\n📊 Range Configuration for sequential naming:")
            start_value = get_integer_input(1, 999999, "Enter start value")
        
        # Algorithm key size options based on CTM requirements
        algorithm_sizes = {
            'aes': [16, 24, 32],
            'aria': [16, 24, 32],
            'hmac-sha1': [16, 24, 32],
            'hmac-sha256': [16, 24, 32, 64],
            'hmac-sha384': [24, 36, 48],
            'hmac-sha512': [32, 48, 64],
        }
        
        # Get algorithm
        print("\n🔧 Algorithm Selection:")
        algorithms = ['hmac-sha1', 'hmac-sha256', 'hmac-sha384', 'hmac-sha512', 'aes', 'aria']
        
        algo_options = [(i+1, algo.upper()) for i, algo in enumerate(algorithms)]
        algo_choice = get_menu_choice(algo_options)
        algo = algorithms[algo_choice - 1]
        
        # Get key size for selected algorithm
        available_sizes = algorithm_sizes[algo]
        print(f"\n📏 Key Size Selection for {algo.upper()}:")
        
        size_options = [(i+1, f"{size} bytes") for i, size in enumerate(available_sizes)]
        size_choice = get_menu_choice(size_options)
        numbytes = available_sizes[size_choice - 1]
        
        print(f"📏 Selected key size: {numbytes} bytes")
        
        # Get owner
        owner = input("Enter owner (default: admin): ").strip() or "admin"
        
        # Always set exportable to true
        exportable = True
        
        # Generate quantum random bytes for all keys
        print(f"\n🌊 Generating {numbytes * num_keys} bytes from SeQRNG...")
        try:
            total_bytes_needed = numbytes * num_keys
            qrng_data, errstr, etystr, etystatus = sq_get_random_bytes(total_bytes_needed, 1, sq_base_url, sq_api_token, sq_ssl_verify)
            
            # Validate total data length
            if len(qrng_data) != total_bytes_needed:
                print(f"❌ Expected {total_bytes_needed} bytes, got {len(qrng_data)}")
                sys.exit(1)
            
            # Split data into individual keys
            keys = group_bytes(qrng_data, numbytes, num_keys)
            
        except Exception as e:
            print(f"❌ Error generating quantum random bytes: {e}")
            sys.exit(1)
        
        # Generate secure key names
        print(f"\n📝 Generating secure key names...")
        key_names = []
        
        try:
            key_generator = SecureKeyNameGenerator()
            key_names = key_generator.generate_batch_names(key_base, num_keys, naming_method, start_sequence_from=start_value)
            
            # Validate security of generated names
            if key_names:
                analysis = key_generator.analyze_name_pattern(key_names)
                if analysis.get('security_issues'):
                    print(f"⚠️ Security issues detected: {', '.join(analysis['security_issues'])}")
                else:
                    print("✅ All key names pass security validation")
            
        except ValueError as e:
            print(f"❌ Error generating secure names: {e}")
            # Fallback to simple names
            key_names = [f"{key_base}_{i+1:06d}" for i in range(num_keys)]
            print("⚠️ Using fallback naming")
        
        # Upload keys to CTM
        print(f"\n📤 Uploading {num_keys} keys to CTM...")
        successful_uploads = 0
        failed_uploads = 0
        
        for i, (key_name, key_material) in enumerate(zip(key_names, keys), 1):
            print(f"\n📤 Uploading key {i}/{num_keys}: {key_name}")
            
            try:
                # Check if key already exists
                if ctm_key_exists(ctm_base_url, ctm_api_key, key_name, ctm_ssl_verify):
                    print(f"⚠️ Key '{key_name}' already exists, skipping...")
                    failed_uploads += 1
                    continue
                
                # Validate key length
                if not check_key_length(key_material, numbytes):
                    print(f"❌ Key length validation failed for {key_name}")
                    failed_uploads += 1
                    continue
                
                # Upload key
                ctm_upload_key(ctm_base_url, ctm_api_key, key_name, key_material, algo, owner, exportable, ctm_ssl_verify)
                successful_uploads += 1
                
            except Exception as e:
                print(f"❌ Error uploading key '{key_name}': {e}")
                failed_uploads += 1
        
        print(f"\n📊 Upload Summary:")
        print(f"   Successful: {successful_uploads}")
        print(f"   Failed: {failed_uploads}")
        print(f"   Total: {num_keys}")
        
        if successful_uploads == 0:
            print(f"\n❌ No keys were uploaded successfully.")
            sys.exit(1)

def get_menu_choice(options, title=None, default=None):
    """
    Muestra un menú de opciones y valida la selección del usuario.
    
    Args:
        options (list of tuples): Lista de (número, texto) para cada opción.
        title (str, optional): Título a mostrar sobre el menú.
        default (int, optional): Opción por defecto si el usuario no ingresa nada.
        
    Returns:
        int: La opción seleccionada por el usuario.
    """
    if title:
        print(title)
        
    valid_choices = [opt[0] for opt in options]
    for num, text in options:
        print(f"   [{num}] {text}")
        
    prompt = f"Select an option ({min(valid_choices)}-{max(valid_choices)}): "
    if default:
        prompt = f"Select an option ({min(valid_choices)}-{max(valid_choices)}, default: {default}): "

    while True:
        user_input = input(prompt).strip()
        
        if not user_input and default is not None:
            return default
            
        try:
            choice = int(user_input)
            if choice in valid_choices:
                return choice
            else:
                print(f"❌ Invalid choice. Please select a number between {min(valid_choices)} and {max(valid_choices)}.")
        except ValueError:
            print("❌ Invalid input. Please enter a number.")

def get_integer_input(min_val, max_val, prompt_text=None):
    """Get integer input with validation"""
    if prompt_text is None:
        prompt = f"Enter a number between {min_val} and {max_val}: "
    else:
        prompt = f"{prompt_text} (between {min_val} and {max_val}): "    
    while True:
        try:
            user_input = int(input(prompt))
            if min_val <= user_input <= max_val:
                return user_input
            else:
                print(f"Please enter a number between {min_val} and {max_val}.")
        except ValueError:
            print("Invalid input. Please enter a valid integer.")

def handle_qrng_failure():
    """Handle QRNG failure with troubleshooting steps"""
    print("\n❌ QRNG Failure - Troubleshooting Steps:")
    print("1. Check SeQRNG service status")
    print("2. Verify network connectivity to SeQRNG")
    print("3. Check API token validity")
    print("4. Verify SSL certificate configuration")
    print("5. Check SeQRNG logs for errors")
    print("6. Ensure sufficient entropy is available")
    print("7. Contact SeQRNG administrator if issues persist")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ Operation cancelled by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        handle_qrng_failure()
        sys.exit(1)