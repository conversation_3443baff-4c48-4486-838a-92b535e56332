#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Entropy Quality Validator
========================

Validación de calidad de entropía para material cuántico y pseudo-aleatorio
"""

import math
import statistics
from collections import Counter
from typing import Dict, Any, Optional

class EntropyValidator:
    """Validador de calidad de entropía"""
    
    def __init__(self):
        self.min_entropy_threshold = 7.0  # Mínimo 7 bits por byte
        self.min_chi_square_threshold = 0.01  # Umbral para test chi-cuadrado
        self.min_entropy_ratio = 0.7  # Ratio mínimo de entropía
        self.min_coverage_ratio = 0.5  # Ratio mínimo de cobertura de bytes
    
    def calculate_shannon_entropy(self, data: bytes) -> float:
        """
        Calcular entropía de Shannon
        
        Args:
            data: Datos a analizar
            
        Returns:
            float: Entropía en bits
        """
        if not data:
            return 0.0
        
        # Contar frecuencias
        byte_counts = Counter(data)
        total_bytes = len(data)
        
        # Calcular entropía
        entropy = 0.0
        for count in byte_counts.values():
            probability = count / total_bytes
            if probability > 0:
                entropy -= probability * math.log2(probability)
        
        return entropy
    
    def calculate_min_entropy(self, data: bytes) -> float:
        """
        Calcular entropía mínima (más conservadora)
        
        Args:
            data: Datos a analizar
            
        Returns:
            float: Entropía mínima en bits
        """
        if not data:
            return 0.0
        
        byte_counts = Counter(data)
        total_bytes = len(data)
        
        # Encontrar la probabilidad máxima
        max_probability = max(count / total_bytes for count in byte_counts.values())
        
        # Entropía mínima = -log2(max_probability)
        return -math.log2(max_probability)
    
    def perform_chi_square_test(self, data: bytes) -> dict:
        """
        Realizar test chi-cuadrado para uniformidad
        
        Args:
            data: Datos a analizar
            
        Returns:
            dict: Resultado del test
        """
        if len(data) < 256:
            return {
                'p_value': 0.0, 
                'is_uniform': False, 
                'error': 'Insufficient data for chi-square test'
            }
        
        # Contar frecuencias de bytes
        byte_counts = [0] * 256
        for byte in data:
            byte_counts[byte] += 1
        
        expected_frequency = len(data) / 256
        chi_square = sum((observed - expected_frequency) ** 2 / expected_frequency 
                        for observed in byte_counts)
        
        # p-value aproximado (para n=255 grados de libertad)
        # Usar aproximación normal para chi-cuadrado
        p_value = 1 - self._chi_square_cdf(chi_square, 255)
        
        return {
            'chi_square': chi_square,
            'p_value': p_value,
            'is_uniform': p_value > self.min_chi_square_threshold,
            'expected_frequency': expected_frequency,
            'degrees_of_freedom': 255
        }
    
    def _chi_square_cdf(self, x: float, df: int) -> float:
        """Aproximación de CDF chi-cuadrado"""
        # Aproximación simple para df > 30
        if df > 30:
            z = (x - df) / math.sqrt(2 * df)
            return 0.5 * (1 + math.erf(z / math.sqrt(2)))
        return 0.5  # Aproximación conservadora
    
    def analyze_byte_distribution(self, data: bytes) -> dict:
        """
        Analizar distribución de bytes
        
        Args:
            data: Datos a analizar
            
        Returns:
            dict: Análisis de distribución
        """
        if not data:
            return {'error': 'No data provided'}
        
        byte_counts = Counter(data)
        total_bytes = len(data)
        
        # Calcular estadísticas
        frequencies = [count / total_bytes for count in byte_counts.values()]
        
        return {
            'unique_bytes': len(byte_counts),
            'total_bytes': total_bytes,
            'mean_frequency': statistics.mean(frequencies),
            'std_frequency': statistics.stdev(frequencies) if len(frequencies) > 1 else 0,
            'min_frequency': min(frequencies),
            'max_frequency': max(frequencies),
            'coverage_ratio': len(byte_counts) / 256,  # Ratio de bytes únicos
            'most_common_byte': byte_counts.most_common(1)[0] if byte_counts else None,
            'least_common_byte': min(byte_counts.items(), key=lambda x: x[1]) if byte_counts else None
        }
    
    def perform_runs_test(self, data: bytes) -> dict:
        """
        Realizar test de rachas (runs test) para detectar patrones
        
        Args:
            data: Datos a analizar
            
        Returns:
            dict: Resultado del test
        """
        if len(data) < 100:
            return {
                'is_random': False,
                'error': 'Insufficient data for runs test'
            }
        
        # Convertir a secuencia de bits
        bits = []
        for byte in data:
            bits.extend([(byte >> i) & 1 for i in range(8)])
        
        # Contar rachas (cambios de 0 a 1 o viceversa)
        runs = 1
        for i in range(1, len(bits)):
            if bits[i] != bits[i-1]:
                runs += 1
        
        # Estadística esperada para datos aleatorios
        n = len(bits)
        expected_runs = (2 * sum(bits) * (n - sum(bits))) / n + 1
        variance = (2 * sum(bits) * (n - sum(bits)) * (2 * sum(bits) * (n - sum(bits)) - n)) / (n * n * (n - 1))
        
        if variance > 0:
            z_score = (runs - expected_runs) / math.sqrt(variance)
            # p-value aproximado (test de dos colas)
            p_value = 2 * (1 - self._normal_cdf(abs(z_score)))
        else:
            p_value = 1.0
        
        return {
            'runs': runs,
            'expected_runs': expected_runs,
            'z_score': z_score if variance > 0 else 0,
            'p_value': p_value,
            'is_random': p_value > 0.05
        }
    
    def _normal_cdf(self, x: float) -> float:
        """Función de distribución acumulativa normal"""
        return 0.5 * (1 + math.erf(x / math.sqrt(2)))
    
    def validate_entropy_quality(self, data: bytes, source: str = "unknown") -> dict:
        """
        Validación completa de calidad de entropía
        
        Args:
            data: Datos a validar
            source: Fuente de los datos (QRNG, PRNG, etc.)
            
        Returns:
            dict: Resultado de validación
        """
        if not data:
            return {
                'is_acceptable': False,
                'issues': ['No data provided'],
                'source': source,
                'quality_score': 0
            }
        
        result = {
            'is_acceptable': True,
            'issues': [],
            'warnings': [],
            'source': source,
            'data_length': len(data)
        }
        
        # Calcular entropías
        shannon_entropy = self.calculate_shannon_entropy(data)
        min_entropy = self.calculate_min_entropy(data)
        
        result['shannon_entropy'] = shannon_entropy
        result['min_entropy'] = min_entropy
        
        # Validar entropía mínima
        if min_entropy < self.min_entropy_threshold:
            result['is_acceptable'] = False
            result['issues'].append(f'Min entropy too low: {min_entropy:.2f} < {self.min_entropy_threshold}')
        
        # Test chi-cuadrado
        chi_square_result = self.perform_chi_square_test(data)
        result['chi_square'] = chi_square_result
        
        if not chi_square_result.get('is_uniform', False):
            result['issues'].append('Chi-square test failed (non-uniform distribution)')
            if source == "PRNG":
                result['is_acceptable'] = False
            else:
                result['warnings'].append('Distribution may not be uniform')
        
        # Test de rachas
        runs_result = self.perform_runs_test(data)
        result['runs_test'] = runs_result
        
        if not runs_result.get('is_random', False):
            result['warnings'].append('Runs test suggests non-random patterns')
        
        # Análisis de distribución
        distribution = self.analyze_byte_distribution(data)
        result['distribution'] = distribution
        
        # Validar cobertura de bytes
        if distribution['coverage_ratio'] < self.min_coverage_ratio:
            result['issues'].append(f'Low byte coverage: {distribution["coverage_ratio"]:.2%}')
            if source == "PRNG":
                result['is_acceptable'] = False
            else:
                result['warnings'].append('Limited byte coverage detected')
        
        # Validar desviación estándar
        if distribution['std_frequency'] < 0.001:
            result['warnings'].append('Very uniform distribution (may indicate bias)')
        
        # Calcular score de calidad
        quality_score = self._calculate_quality_score(result)
        result['quality_score'] = quality_score
        
        # Determinar nivel de aceptabilidad
        if quality_score >= 90:
            result['quality_level'] = 'Excellent'
        elif quality_score >= 80:
            result['quality_level'] = 'Good'
        elif quality_score >= 70:
            result['quality_level'] = 'Acceptable'
        elif quality_score >= 60:
            result['quality_level'] = 'Poor'
        else:
            result['quality_level'] = 'Unacceptable'
        
        return result
    
    def _calculate_quality_score(self, validation_result: dict) -> float:
        """Calcular score de calidad (0-100)"""
        score = 100.0
        
        # Penalizar por entropía baja
        min_entropy = validation_result.get('min_entropy', 0)
        if min_entropy < 8.0:
            score -= (8.0 - min_entropy) * 10
        
        # Penalizar por test chi-cuadrado fallido
        if not validation_result.get('chi_square', {}).get('is_uniform', True):
            score -= 20
        
        # Penalizar por test de rachas fallido
        if not validation_result.get('runs_test', {}).get('is_random', True):
            score -= 15
        
        # Penalizar por cobertura baja
        coverage = validation_result.get('distribution', {}).get('coverage_ratio', 0)
        if coverage < 0.8:
            score -= (0.8 - coverage) * 50
        
        # Penalizar por desviación estándar muy baja
        std_freq = validation_result.get('distribution', {}).get('std_frequency', 0)
        if std_freq < 0.001:
            score -= 10
        
        return max(0.0, score)
    
    def log_entropy_analysis(self, validation_result: dict, context: str = ""):
        """Log análisis de entropía para debugging"""
        print(f"🌊 Entropy Analysis {context}:")
        print(f"   Source: {validation_result.get('source', 'Unknown')}")
        print(f"   Data length: {validation_result.get('data_length', 0)} bytes")
        print(f"   Shannon entropy: {validation_result.get('shannon_entropy', 0):.2f} bits")
        print(f"   Min entropy: {validation_result.get('min_entropy', 0):.2f} bits")
        print(f"   Quality score: {validation_result.get('quality_score', 0):.1f}/100")
        print(f"   Quality level: {validation_result.get('quality_level', 'Unknown')}")
        
        # Información de distribución
        distribution = validation_result.get('distribution', {})
        if 'coverage_ratio' in distribution:
            print(f"   Byte coverage: {distribution['coverage_ratio']:.2%}")
        
        # Issues y warnings
        issues = validation_result.get('issues', [])
        warnings = validation_result.get('warnings', [])
        
        if issues:
            print(f"   ❌ Issues: {', '.join(issues)}")
        
        if warnings:
            print(f"   ⚠️ Warnings: {', '.join(warnings)}")
        
        if not issues and not warnings:
            print(f"   ✅ No issues detected")
    
    def get_recommendations(self, validation_result: dict) -> list:
        """Obtener recomendaciones basadas en el análisis"""
        recommendations = []
        
        min_entropy = validation_result.get('min_entropy', 0)
        if min_entropy < 7.0:
            recommendations.append("Consider using a different entropy source")
        
        if not validation_result.get('chi_square', {}).get('is_uniform', True):
            recommendations.append("Check entropy source for bias")
        
        coverage = validation_result.get('distribution', {}).get('coverage_ratio', 0)
        if coverage < 0.5:
            recommendations.append("Entropy source may have limited range")
        
        quality_score = validation_result.get('quality_score', 0)
        if quality_score < 70:
            recommendations.append("Consider additional entropy validation")
        
        return recommendations 