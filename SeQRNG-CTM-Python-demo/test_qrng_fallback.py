#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Script para Demostrar Mejoras en Fallback PRNG
==================================================

Este script demuestra las mejoras implementadas:
1. Confirmación del usuario antes de usar PRNG
2. Timeout reducido a 10 segundos
3. Mensajes de error mejorados
4. Eliminación del texto "Critical PRNG quality issue"
"""

import sys
import os

# Agregar el directorio actual al path para importar módulos
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_qrng_fallback():
    """Probar el fallback al PRNG con confirmación del usuario"""
    
    print("🧪 Test de Mejoras en Fallback PRNG")
    print("=" * 50)
    
    try:
        from interface_seqrng_v2 import sq_get_random_bytes
        
        print("✅ Módulo importado correctamente")
        print("\n📋 Mejoras implementadas:")
        print("   1. ✅ Confirmación del usuario antes de usar PRNG")
        print("   2. ✅ Timeout reducido a 10 segundos")
        print("   3. ✅ Mensajes de error mejorados")
        print("   4. ✅ Eliminación del texto 'Critical PRNG quality issue'")
        
        print("\n🔧 Para probar el fallback:")
        print("   - Usar una IP inválida o no accesible")
        print("   - El timeout se activará en 10 segundos")
        print("   - Se mostrará el diálogo de confirmación")
        
        # Simular una IP inválida para probar el fallback
        test_ip = "192.168.999.999"  # IP inválida
        test_token = "test_token"
        
        print(f"\n🌊 Probando conexión a IP inválida: {test_ip}")
        print("   (Esto debería fallar y mostrar el diálogo de confirmación)")
        
        # Esta llamada debería fallar y mostrar el diálogo
        result = sq_get_random_bytes(32, 1, test_ip, test_token, False)
        
        print(f"\n✅ Resultado: {len(result[0])} bytes generados")
        
    except ImportError as e:
        print(f"❌ Error importando módulo: {e}")
        return False
    except Exception as e:
        print(f"❌ Error inesperado: {e}")
        return False
    
    return True

def test_confirm_prng_fallback():
    """Probar solo la función de confirmación"""
    
    print("\n🧪 Test de Función de Confirmación")
    print("=" * 40)
    
    try:
        from interface_seqrng_v2 import confirm_prng_fallback
        
        print("✅ Función de confirmación disponible")
        print("\n📝 La función muestra:")
        print("   • Mensaje claro de fallo de conexión")
        print("   • Advertencia sobre seguridad del PRNG")
        print("   • Guía de troubleshooting")
        print("   • Solicitud de confirmación con 'PRNG'")
        
        print("\n🔧 Para probar manualmente:")
        print("   - Ejecutar: python SeQRNG_CTM_1.4.py")
        print("   - Usar una IP inválida")
        print("   - Ver el diálogo de confirmación")
        
        return True
        
    except ImportError as e:
        print(f"❌ Error importando función: {e}")
        return False

def main():
    """Función principal de pruebas"""
    
    print("🚀 Test de Mejoras en Fallback PRNG")
    print("=" * 50)
    
    # Test 1: Verificar que el módulo se importa correctamente
    test1_result = test_confirm_prng_fallback()
    
    # Test 2: Probar el fallback (comentado para evitar interacción automática)
    print("\n⚠️  Test de fallback comentado para evitar interacción automática")
    print("   Para probar manualmente, ejecute: python SeQRNG_CTM_1.4.py")
    
    # test2_result = test_qrng_fallback()
    
    print("\n📊 Resumen de Mejoras:")
    print("   ✅ Función de confirmación implementada")
    print("   ✅ Timeout reducido a 10 segundos")
    print("   ✅ Mensajes de error mejorados")
    print("   ✅ Texto 'Critical PRNG' eliminado")
    print("   ✅ Guía de troubleshooting incluida")
    
    print("\n🎯 Para probar las mejoras:")
    print("   1. Ejecutar: python SeQRNG_CTM_1.4.py")
    print("   2. Seleccionar 'Create new key'")
    print("   3. Usar una IP inválida (ej: 192.168.999.999)")
    print("   4. Esperar el timeout de 10 segundos")
    print("   5. Ver el diálogo de confirmación")
    print("   6. Escribir 'PRNG' para continuar o cualquier otra cosa para cancelar")
    
    return test1_result

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 