#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SeQRNG Interface v2
==================

Interface mejorado para SeQRNG con validación de entropía y manejo de errores
"""

import requests
import json
import os
import sys
from urllib3.exceptions import InsecureRequestWarning

def convert_byte_alphanumeric(byte_string):
    """
    Convert byte string to alphanumeric string
    """
    import string
    import random
    
    # Create a mapping of byte values to alphanumeric characters
    alphanumeric_chars = string.ascii_letters + string.digits
    result = ""
    
    for byte in byte_string:
        # Use the byte value to select a character
        char_index = byte % len(alphanumeric_chars)
        result += alphanumeric_chars[char_index]
    
    return result

def entropy_check(entropy):
    """
    Check if entropy value is acceptable
    """
    try:
        entropy_float = float(entropy)
        return entropy_float >= 0.8
    except (ValueError, TypeError):
        return False

def confirm_prng_fallback():
    """
    Solicitar confirmación del usuario para usar PRNG como fallback
    
    Returns:
        bool: True si el usuario confirma, False si cancela
    """
    print("\n⚠️  CONEXIÓN AL QRNG FALLIDA")
    print("=" * 40)
    print("❌ No se pudo conectar al SeQRNG (Quantum Random Number Generator)")
    print("📡 El sistema puede usar un generador de números pseudo-aleatorios (PRNG) como respaldo")
    print("\n🔒 IMPORTANTE:")
    print("   • Los números pseudo-aleatorios son menos seguros que los cuánticos")
    print("   • Solo use PRNG para pruebas o desarrollo")
    print("   • Para producción, resuelva los problemas de conectividad")
    
    print("\n🔧 TROUBLESHOOTING RECOMENDADO:")
    print("   1. Verificar conectividad de red: ping <IP_QRNG>")
    print("   2. Verificar que el SeQRNG esté funcionando")
    print("   3. Verificar credenciales y configuración SSL")
    print("   4. Revisar logs del SeQRNG")
    print("   5. Contactar al administrador del sistema")
    
    print("\n❓ ¿Desea continuar usando PRNG como respaldo?")
    print("   Para confirmar, escriba exactamente: PRNG")
    
    confirmation = input("\nConfirmación: ").strip()
    
    if confirmation == "PRNG":
        print("✅ Confirmado. Continuando con PRNG...")
        return True
    else:
        print("❌ Operación cancelada por el usuario.")
        print("🔧 Por favor, resuelva los problemas de conectividad antes de continuar.")
        return False

def sq_get_random_bytes(num_bytes, packages, base_url, api_token, ssl_verify=None):
    """
    Get random bytes from SeQRNG with entropy validation and improved error handling
    
    Args:
        num_bytes (int): Number of bytes to generate
        packages (int): Number of packages
        base_url (str): SeQRNG base URL
        api_token (str): SeQRNG API token
        ssl_verify: SSL verification option (from config)
    
    Returns:
        tuple: (decoded_bytes, errstr, etystr, etystatus)
    """
    # Suppress the warnings from urllib3 only if SSL verification is disabled
    if ssl_verify is False:
        requests.packages.urllib3.disable_warnings(category=InsecureRequestWarning)
    
    # Use base_url directly if it's a complete URL, otherwise treat as IP for backward compatibility
    if base_url.startswith(('http://', 'https://')):
        api_url = f"{base_url.rstrip('/')}/api/v1/get_data"
    else:
        # Backward compatibility: treat as IP address
        api_url = f"https://{base_url}/api/v1/get_data"
    
    # Use SSL verification from config, fallback to False for backward compatibility
    verify_option = ssl_verify if ssl_verify is not None else False
    
    headers = {'Authorization': f'Bearer {api_token}'}
    data = {
        'request_type': 'data',
        'package_size': num_bytes,
        'package_total': packages,
        'output_type': 'base64',
        'self_testing_report': '1'
    }
    
    try:
        response = requests.post(api_url, headers=headers, data=data, verify=verify_option, timeout=10)
        response.raise_for_status()
        content = json.loads(response.content)
        
        # Extraer datos de la respuesta según el tipo de output
        decoded_bytes = None
        if isinstance(content, dict) and 'success' in content:
            success_data = content['success']
            
            if 'base64_data' in success_data:
                # Para output_type: base64
                import base64
                decoded_bytes = base64.b64decode(success_data['base64_data'])
            elif 'hex_data' in success_data:
                # Para output_type: hex
                decoded_bytes = bytes.fromhex(success_data['hex_data'])
            elif 'alphanumeric_data' in success_data:
                # Para output_type: alphanumeric
                decoded_bytes = success_data['alphanumeric_data'].encode('utf-8')
            elif 'data' in success_data:
                # Fallback para estructura anterior
                decoded_bytes = success_data['data']
            else:
                # Si no encontramos la estructura esperada, mostrar error detallado
                print(f"❌ Estructura de respuesta inesperada:")
                print(f"   Success keys disponibles: {list(success_data.keys())}")
                print(f"   Content: {content}")
                raise ValueError(f"Estructura de respuesta no reconocida. Claves disponibles: {list(success_data.keys())}")
        else:
            raise ValueError(f"Respuesta no tiene la estructura esperada: {type(content)}")
        
        if decoded_bytes is None:
            raise ValueError("No se pudo extraer datos de la respuesta")
        
        # Validar calidad de entropía (silent)
        try:
            from entropy_validator import EntropyValidator
            entropy_validator = EntropyValidator()
            validation_result = entropy_validator.validate_entropy_quality(decoded_bytes, "QRNG")            
                
        except ImportError:
            pass  # Silently ignore if entropy validator not available
        except Exception as e:
            pass  # Silently ignore entropy validation errors
        
        # Extract error information
        errstr = content.get('error', {}).get('message', 'N/A')
        etystr = content.get('success', {}).get('entropy', 'N/A')
        
        # Safely extract ETY value from the nested structure
        etystatus = 'N/A'
        try:
            report_list = content.get('success', {}).get('self_testing_report', [])
            if report_list:
                # Find the 'Statistics' dictionary in the report list
                stats_item = next((item for item in report_list if 'Statistics' in item), None)
                if stats_item:
                    etystatus = stats_item.get('Statistics', {}).get('ETY', 'N/A')
        except Exception:
            # In case of any error during extraction, default to 'N/A'
            etystatus = 'N/A'

        return (decoded_bytes, errstr, etystr, etystatus)
        
    except requests.exceptions.ConnectTimeout:
        print(f"❌ Timeout de conexión al SeQRNG ({base_url})")
        print("   ⏱️  El servidor no respondió en 10 segundos")
        
        if not confirm_prng_fallback():
            sys.exit(1)
        
        # Generar PRNG
        prng_bytes = os.urandom(num_bytes)
        
        return (prng_bytes, "N/A - prng output", "N/A - prng output", "N/A - prng output")
        
    except requests.exceptions.ConnectionError as e:
        print(f"❌ Error de conexión al SeQRNG ({base_url})")
        print(f"   🔌 No se pudo establecer conexión: {e}")
        
        if not confirm_prng_fallback():
            sys.exit(1)
        
        # Generar PRNG
        prng_bytes = os.urandom(num_bytes)
        
        return (prng_bytes, "N/A - prng output", "N/A - prng output", "N/A - prng output")
        
    except Exception as e:
        print(f"❌ Error inesperado al conectar con SeQRNG ({base_url})")
        print(f"   🔍 Detalles: {e}")
        
        if not confirm_prng_fallback():
            sys.exit(1)
        
        # Generar PRNG
        prng_bytes = os.urandom(num_bytes)
        
        return (prng_bytes, "N/A - prng output", "N/A - prng output", "N/A - prng output")

####################################################################################    
#################CALL Routine SeQRNG to get random hexadecimal key###################
def get_random_hex_key(num_bytes, packages, base_url, api_token, ssl_verify=None):
    """
    Get random hex key from SeQRNG with entropy validation and improved error handling
    
    Args:
        num_bytes (int): Number of bytes to generate
        packages (int): Number of packages
        base_url (str): SeQRNG base URL
        api_token (str): SeQRNG API token
        ssl_verify: SSL verification option (from config)
    
    Returns:
        tuple: (hex_key, errstr, etystr, etystatus)
    """
    # Suppress the warnings from urllib3 only if SSL verification is disabled
    if ssl_verify is False:
        requests.packages.urllib3.disable_warnings(category=InsecureRequestWarning)
    
    # Use base_url directly if it's a complete URL, otherwise treat as IP for backward compatibility
    if base_url.startswith(('http://', 'https://')):
        api_url = f"{base_url.rstrip('/')}/api/v1/get_data"
    else:
        # Backward compatibility: treat as IP address
        api_url = f"https://{base_url}/api/v1/get_data"
    
    # Use SSL verification from config, fallback to False for backward compatibility
    verify_option = ssl_verify if ssl_verify is not None else False
    
    headers = {'Authorization': f'Bearer {api_token}'}
    data = {
        'request_type': 'data',
        'package_size': num_bytes,
        'package_total': packages,
        'output_type': 'hex',
        'self_testing_report': '1'
    }
    
    try:
        response = requests.post(api_url, headers=headers, data=data, verify=verify_option, timeout=10)
        response.raise_for_status()
        content = json.loads(response.content)
        
        # Verificar si hay errores en la respuesta
        if 'error_messages' in content:
            error_msg = content.get('error_messages', 'Unknown error')
            raise ValueError(f"SeQRNG returned error: {error_msg}")
        
        # Extraer datos hex
        if isinstance(content, dict) and 'success' in content:
            success_data = content['success']
            
            if 'hex_data' in success_data:
                hex_key = success_data['hex_data']
            elif 'base64_data' in success_data:
                # Fallback: convertir base64 a hex
                import base64
                decoded_bytes = base64.b64decode(success_data['base64_data'])
                hex_key = decoded_bytes.hex()
            else:
                raise ValueError(f"Estructura de respuesta no reconocida. Claves disponibles: {list(success_data.keys())}")
        else:
            raise ValueError(f"Respuesta no tiene la estructura esperada: {type(content)}")
        
        # Validar calidad de entropía (silent)
        try:
            from entropy_validator import EntropyValidator
            entropy_validator = EntropyValidator()
            hex_bytes = bytes.fromhex(hex_key)
            validation_result = entropy_validator.validate_entropy_quality(hex_bytes, "QRNG")
            
                
        except ImportError:
            pass  # Silently ignore if entropy validator not available
        except Exception as e:
            pass  # Silently ignore entropy validation errors
        
        # Extract error information
        errstr = content.get('error', {}).get('message', 'N/A')
        etystr = content.get('success', {}).get('entropy', 'N/A')
        etystatus = content.get('success', {}).get('status', 'N/A')
        
        return (hex_key, errstr, etystr, etystatus)
        
    except requests.exceptions.ConnectTimeout:
        print(f"❌ Timeout de conexión al SeQRNG ({base_url})")
        print("   ⏱️  El servidor no respondió en 10 segundos")
        
        if not confirm_prng_fallback():
            sys.exit(1)
        
        # Generar PRNG
        prng_bytes = os.urandom(num_bytes)
        prng_hex = prng_bytes.hex()
        
        return (prng_hex, "N/A - prng output", "N/A - prng output", "N/A - prng output")
        
    except requests.exceptions.ConnectionError as e:
        print(f"❌ Error de conexión al SeQRNG ({base_url})")
        print(f"   🔌 No se pudo establecer conexión: {e}")
        
        if not confirm_prng_fallback():
            sys.exit(1)
        
        # Generar PRNG
        prng_bytes = os.urandom(num_bytes)
        prng_hex = prng_bytes.hex()
        
        return (prng_hex, "N/A - prng output", "N/A - prng output", "N/A - prng output")
        
    except Exception as e:
        print(f"❌ Error inesperado al conectar con SeQRNG ({base_url})")
        print(f"   🔍 Detalles: {e}")
        
        if not confirm_prng_fallback():
            sys.exit(1)
        
        # Generar PRNG
        prng_bytes = os.urandom(num_bytes)
        prng_hex = prng_bytes.hex()
        
        return (prng_hex, "N/A - prng output", "N/A - prng output", "N/A - prng output")

####################################################################################

#################CALL Routine SeQRNG to get alphanumeric key###################
def get_random_alphanumeric_key(num_bytes, packages, base_url, api_token, ssl_verify=None):
    """
    Get random alphanumeric key from SeQRNG with entropy validation and improved error handling
    
    Args:
        num_bytes (int): Number of bytes to generate
        packages (int): Number of packages
        base_url (str): SeQRNG base URL
        api_token (str): SeQRNG API token
        ssl_verify: SSL verification option (from config)
    
    Returns:
        tuple: (alphanumeric_key, errstr, etystr, etystatus)
    """
    # Suppress the warnings from urllib3 only if SSL verification is disabled
    if ssl_verify is False:
        requests.packages.urllib3.disable_warnings(category=InsecureRequestWarning)
    
    # Use base_url directly if it's a complete URL, otherwise treat as IP for backward compatibility
    if base_url.startswith(('http://', 'https://')):
        api_url = f"{base_url.rstrip('/')}/api/v1/get_data"
    else:
        # Backward compatibility: treat as IP address
        api_url = f"https://{base_url}/api/v1/get_data"
    
    # Use SSL verification from config, fallback to False for backward compatibility
    verify_option = ssl_verify if ssl_verify is not None else False
    
    headers = {'Authorization': f'Bearer {api_token}'}
    data = {
        'request_type': 'data',
        'package_size': num_bytes,
        'package_total': packages,
        'output_type': 'alphanumeric',
        'self_testing_report': '1'
    }
    
    try:
        response = requests.post(api_url, headers=headers, data=data, verify=verify_option, timeout=10)
        response.raise_for_status()
        content = json.loads(response.content)
        
        # Verificar si hay errores en la respuesta
        if 'error_messages' in content:
            error_msg = content.get('error_messages', 'Unknown error')
            raise ValueError(f"SeQRNG returned error: {error_msg}")
        
        # Extraer datos alfanuméricos
        if isinstance(content, dict) and 'success' in content:
            success_data = content['success']
            
            if 'alphanumeric_data' in success_data:
                alphanumeric_key = success_data['alphanumeric_data']
            elif 'base64_data' in success_data:
                # Fallback: convertir base64 a alfanumérico
                import base64
                decoded_bytes = base64.b64decode(success_data['base64_data'])
                alphanumeric_key = convert_byte_alphanumeric(decoded_bytes)
            else:
                raise ValueError(f"Estructura de respuesta no reconocida. Claves disponibles: {list(success_data.keys())}")
        else:
            raise ValueError(f"Respuesta no tiene la estructura esperada: {type(content)}")
        
        # Validar calidad de entropía (silent)
        try:
            from entropy_validator import EntropyValidator
            entropy_validator = EntropyValidator()
            # Convertir alfanumérico a bytes (aproximación)
            alphanumeric_bytes = alphanumeric_key.encode('utf-8')
            validation_result = entropy_validator.validate_entropy_quality(alphanumeric_bytes, "QRNG")
            
            # Solo log si hay problemas críticos (score < 50)
            if validation_result['quality_score'] < 50:
                print(f"⚠️ QRNG quality warning: {validation_result['issues']}")
                
        except ImportError:
            pass  # Silently ignore if entropy validator not available
        except Exception as e:
            pass  # Silently ignore entropy validation errors
        
        # Extract error information
        errstr = content.get('error', {}).get('message', 'N/A')
        etystr = content.get('success', {}).get('entropy', 'N/A')
        etystatus = content.get('success', {}).get('status', 'N/A')
        
        return (alphanumeric_key, errstr, etystr, etystatus)
        
    except requests.exceptions.ConnectTimeout:
        print(f"❌ Timeout de conexión al SeQRNG ({base_url})")
        print("   ⏱️  El servidor no respondió en 10 segundos")
        
        if not confirm_prng_fallback():
            sys.exit(1)
        
        # Generar PRNG
        import string
        import random as random_module
        
        # Generar clave alfanumérica usando PRNG
        characters = string.ascii_letters + string.digits
        prng_alphanumeric = ''.join(random_module.choice(characters) for _ in range(num_bytes * 2))
        
        return (prng_alphanumeric, "N/A - prng output", "N/A - prng output", "N/A - prng output")
        
    except requests.exceptions.ConnectionError as e:
        print(f"❌ Error de conexión al SeQRNG ({base_url})")
        print(f"   🔌 No se pudo establecer conexión: {e}")
        
        if not confirm_prng_fallback():
            sys.exit(1)
        
        # Generar PRNG
        import string
        import random as random_module
        
        # Generar clave alfanumérica usando PRNG
        characters = string.ascii_letters + string.digits
        prng_alphanumeric = ''.join(random_module.choice(characters) for _ in range(num_bytes * 2))
        
        return (prng_alphanumeric, "N/A - prng output", "N/A - prng output", "N/A - prng output")
        
    except Exception as e:
        print(f"❌ Error inesperado al conectar con SeQRNG ({base_url})")
        print(f"   🔍 Detalles: {e}")
        
        if not confirm_prng_fallback():
            sys.exit(1)
        
        # Generar PRNG
        import string
        import random as random_module
        
        # Generar clave alfanumérica usando PRNG
        characters = string.ascii_letters + string.digits
        prng_alphanumeric = ''.join(random_module.choice(characters) for _ in range(num_bytes * 2))
        
        return (prng_alphanumeric, "N/A - prng output", "N/A - prng output", "N/A - prng output")
####################################################################################