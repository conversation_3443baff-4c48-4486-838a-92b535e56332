#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Security Improvements
=========================

Script para probar las mejoras de seguridad implementadas:
- Validación JWT
- Generación segura de nombres
- Validación de entropía
"""

import os
import sys
import time
import secrets
from pathlib import Path

def test_jwt_validation():
    """Test JWT validation functionality"""
    print("🔑 Testing JWT Validation")
    print("-" * 30)
    
    try:
        from jwt_validator import JWTValidator
        
        # Test with valid JWT token (example)
        test_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjE3NzYyMzkwMjJ9.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
        
        validator = JWTValidator()
        
        # Test token validation
        try:
            payload = validator.validate_jwt_token(test_token)
            print("✅ JWT token validation successful")
            print(f"   Subject: {payload.get('sub')}")
            print(f"   Name: {payload.get('name')}")
            print(f"   Issued at: {payload.get('iat')}")
            print(f"   Expires at: {payload.get('exp')}")
        except ValueError as e:
            print(f"⚠️ JWT validation warning: {e}")
        
        # Test token info
        token_info = validator.get_token_info(test_token)
        print(f"✅ Token info extraction successful")
        print(f"   Expired: {token_info.get('is_expired')}")
        print(f"   Time until expiry: {token_info.get('time_until_expiry')}")
        
        # Test expired token
        expired_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjE1MTYyMzkwMjJ9.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
        
        try:
            validator.validate_jwt_token(expired_token)
            print("❌ Should have detected expired token")
        except ValueError as e:
            print(f"✅ Correctly detected expired token: {e}")
        
        return True
        
    except ImportError:
        print("❌ JWT validation module not available")
        return False
    except Exception as e:
        print(f"❌ JWT validation test failed: {e}")
        return False

def test_secure_key_generation():
    """Test secure key name generation"""
    print("\n🎲 Testing Secure Key Generation")
    print("-" * 30)
    
    try:
        from secure_key_generator import SecureKeyNameGenerator
        
        generator = SecureKeyNameGenerator()
        
        # Test secure name generation
        base_name = "test_key"
        secure_name = generator.generate_secure_name(base_name)
        print(f"✅ Generated secure name: {secure_name}")
        
        # Test security validation
        security_check = generator.validate_name_security(secure_name)
        print(f"✅ Security validation: {security_check['is_secure']}")
        print(f"   Entropy score: {security_check['entropy_score']:.2f}")
        
        # Test different generation methods
        timestamped_name = generator.generate_timestamped_name(base_name)
        uuid_name = generator.generate_uuid_name(base_name)
        sequential_name = generator.generate_sequential_secure_name(base_name, 1)
        
        print(f"✅ Timestamped name: {timestamped_name}")
        print(f"✅ UUID name: {uuid_name}")
        print(f"✅ Sequential name: {sequential_name}")
        
        # Test batch generation
        batch_names = generator.generate_batch_names(base_name, 5, 'secure')
        print(f"✅ Generated {len(batch_names)} batch names")
        
        # Test name pattern analysis
        analysis = generator.analyze_name_pattern(batch_names)
        print(f"✅ Pattern analysis: {analysis['unique_names']}/{analysis['total_names']} unique")
        
        return True
        
    except ImportError:
        print("❌ Secure key generator module not available")
        return False
    except Exception as e:
        print(f"❌ Secure key generation test failed: {e}")
        return False

def test_entropy_validation():
    """Test entropy validation functionality"""
    print("\n🌊 Testing Entropy Validation")
    print("-" * 30)
    
    try:
        from entropy_validator import EntropyValidator
        
        validator = EntropyValidator()
        
        # Test with high-quality random data
        good_data = secrets.token_bytes(1024)
        good_validation = validator.validate_entropy_quality(good_data, "QRNG")
        
        print(f"✅ Good entropy validation:")
        print(f"   Quality score: {good_validation['quality_score']:.1f}/100")
        print(f"   Quality level: {good_validation['quality_level']}")
        print(f"   Shannon entropy: {good_validation['shannon_entropy']:.2f} bits")
        print(f"   Min entropy: {good_validation['min_entropy']:.2f} bits")
        print(f"   Acceptable: {good_validation['is_acceptable']}")
        
        # Test with low-quality data (repeated bytes)
        bad_data = b'\x00' * 512 + b'\xff' * 512
        bad_validation = validator.validate_entropy_quality(bad_data, "PRNG")
        
        print(f"\n⚠️ Bad entropy validation:")
        print(f"   Quality score: {bad_validation['quality_score']:.1f}/100")
        print(f"   Quality level: {bad_validation['quality_level']}")
        print(f"   Acceptable: {bad_validation['is_acceptable']}")
        print(f"   Issues: {', '.join(bad_validation['issues'])}")
        
        # Test distribution analysis
        distribution = validator.analyze_byte_distribution(good_data)
        print(f"\n✅ Distribution analysis:")
        print(f"   Unique bytes: {distribution['unique_bytes']}/256")
        print(f"   Coverage ratio: {distribution['coverage_ratio']:.2%}")
        print(f"   Mean frequency: {distribution['mean_frequency']:.6f}")
        
        # Test chi-square test
        chi_square_result = validator.perform_chi_square_test(good_data)
        print(f"\n✅ Chi-square test:")
        print(f"   Is uniform: {chi_square_result['is_uniform']}")
        print(f"   P-value: {chi_square_result['p_value']:.6f}")
        
        return True
        
    except ImportError:
        print("❌ Entropy validator module not available")
        return False
    except Exception as e:
        print(f"❌ Entropy validation test failed: {e}")
        return False

def test_integration():
    """Test integration of all security improvements"""
    print("\n🔗 Testing Integration")
    print("-" * 30)
    
    try:
        # Test configuration loading
        from config import get_config
        config = get_config()
        print("✅ Configuration loaded")
        
        # Test SSL verification options
        seqrng_ssl = config.get_ssl_verify_option('seqrng')
        ctm_ssl = config.get_ssl_verify_option('ctm')
        print(f"✅ SSL verification options:")
        print(f"   SeQRNG: {seqrng_ssl}")
        print(f"   CTM: {ctm_ssl}")
        
        # Test module imports
        from sq_ctm_modules_v1 import ctm_get_api_key, ctm_upload_key, ctm_key_exists
        from interface_seqrng_v2 import sq_get_random_bytes
        print("✅ All modules imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False

def test_dependencies():
    """Test if all required dependencies are available"""
    print("\n📦 Testing Dependencies")
    print("-" * 30)
    
    dependencies = [
        ('requests', 'HTTP requests'),
        ('urllib3', 'HTTP client'),
        ('certifi', 'SSL certificates'),
        ('jwt', 'JWT validation'),
        ('cryptography', 'Cryptographic functions'),
        ('secrets', 'Cryptographic random'),
        ('hashlib', 'Hash functions'),
        ('uuid', 'UUID generation'),
        ('math', 'Mathematical functions'),
        ('statistics', 'Statistical functions'),
        ('collections', 'Data structures')
    ]
    
    all_available = True
    
    for module, description in dependencies:
        try:
            __import__(module)
            print(f"✅ {module}: {description}")
        except ImportError:
            print(f"❌ {module}: {description} - NOT AVAILABLE")
            all_available = False
    
    return all_available

def main():
    """Main test function"""
    print("🔒 Security Improvements Test Suite")
    print("=" * 50)
    
    # Test dependencies
    deps_ok = test_dependencies()
    
    # Test JWT validation
    jwt_ok = test_jwt_validation()
    
    # Test secure key generation
    key_gen_ok = test_secure_key_generation()
    
    # Test entropy validation
    entropy_ok = test_entropy_validation()
    
    # Test integration
    integration_ok = test_integration()
    
    # Summary
    print("\n📊 Test Results Summary")
    print("-" * 30)
    print(f"Dependencies: {'✅ PASS' if deps_ok else '❌ FAIL'}")
    print(f"JWT Validation: {'✅ PASS' if jwt_ok else '❌ FAIL'}")
    print(f"Secure Key Generation: {'✅ PASS' if key_gen_ok else '❌ FAIL'}")
    print(f"Entropy Validation: {'✅ PASS' if entropy_ok else '❌ FAIL'}")
    print(f"Integration: {'✅ PASS' if integration_ok else '❌ FAIL'}")
    
    all_passed = all([deps_ok, jwt_ok, key_gen_ok, entropy_ok, integration_ok])
    
    if all_passed:
        print("\n🎉 All tests passed! Security improvements are working correctly.")
        print("\n📋 Next steps:")
        print("1. Install dependencies: pip install -r requirements.txt")
        print("2. Configure your .env file")
        print("3. Test with: python SeQRNG_CTM_1.4.py")
    else:
        print("\n⚠️ Some tests failed. Please check the configuration and dependencies.")
        print("\n🔧 To fix issues:")
        print("1. Install missing dependencies: pip install -r requirements.txt")
        print("2. Check Python version (3.7+ required)")
        print("3. Verify all modules are in the correct location")
    
    return all_passed

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test suite error: {e}")
        sys.exit(1) 