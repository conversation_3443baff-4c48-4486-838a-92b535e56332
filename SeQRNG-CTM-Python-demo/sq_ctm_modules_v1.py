from urllib3.exceptions import InsecureRequestWarning
import requests
import random
import base64
import time
import os
import sys
import json

def get_valid_input(valid_options, prompt_text=None, default_value=None):
    """
    Prompt the user for input until they enter one of the valid options.
    Allows a default value when user presses Enter.

    Parameters:
        valid_options (list of str): The allowed input values.
        prompt_text (str): Optional custom message to show to the user.
        default_value (str): Optional default to use if the user presses Enter.

    Returns:
        str: The valid input entered by the user or the default.
    """
    if not valid_options:
        raise ValueError("List of valid options cannot be empty.")

    valid_options = [option.lower() for option in valid_options]  # Normalize

    #if default_value is not None:
        #default_value = default_value.lower()
        #if default_value not in valid_options:
            #raise ValueError(f"Default value '{default_value}' is not in valid options.")

    # Build prompt
    options_display = ', '.join(valid_options)
    if prompt_text is None:
        prompt_message = f"Choose one ({options_display})"
    else:
        prompt_message = f"{prompt_text} ({options_display})"

    if default_value is not None:
        prompt_message += f" [default: {default_value}]"

    prompt_message += ": "

    # Input loop
    while True:
        user_input_raw = input(prompt_message).strip().lower()
        user_input = user_input_raw.strip().lower()
        if not user_input and default_value is not None:
            return default_value
        elif user_input in valid_options:
            return user_input_raw
        else:
            print(f"Invalid input. Please choose from: {options_display}")


def get_valid_input2(valid_options, prompt_text=None):
    """
    Prompt the user for input until they enter one of the valid options.
    
    Parameters:
        valid_options (list of str): The allowed input values.
        prompt_message (str): Optional custom message to show to the user.
    
    Returns:
        str: The valid input entered by the user.
    """
    if not valid_options:
        raise ValueError("List of valid options cannot be empty.")
    
    valid_options = [option.lower() for option in valid_options]  # Normalize

    if prompt_text is None:
        prompt_message = f"Choose one ({', '.join(valid_options)}): "
    else:
        prompt_message = f"{prompt_text} ({', '.join(valid_options)}): "
    while True:
        user_input = input(prompt_message).strip().lower()
        if user_input in valid_options:
            return user_input
        else:
            print(f"Invalid input. Please choose from: {', '.join(valid_options)}")

    # input_validator.py

def get_integer_input(min_val, max_val, prompt_text=None):
    """
    Prompt the user for an integer input between min_val and max_val (inclusive).
    
    Args:
        min_val (int): Minimum acceptable value.
        max_val (int): Maximum acceptable value.
        prompt (str, optional): Custom prompt text. Default is auto-generated.

    Returns:
        int: Validated integer input from user.
    """
    if prompt_text is None:
        prompt = f"Enter a number between {min_val} and {max_val}: "
    else:
        prompt = f"{prompt_text} (between {min_val} and {max_val}): "    
    while True:
        try:
            user_input = int(input(prompt))
            if min_val <= user_input <= max_val:
                return user_input
            else:
                print(f"Please enter a number between {min_val} and {max_val}.")
        except ValueError:
            print("Invalid input. Please enter a valid integer.")

def str_to_boolean(s):
    """
    Convert a string to a boolean value.
    Accepted true values: 'true', '1', 'yes', 'on'
    Accepted false values: 'false', '0', 'no', 'off'
    """
    s = s.strip().lower()
    if s in ['true', '1', 'yes', 'on']:
        return True
    elif s in ['false', '0', 'no', 'off']:
        return False
    else:
        raise ValueError(f"Cannot convert '{s}' to boolean.")
    


 
 

# Module to separate N bytes into m groups of n, for multiple keys
def group_bytes(data: bytes, n: int, m: int):
    """
    Splits bytes into m groups of n bytes.
    
    Args:
        data (bytes): The input byte sequence.
        m (int): Number of groups.
        n (int): Size of each group (in bytes).

    Returns:
        List[bytes]: List of m byte groups of length n.

    Raises:
        ValueError: If data length < m * n.
    """
    total_needed = m * n
    if len(data) < total_needed:
        raise ValueError(f"Not enough data: need at least {total_needed} bytes, got {len(data)}.")

    return [data[i*n:(i+1)*n] for i in range(m)]

def check_key_length(key,size): 
# Check key length
    if len(key) == size:
        print("Random seed generated and length is "+str(len(key))+" bytes............success")
        length_status=True
    else:
        print("Error: Key not generated or not equal to required number of bytes. Exit program.")
        length_status=False
    return(length_status) 

# ---------Routine to upload random byte key to  CTM -----------
def ctm_upload_key(base_url, api_key, name_key, key_material, algorithm, own, export, ssl_verify=None):
    """
    Upload key to CTM with SSL verification and JWT validation
    
    Args:
        base_url (str): CTM base URL
        api_key (str): CTM API key
        name_key (str): Key name
        key_material (bytes): Key material
        algorithm (str): Algorithm type
        own (str): Owner
        export (bool): Exportable flag
        ssl_verify: SSL verification option (from config)
    """
    # Validar token JWT antes de hacer request
    try:
        from jwt_validator import JWTValidator
        jwt_validator = JWTValidator()
        
        # JWT validation (silent)
        if jwt_validator.is_token_expired(api_key):
            pass  # Silently handle expired token
        elif jwt_validator.should_refresh_token(api_key):
            pass  # Silently handle token that expires soon
        else:
            pass  # Token is valid
            
    except ImportError:
        pass  # Silently ignore if JWT validator not available
    except Exception as e:
        pass  # Silently ignore JWT validation errors
    
    key_material_len=len(key_material)
    headers = {'Authorization': f'Bearer {api_key}'}
    ctm_key_data = { "name": name_key, "usageMask": 12, "algorithm": algorithm, "meta": { "ownerId": own }, "state": "Pre-Active", "unexportable": export, "material": key_material.hex(), "aliases": [ { "alias": name_key, "type": "string" } ] }

    # Use base_url directly if it's a complete URL, otherwise treat as IP for backward compatibility
    if base_url.startswith(('http://', 'https://')):
        api_url = f"{base_url.rstrip('/')}/api/v1/vault/keys2"
    else:
        # Backward compatibility: treat as IP address
        api_url = f"https://{base_url}/api/v1/vault/keys2"
    
    # Use SSL verification from config, fallback to False for backward compatibility
    verify_option = ssl_verify if ssl_verify is not None else False
    
    response = requests.post(api_url, headers=headers, json=ctm_key_data, verify=verify_option, timeout=30)
    content = json.loads(response.content)
    ctm_key_id=content['id']
    if (response):
        print(f"Key {name_key} uploaded successfully, CTM Key ID is: ",ctm_key_id)
        
    else:
        print("Key not uploaded to CipherTrust: Error\nExit program.")
        sys.exit(0)

# ---------Routine to get api key from CTM -----------
def ctm_get_api_key(base_url, login_data, ssl_verify=None):
    """
    Get API key from CTM with SSL verification and JWT validation
    
    Args:
        base_url (str): CTM base URL
        login_data (dict): Login credentials
        ssl_verify: SSL verification option (from config)
    
    Returns:
        str: API key
    """
    # Suppress the warnings from urllib3 only if SSL verification is disabled
    if ssl_verify is False:
        requests.packages.urllib3.disable_warnings(category=InsecureRequestWarning)
    
    # Use base_url directly if it's a complete URL, otherwise treat as IP for backward compatibility
    if base_url.startswith(('http://', 'https://')):
        api_url = f"{base_url.rstrip('/')}/api/v1/auth/tokens"
    else:
        # Backward compatibility: treat as IP address
        api_url = f"https://{base_url}/api/v1/auth/tokens"
    
    # Use SSL verification from config, fallback to False for backward compatibility
    verify_option = ssl_verify if ssl_verify is not None else False
    
    try:
        response = requests.post(api_url, json=login_data, verify=verify_option, timeout=30)
        
        # Check if the request was successful
        if response.status_code != 200:
            print(f"CTM authentication failed: HTTP {response.status_code}")
            print(f"Response: {response.text}")
            sys.exit(1)
        
        # Try to parse as JSON first
        try:
            json_data = response.json()
            # Look for token in common JSON response formats
            if isinstance(json_data, dict):
                # Try different possible field names for the token
                api_key = None
                for field in ['jwt', 'token', 'access_token', 'api_key', 'key', 'value']:
                    if field in json_data:
                        api_key = json_data[field]
                        break
                
                if api_key:
                    # Validar token JWT
                    try:
                        from jwt_validator import JWTValidator
                        jwt_validator = JWTValidator()
                        
                        try:
                            jwt_validator.validate_jwt_token(api_key)
                            # JWT validation successful (silent)
                        except ValueError as e:
                            # JWT validation warning (silent)
                            pass
                        
                    except ImportError:
                        pass  # Silently ignore if JWT validator not available
                    except Exception as e:
                        pass  # Silently ignore JWT validation errors
                    
                    return api_key
        except json.JSONDecodeError:
            pass
        
        # Fallback to the original string parsing method
        response_text = response.text
        if '"' in response_text:
            data = response_text.split('"')[1::2]
            if len(data) >= 2:
                api_key = data[1]
                if api_key and api_key != 'null':
                    # JWT validation (fallback, silent)
                    try:
                        from jwt_validator import JWTValidator
                        jwt_validator = JWTValidator()
                        # JWT validation successful (silent)
                    except:
                        pass
                    
                    return api_key
        
        # If we get here, we couldn't extract the token
        print("Connection to CipherTrust Manager: Could not extract API token from response")
        print(f"Response content: {response.text}")
        sys.exit(1)
        
    except requests.exceptions.RequestException as e:
        print(f"Connection to CipherTrust Manager: Network error - {e}")
        sys.exit(1)
    except Exception as e:
        print(f"Connection to CipherTrust Manager: Unexpected error - {e}")
        sys.exit(1)
# ---------End get api key routine -----------

# ---------Routine to check if a key exists in CTM -----------
def ctm_key_exists(base_url, api_key, name_key, ssl_verify=None):
    """
    Check if a key with the given name already exists in the CTM.
    Returns True if exists, False if not.
    Raises exception on error.
    
    Args:
        base_url (str): CTM base URL
        api_key (str): CTM API key
        name_key (str): Key name to check
        ssl_verify: SSL verification option (from config)
    """
    # Validar token JWT antes de hacer request
    try:
        from jwt_validator import JWTValidator
        jwt_validator = JWTValidator()
        
        # JWT validation (silent)
        if jwt_validator.is_token_expired(api_key):
            pass  # Silently handle expired token
        elif jwt_validator.should_refresh_token(api_key):
            pass  # Silently handle token that expires soon
            
    except ImportError:
        pass  # Silently ignore if JWT validator not available
    except Exception as e:
        pass  # Silently ignore JWT validation errors
    
    headers = {'Authorization': f'Bearer {api_key}'}
    params = {'name': name_key}
    
    # Use base_url directly if it's a complete URL, otherwise treat as IP for backward compatibility
    if base_url.startswith(('http://', 'https://')):
        api_url = f"{base_url.rstrip('/')}/api/v1/vault/keys2"
    else:
        # Backward compatibility: treat as IP address
        api_url = f"https://{base_url}/api/v1/vault/keys2"
    
    # Use SSL verification from config, fallback to False for backward compatibility
    verify_option = ssl_verify if ssl_verify is not None else False
    
    try:
        response = requests.get(
            api_url,
            headers=headers,
            params=params,
            verify=verify_option,
            timeout=30
        )
        if response.status_code == 404:
            # No keys found with that name
            return False
        elif response.status_code != 200:
            raise Exception(f"Error querying CTM: {response.status_code} {response.text}")
        
        content = response.json()
        
        # Handle different response formats
        if isinstance(content, dict) and 'resources' in content:
            keys = content['resources']
        elif isinstance(content, list):
            keys = content
        else:
            # Single key object or other format
            keys = [content] if content else []
        
        # Ensure keys is a list and not None
        if keys is None:
            return False
        
        # Check if any key matches the name exactly
        for key in keys:
            if isinstance(key, dict) and 'name' in key and key['name'] == name_key:
                print(f"Key '{name_key}' already exists in CTM")
                return True
        
        return False
        
    except requests.exceptions.RequestException as e:
        print(f"Network error checking if key exists: {e}")
        raise
    except json.JSONDecodeError as e:
        print(f"Error parsing JSON response: {e}")
        raise
    except Exception as e:
        print(f"Error checking if key exists: {e}")
        raise

