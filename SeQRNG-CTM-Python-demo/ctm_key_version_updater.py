#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CTM Key Version Updater
=======================

This script allows updating existing keys in Thales CipherTrust Manager
with new quantum material generated by SeQRNG.

Use case: A client wants to reinforce their current keys with quantum material
from our QRNG.

Author: Based on SeQRNG_CTM_1.4.py
"""

from urllib3.exceptions import InsecureRequestWarning
import requests
import base64
import time
import os
import sys
import json
import gc
import warnings

from interface_seqrng_v2 import sq_get_random_bytes
from sq_ctm_modules_v1 import get_valid_input, get_integer_input, str_to_boolean, check_key_length, ctm_get_api_key
from config import get_config

coding = "latin-1"

def load_configuration():
    """
    Load and validate configuration from secure sources
    Returns configuration dictionaries for SeQRNG and CTM
    """
    try:
        config = get_config()
        
        # Validate configuration
        if not config.validate_config():
            print("\n❌ Configuration validation failed!")
            print("\n🔧 Available setup options:")
            print("1. Run: python config.py --setup (interactive setup)")
            print("2. Run: python config.py --create-examples (create example files)")
            print("3. Set environment variables manually")
            print("4. Create .env file manually")
            sys.exit(1)
        
        # Get configuration
        seqrng_config = config.get_seqrng_config()
        ctm_config = config.get_ctm_config()
        
        # Configuration loaded successfully
        
        return seqrng_config, ctm_config
        
    except Exception as e:
        print(f"\n❌ Configuration error: {e}")
        print("\n🔧 To fix this, run one of the following:")
        print("  python config.py --setup")
        print("  python config.py --create-examples")
        sys.exit(1)

def ctm_create_key_version(base_url, api_key, key_id, key_material, algorithm):
    """
    Create a new version of an existing key with new quantum material
    
    Args:
        base_url (str): CTM base URL
        api_key (str): CTM API key
        key_id (str): ID of the key to create the new version
        key_material (bytes): New quantum material in bytes
        algorithm (str): Algorithm type (aes, aria, hmac-sha1, etc.)
    
    Returns:
        bool: True if successful, False otherwise
    """
    headers = {'Authorization': f'Bearer {api_key}'}
    
    # Prepare the request payload
    # According to the schema, material should be hex-encoded for most algorithms
    # except for RSA/EC which should be PEM-encoded
    if algorithm in ['rsa', 'ec']:
        # For RSA/EC, material should be PEM-encoded
        # This is a simplified approach - in practice you'd need proper PEM formatting
        material_value = base64.b64encode(key_material).decode('utf-8')
    else:
        # For other algorithms (aes, aria, hmac-*), material should be hex-encoded
        material_value = key_material.hex()
    
    ctm_version_data = {
        "material": material_value
    }
    
    # Use base_url directly if it's a complete URL, otherwise treat as IP for backward compatibility
    if base_url.startswith(('http://', 'https://')):
        api_url = f"{base_url.rstrip('/')}/api/v1/vault/keys2/{key_id}/versions"
    else:
        # Backward compatibility: treat as IP address
        api_url = f"https://{base_url}/api/v1/vault/keys2/{key_id}/versions"
    
    try:
        print(f"🔄 Creating new version for key ID: {key_id}")
        print(f"📊 Algorithm: {algorithm}")
        print(f"🔢 Material length: {len(key_material)} bytes")
        
        response = requests.post(api_url, headers=headers, json=ctm_version_data, verify=False)
        
        if response.status_code == 201 or response.status_code == 200:
            content = response.json()
            print(f"✅ New version created successfully!")
            
            # Show only essential information from response
            version_id = content.get('id', 'N/A')
            version_number = content.get('version', 'N/A')
            created_at = content.get('createdAt', 'N/A')
            
            print(f"📋 New version created:")
            print(f"   🔑 ID: {version_id}")
            print(f"   🔄 Version: {version_number}")
            print(f"   📅 Created: {created_at}")
            
            return True
        else:
            print(f"❌ Failed to create new version: HTTP {response.status_code}")
            print(f"📋 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error creating new version: {e}")
        return False

def ctm_get_key_info(base_url, api_key, key_id):
    """
    Get information about an existing key
    
    Args:
        base_url (str): CTM base URL
        api_key (str): CTM API key
        key_id (str): ID of the key to get info for
    
    Returns:
        dict: Key information or None if error
    """
    headers = {'Authorization': f'Bearer {api_key}'}
    
    # Use base_url directly if it's a complete URL, otherwise treat as IP for backward compatibility
    if base_url.startswith(('http://', 'https://')):
        api_url = f"{base_url.rstrip('/')}/api/v1/vault/keys2/{key_id}"
    else:
        # Backward compatibility: treat as IP address
        api_url = f"https://{base_url}/api/v1/vault/keys2/{key_id}"
    
    try:
        response = requests.get(api_url, headers=headers, verify=False)
        
        if response.status_code == 200:
            content = response.json()
            return content
        else:
            print(f"❌ Failed to get key info: HTTP {response.status_code}")
            print(f"📋 Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error getting key info: {e}")
        return None

def ctm_archive_key(base_url, api_key, key_id):
    """
    Archive an existing key, making it unavailable for use
    
    Args:
        base_url (str): CTM base URL
        api_key (str): CTM API key
        key_id (str): ID of the key to archive
    
    Returns:
        bool: True if successful, False otherwise
    """
    headers = {'Authorization': f'Bearer {api_key}'}
    
    # Use base_url directly if it's a complete URL, otherwise treat as IP for backward compatibility
    if base_url.startswith(('http://', 'https://')):
        api_url = f"{base_url.rstrip('/')}/api/v1/vault/keys2/{key_id}/archive"
    else:
        # Backward compatibility: treat as IP address
        api_url = f"https://{base_url}/api/v1/vault/keys2/{key_id}/archive"
    
    try:
        print(f"📦 Archiving key ID: {key_id}")
        
        response = requests.post(api_url, headers=headers, verify=False)
        
        if response.status_code == 200:
            content = response.json()
            print(f"✅ Key archived successfully!")
            
            # Show essential information from response
            archive_date = content.get('archiveDate', 'N/A')
            state = content.get('state', 'N/A')
            name = content.get('name', 'N/A')
            
            print(f"📋 Archive information:")
            print(f"   📝 Name: {name}")
            print(f"   📅 Archive date: {archive_date}")
            print(f"   🔒 State: {state}")
            
            return True
        else:
            print(f"❌ Failed to archive key: HTTP {response.status_code}")
            print(f"📋 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error archiving key: {e}")
        return False

def update_key_version(key_id):
    """
    Main logic to create a key version. Can be called from other scripts.
    
    Args:
        key_id (str): The ID of the key to create the new version.
    """
    # Load secure configuration
    seqrng_config, ctm_config = load_configuration()
    
    # Extract configuration values
    sq_base_url = seqrng_config['base_url']
    sq_api_token = seqrng_config['api_token']
    
    ctm_base_url = ctm_config['base_url'] 
    ctm_login_data = {
        "name": ctm_config['username'],
        "password": ctm_config['password'],
        "domain": ctm_config['domain']
    }
    
    # Get API key from CTM
    ctm_api_key = ctm_get_api_key(ctm_base_url, ctm_login_data)
    
    # Get key information to determine algorithm and size
    key_info = ctm_get_key_info(ctm_base_url, ctm_api_key, key_id)
    
    if not key_info:
        print("❌ Could not get key information. Verify the ID.")
        sys.exit(1)
    
    # Extract algorithm and determine key size
    algorithm = key_info.get('algorithm', 'aes')
    current_size = key_info.get('size', 'N/A')
    
    # Determine key size based on algorithm (use current size if available)
    if algorithm == "aes":
        if current_size != 'N/A' and isinstance(current_size, (int, str)):
            try:
                current_size_int = int(current_size)
                numbytes = current_size_int // 8
            except ValueError:
                numbytes = 32  # Default
        else:
            numbytes = 32
    elif algorithm == "aria":
        if current_size != 'N/A' and isinstance(current_size, (int, str)):
            try:
                current_size_int = int(current_size)
                numbytes = current_size_int // 8
            except ValueError:
                numbytes = 32  # Default
        else:
            numbytes = 32
    elif algorithm == "hmac-sha1":
        if current_size != 'N/A' and isinstance(current_size, (int, str)):
            try:
                current_size_int = int(current_size)
                numbytes = current_size_int // 8
            except ValueError:
                numbytes = 32  # Default
        else:
            numbytes = 32
    elif algorithm == "hmac-sha256":
        if current_size != 'N/A' and isinstance(current_size, (int, str)):
            try:
                current_size_int = int(current_size)
                numbytes = current_size_int // 8
            except ValueError:
                numbytes = 32  # Default
        else:
            numbytes = 32
    elif algorithm == "hmac-sha384":
        if current_size != 'N/A' and isinstance(current_size, (int, str)):
            try:
                current_size_int = int(current_size)
                numbytes = current_size_int // 8
            except ValueError:
                numbytes = 48  # Default
        else:
            numbytes = 48
    elif algorithm == "hmac-sha512":
        if current_size != 'N/A' and isinstance(current_size, (int, str)):
            try:
                current_size_int = int(current_size)
                numbytes = current_size_int // 8
            except ValueError:
                numbytes = 64  # Default
        else:
            numbytes = 64
    else:
        if current_size != 'N/A' and isinstance(current_size, (int, str)):
            try:
                current_size_int = int(current_size)
                numbytes = current_size_int // 8
            except ValueError:
                numbytes = 32  # Default
        else:
            numbytes = 32
    
    confirm = get_valid_input(["y","n"], f"Confirm new version creation for the key '{key_info.get('name')}' (ID: {key_id})?", "n")
    if confirm.lower() != 'y':
        print("❌ Operation cancelled by user")
        sys.exit(0)
    
    try:
        # Get quantum random bytes from SeQRNG
        qrng_key, errstr, etystr, etystatus = sq_get_random_bytes(numbytes, 1, sq_base_url, sq_api_token)
        
        if check_key_length(qrng_key, numbytes):
            # Create new version with quantum material
            success = ctm_create_key_version(ctm_base_url, ctm_api_key, key_id, qrng_key, algorithm)
            
            if success:
                # Archive the original key to ensure only the latest version is available
                archive_success = ctm_archive_key(ctm_base_url, ctm_api_key, key_id)
                
                if archive_success:
                    print(f"✅ Key {key_id} new version created and previous version archived successfully")
                else:
                    print(f"✅ Key {key_id} new version created, but failed to archive previous version")
            else:
                print("❌ Error creating new key version")
                sys.exit(1)
        else:
            print("❌ Error: Quantum material not generated correctly")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ Error during creation the new version: {e}")
        sys.exit(1)

def main():
    """
    Main function to create new version of CTM keys with quantum material
    """
    # Suppress SSL warnings for development
    warnings.filterwarnings('ignore', category=InsecureRequestWarning)
    
    print("🔐 CTM Key New Version Creation")
    print("==========================")
    
    key_id = None
    if len(sys.argv) > 1:
        key_id = sys.argv[1]
        print(f"🔑 Key ID provided via argument: {key_id}")
    else:
        key_id = input("🔑 Enter the ID of the key to create a new version: ").strip()

    if not key_id:
        print("❌ Error: Key ID is required")
        sys.exit(1)
        
    update_key_version(key_id)

if __name__ == "__main__":
    main() 