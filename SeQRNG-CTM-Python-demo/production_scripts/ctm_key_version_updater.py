#!/Users/<USER>/Dev/SQQ-CTM/venv/bin/python
# -*- coding: utf-8 -*-
"""
Script de producción generado automáticamente
Ejecuta: ctm_key_version_updater.py
"""

import sys
import os

# Obtener la ruta absoluta del directorio del proyecto
PROJECT_DIR = "/Users/<USER>/Dev/SQQ-CTM"
SCRIPT_PATH = os.path.join(PROJECT_DIR, "ctm_key_version_updater.py")

# Agregar el directorio del proyecto al path para importar módulos
sys.path.insert(0, PROJECT_DIR)

# Cambiar al directorio del proyecto
os.chdir(PROJECT_DIR)

# Ejecutar el script original
exec(open(SCRIPT_PATH).read())
