#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Configuration management for SeQRNG-CTM integration
Handles secure configuration loading from environment variables and config files
"""

import os
import json
import sys
import re
import ipaddress
from pathlib import Path
from typing import Dict, Any, Optional
from urllib.parse import urlparse

def parse_connection_string(connection_input: str, default_protocol: str = "https", default_port: Optional[int] = None) -> str:
    """
    Parse a connection string that can be either an IP address or a URL.
    Returns a normalized URL.
    
    Args:
        connection_input: Can be IP (*************), URL (https://example.com), or hostname (example.com)
        default_protocol: Protocol to use if none specified (default: https)
        default_port: Port to use if none specified
        
    Returns:
        Normalized URL string
        
    Examples:
        '*************' -> 'https://*************'
        'example.com' -> 'https://example.com'
        'http://example.com:8080' -> 'http://example.com:8080'
        'https://*************:8443' -> 'https://*************:8443'
    """
    if not connection_input or not connection_input.strip():
        raise ValueError("Connection string cannot be empty")
    
    connection_input = connection_input.strip()
    
    # If it already has a protocol, parse and validate
    if '://' in connection_input:
        parsed = urlparse(connection_input)
        if not parsed.scheme or not parsed.netloc:
            raise ValueError(f"Invalid URL format: {connection_input}")
        return connection_input
    
    # Check if it's an IPv4 address
    try:
        ipaddress.IPv4Address(connection_input)
        # It's a valid IPv4 address
        base_url = f"{default_protocol}://{connection_input}"
        if default_port:
            base_url += f":{default_port}"
        return base_url
    except ipaddress.AddressValueError:
        pass
    
    # Check if it's an IPv6 address
    try:
        ipaddress.IPv6Address(connection_input)
        # It's a valid IPv6 address
        base_url = f"{default_protocol}://[{connection_input}]"
        if default_port:
            base_url += f":{default_port}"
        return base_url
    except ipaddress.AddressValueError:
        pass
    
    # Check if it's IP:port format
    if ':' in connection_input and not connection_input.count(':') > 1:  # Not IPv6
        try:
            ip_part, port_part = connection_input.rsplit(':', 1)
            ipaddress.IPv4Address(ip_part)  # Validate IP part
            int(port_part)  # Validate port part
            return f"{default_protocol}://{connection_input}"
        except (ipaddress.AddressValueError, ValueError):
            pass
    
    # Check if it's hostname:port format
    if ':' in connection_input and not connection_input.count(':') > 1:
        try:
            hostname_part, port_part = connection_input.rsplit(':', 1)
            int(port_part)  # Validate port part
            # Basic hostname validation
            if re.match(r'^[a-zA-Z0-9.-]+$', hostname_part):
                return f"{default_protocol}://{connection_input}"
        except ValueError:
            pass
    
    # Assume it's a hostname/domain
    if re.match(r'^[a-zA-Z0-9.-]+$', connection_input):
        base_url = f"{default_protocol}://{connection_input}"
        if default_port:
            base_url += f":{default_port}"
        return base_url
    
    raise ValueError(f"Unable to parse connection string: {connection_input}")

def validate_connection_string(connection_input: str) -> bool:
    """
    Validate if a connection string can be parsed successfully.
    
    Args:
        connection_input: Connection string to validate
        
    Returns:
        True if valid, False otherwise
    """
    try:
        parse_connection_string(connection_input)
        return True
    except ValueError:
        return False

class Config:
    """
    Configuration manager that loads settings from environment variables,
    .env files, and configuration files in order of precedence.
    """
    
    def __init__(self, config_file: Optional[str] = None):
        """
        Initialize configuration manager
        
        Args:
            config_file: Optional path to JSON configuration file
        """
        self.config_file = config_file or "config.json"
        self._config = {}
        self._load_config()
    
    def _load_env_file(self):
        """Load environment variables from .env file if it exists"""
        env_file = Path(".env")
        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        # Solo establecer si no está ya en el entorno del sistema
                        if key.strip() not in os.environ:
                            os.environ[key.strip()] = value.strip().strip('"\'')
    
    def _load_config_file(self):
        """Load configuration from JSON file if it exists"""
        config_path = Path(self.config_file)
        if config_path.exists():
            try:
                with open(config_path, 'r') as f:
                    file_config = json.load(f)
                    self._config.update(file_config)
            except (json.JSONDecodeError, IOError) as e:
                print(f"Warning: Could not load config file {self.config_file}: {e}")
    
    def _load_config(self):
        """Load configuration from all sources in order of precedence"""
        # 1. Load from config file (lowest precedence)
        self._load_config_file()
        
        # 2. Load from .env file
        self._load_env_file()
        
        # 3. Environment variables have highest precedence (already loaded)
    
    def get(self, key: str, default: Any = None, required: bool = False) -> Any:
        """
        Get configuration value from environment variables or config file
        
        Args:
            key: Configuration key
            default: Default value if key not found
            required: If True, raise exception if key not found
            
        Returns:
            Configuration value
            
        Raises:
            ValueError: If required key is not found
        """
        # Check environment variables first (highest precedence)
        value = os.environ.get(key)
        
        # Then check config file
        if value is None:
            value = self._config.get(key)
        
        # Use default if still not found
        if value is None:
            value = default
        
        # Check if required
        if required and value is None:
            raise ValueError(f"Required configuration key '{key}' not found")
        
        return value
    
    def is_production(self) -> bool:
        """
        Check if running in production environment
        
        Returns:
            True if production, False if development
        """
        env = self.get('ENVIRONMENT', 'development').lower()
        return env in ['production', 'prod', 'true']
    
    def get_ssl_verify_option(self, connection_type: str = 'ctm') -> Any:
        """
        Get SSL verification option based on environment and connection type
        
        Args:
            connection_type: 'ctm' for CTM connection, 'seqrng' for SeQRNG connection
            
        Returns:
            SSL verification option (cert path, True, False, or certifi.where())
        """
        # Check if SSL verification is disabled globally
        verify_ssl = self.get('VERIFY_SSL', 'true').lower() == 'true'
        if not verify_ssl:
            print("⚠️  SSL verification disabled globally (development mode)")
            return False
        
        # Production environment
        if self.is_production():
            if connection_type == 'seqrng':
                # SeQRNG connection in production - use local certificate
                cert_path = '/etc/pki/tls/certs/apache-selfsigned.crt'
                if os.path.exists(cert_path):
                    return cert_path
                else:
                    return False
            else:
                # CTM connection in production - use system certificates
                try:
                    import certifi
                    return certifi.where()
                except ImportError:
                    return True
        else:
            # Development environment
            if connection_type == 'seqrng':
                # SeQRNG connection in development - check for local certificate
                cert_path = self.get('SEQRNG_CERT_PATH')
                if cert_path and os.path.exists(cert_path):
                    return cert_path
                else:
                    return False
            else:
                # CTM connection in development - check for custom certificate
                cert_path = self.get('CTM_CERT_PATH')
                if cert_path and os.path.exists(cert_path):
                    return cert_path
                else:
                    try:
                        import certifi
                        return certifi.where()
                    except ImportError:
                        return False
    
    def get_seqrng_config(self) -> Dict[str, str]:
        """Get SeQRNG configuration with normalized URL and SSL options"""
        raw_address = self.get('SEQRNG_IP_ADDRESS', required=True)
        try:
            # Detect if protocol is already specified in the address
            if raw_address.startswith(('http://', 'https://')):
                # Protocol is already specified, use as is
                normalized_url = raw_address
            else:
                # No protocol specified, use HTTP by default for backward compatibility
                normalized_url = f"http://{raw_address}"
            
            return {
                'ip_address': raw_address,  # Keep original for backward compatibility 
                'base_url': normalized_url,  # New normalized URL
                'api_token': self.get('SEQRNG_API_TOKEN', required=True),
                'ssl_verify': self.get_ssl_verify_option('seqrng')  # SSL verification option
            }
        except ValueError as e:
            raise ValueError(f"Invalid SeQRNG address format '{raw_address}': {e}")
    
    def get_ctm_config(self) -> Dict[str, Any]:
        """Get CipherTrust Manager configuration with normalized URL and SSL options"""
        raw_address = self.get('CTM_IP_ADDRESS', required=True)
        try:
            # Detect if protocol is already specified in the address
            if raw_address.startswith(('http://', 'https://')):
                # Protocol is already specified, use as is
                normalized_url = raw_address
            else:
                # No protocol specified, use HTTP by default for backward compatibility
                normalized_url = f"http://{raw_address}"
            
            return {
                'ip_address': raw_address,  # Keep original for backward compatibility
                'base_url': normalized_url,  # New normalized URL
                'username': self.get('CTM_USERNAME', required=True),
                'password': self.get('CTM_PASSWORD', required=True),
                'domain': self.get('CTM_DOMAIN', required=True),
                'ssl_verify': self.get_ssl_verify_option('ctm')  # SSL verification option
            }
        except ValueError as e:
            raise ValueError(f"Invalid CTM address format '{raw_address}': {e}")
    
    def validate_config(self) -> bool:
        """
        Validate that all required configuration is present
        
        Returns:
            True if configuration is valid
            
        Raises:
            ValueError: If required configuration is missing
        """
        try:
            self.get_seqrng_config()
            self.get_ctm_config()
            return True
        except ValueError as e:
            print(f"Configuration validation failed: {e}")
            return False
    
    def create_example_files(self):
        """Create example configuration files for easy setup"""
        
        # Create example .env file
        env_example = """# Environment Configuration
# Set to 'production' for production environment, 'development' for local development
ENVIRONMENT=development

# SSL Configuration
# Set to 'true' to enable SSL verification, 'false' to disable (development only)
VERIFY_SSL=true

# SeQRNG Configuration (IP address or URL)
# Examples:
# SEQRNG_IP_ADDRESS=**************          # Uses HTTP by default
# SEQRNG_IP_ADDRESS=http://**************   # Explicit HTTP
# SEQRNG_IP_ADDRESS=https://**************  # Explicit HTTPS
# SEQRNG_IP_ADDRESS=https://seqrng.example.com
# SEQRNG_IP_ADDRESS=https://**************:8443
SEQRNG_IP_ADDRESS=**************
SEQRNG_API_TOKEN=1|your_seqrng_token_here

# SeQRNG Certificate Path (development only, production uses /etc/pki/tls/certs/apache-selfsigned.crt)
# SEQRNG_CERT_PATH=/path/to/your/local/certificate.crt

# CipherTrust Manager Configuration (IP address or URL)
# Examples:
# CTM_IP_ADDRESS=*************              # Uses HTTP by default
# CTM_IP_ADDRESS=http://*************       # Explicit HTTP
# CTM_IP_ADDRESS=https://*************      # Explicit HTTPS
# CTM_IP_ADDRESS=https://ctm.example.com
# CTM_IP_ADDRESS=https://*************:443
CTM_IP_ADDRESS=*************
CTM_USERNAME=seqrng
CTM_PASSWORD=your_secure_password_here
CTM_DOMAIN=Sequre_Quantum_test

# CTM Certificate Path (optional, for custom certificates)
# CTM_CERT_PATH=/path/to/ctm-certificate.crt

# Optional: Logging level
LOG_LEVEL=INFO
"""
        
        with open('.env.example', 'w') as f:
            f.write(env_example)
        
        # Create example config.json file
        config_example = {
            "_comment_environment": "Environment configuration",
            "environment": {
                "type": "development",
                "verify_ssl": True
            },
            "_comment_seqrng": "SeQRNG can be IP address, hostname, or full URL",
            "_examples_seqrng": [
                "**************",
                "https://seqrng.example.com", 
                "https://**************:8443"
            ],
            "seqrng": {
                "ip_address": "**************",
                "api_token": "1|your_seqrng_token_here"
            },
            "_comment_ctm": "CTM can be IP address, hostname, or full URL",
            "_examples_ctm": [
                "*************",
                "https://ctm.example.com",
                "https://*************:443"
            ],
            "ctm": {
                "ip_address": "*************",
                "username": "seqrng", 
                "password": "your_secure_password_here",
                "domain": "Sequre_Quantum_test"
            },
            "logging": {
                "level": "INFO"
            }
        }
        
        with open('config.json.example', 'w') as f:
            json.dump(config_example, f, indent=2)
        
        print("✅ Example configuration files created:")
        print("   - .env.example (recommended for production)")
        print("   - config.json.example (alternative option)")
        print("\n📋 Setup instructions:")
        print("1. Copy .env.example to .env")
        print("2. Edit .env with your actual credentials")
        print("3. Set ENVIRONMENT=production for production environment")
        print("4. Make sure .env is in your .gitignore file")

def get_config() -> Config:
    """Get global configuration instance"""
    return Config()

def interactive_setup():
    """Interactive setup for first-time configuration"""
    print("🔧 SeQRNG-CTM Configuration Setup")
    print("=" * 40)
    
    config = {}
    
    # Environment Configuration
    print("\n🌍 Environment Configuration:")
    env_choice = input("Environment (development/production) [development]: ").strip().lower()
    config['ENVIRONMENT'] = env_choice if env_choice else 'development'
    
    # SSL Configuration
    print("\n🔒 SSL Configuration:")
    verify_ssl = input("Enable SSL verification (true/false) [true]: ").strip().lower()
    config['VERIFY_SSL'] = verify_ssl if verify_ssl else 'true'
    
    # SeQRNG Configuration
    print("\n📡 SeQRNG Configuration:")
    print("   You can enter an IP address, hostname, or full URL")
    print("   Examples: **************, https://seqrng.example.com, https://**************:8443")
    config['SEQRNG_IP_ADDRESS'] = input("SeQRNG Address: ").strip()
    config['SEQRNG_API_TOKEN'] = input("SeQRNG API Token: ").strip()
    
    # SeQRNG Certificate (development only)
    if config['ENVIRONMENT'] == 'development':
        cert_path = input("SeQRNG Certificate Path (optional): ").strip()
        if cert_path:
            config['SEQRNG_CERT_PATH'] = cert_path
    
    # CTM Configuration  
    print("\n🔐 CipherTrust Manager Configuration:")
    print("   You can enter an IP address, hostname, or full URL")
    print("   Examples: *************, https://ctm.example.com, https://*************:443")
    config['CTM_IP_ADDRESS'] = input("CTM Address: ").strip()
    config['CTM_USERNAME'] = input("CTM Username: ").strip()
    config['CTM_PASSWORD'] = input("CTM Password: ").strip()
    config['CTM_DOMAIN'] = input("CTM Domain: ").strip()
    
    # CTM Certificate (optional)
    cert_path = input("CTM Certificate Path (optional): ").strip()
    if cert_path:
        config['CTM_CERT_PATH'] = cert_path
    
    # Save to .env file
    with open('.env', 'w') as f:
        f.write("# SeQRNG-CTM Configuration\n")
        f.write("# Generated by interactive setup\n\n")
        for key, value in config.items():
            f.write(f"{key}={value}\n")
    
    print("\n✅ Configuration saved to .env file")
    print("🔒 Make sure to add .env to your .gitignore file!")
    
    return config

if __name__ == "__main__":
    """Run interactive setup when executed directly"""
    if len(sys.argv) > 1 and sys.argv[1] == "--setup":
        interactive_setup()
    elif len(sys.argv) > 1 and sys.argv[1] == "--create-examples":
        config = Config()
        config.create_example_files()
    else:
        print("Usage:")
        print("  python config.py --setup          # Interactive configuration setup")
        print("  python config.py --create-examples # Create example config files") 