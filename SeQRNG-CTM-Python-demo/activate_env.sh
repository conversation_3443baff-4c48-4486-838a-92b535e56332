#!/bin/bash
# Script para activar el entorno virtual del proyecto SQQ-CTM

echo "🔐 Activando entorno virtual para SQQ-CTM..."
echo "=========================================="

# Verificar si el entorno virtual existe
if [ ! -d "venv" ]; then
    echo "❌ Entorno virtual no encontrado. Creando..."
    python3 -m venv venv
    echo "✅ Entorno virtual creado"
fi

# Activar el entorno virtual
source venv/bin/activate

# Verificar si las dependencias están instaladas
if ! python -c "import jwt, cryptography" 2>/dev/null; then
    echo "📦 Instalando dependencias..."
    pip install -r requirements.txt
    echo "✅ Dependencias instaladas"
else
    echo "✅ Dependencias ya instaladas"
fi

echo ""
echo "🎉 Entorno virtual activado!"
echo "📋 Comandos útiles:"
echo "   python sq-ctm-manager.py           # Script principal (menú interactivo)"
echo "   python test_security_improvements.py  # Ejecutar pruebas"
echo "   python debug_ctm.py               # Debug CTM"
echo ""
echo "🔧 Para desactivar: deactivate"
echo "" 