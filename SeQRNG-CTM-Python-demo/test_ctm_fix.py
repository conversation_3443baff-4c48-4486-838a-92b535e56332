#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script to verify the CTM authentication fix
"""

import sys
import os

def test_ctm_function():
    """Test the fixed CTM authentication function"""
    print("🧪 Testing CTM Authentication Fix")
    print("=" * 40)
    
    try:
        # Import the fixed function
        from sq_ctm_modules_v1 import ctm_get_api_key
        from config import get_config
        
        # Load configuration
        config = get_config()
        ctm_config = config.get_ctm_config()
        
        print("✅ Configuration loaded successfully")
        print(f"📡 CTM URL: {ctm_config['base_url']}")
        print(f"👤 Username: {ctm_config['username']}")
        print(f"🏢 Domain: {ctm_config['domain']}")
        print()
        
        # Prepare login data
        login_data = {
            "name": ctm_config['username'],
            "password": ctm_config['password'],
            "domain": ctm_config['domain']
        }
        
        print("🔄 Testing CTM authentication...")
        
        # Test the function
        try:
            api_key = ctm_get_api_key(ctm_config['base_url'], login_data)
            print(f"✅ Authentication successful!")
            print(f"🔑 API Key: ***HIDDEN***")
            return True
            
        except SystemExit as e:
            print(f"❌ Authentication failed with exit code: {e}")
            return False
        except Exception as e:
            print(f"❌ Authentication failed with error: {e}")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

def show_help():
    """Show help information"""
    print("\n🔧 CTM Authentication Fix Test")
    print("=" * 40)
    print()
    print("This script tests the fixed CTM authentication function.")
    print()
    print("Usage:")
    print("  python test_ctm_fix.py          # Test authentication")
    print("  python test_ctm_fix.py --help   # Show this help")
    print()
    print("Make sure your .env file contains:")
    print("  CTM_IP_ADDRESS=your_ctm_ip_or_url")
    print("  CTM_USERNAME=your_username")
    print("  CTM_PASSWORD=your_password")
    print("  CTM_DOMAIN=your_domain")
    print()
    print("For detailed debugging, use:")
    print("  python debug_ctm.py")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] in ['--help', '-h']:
        show_help()
    else:
        success = test_ctm_function()
        
        if success:
            print("\n🎉 CTM authentication test passed!")
            print("🚀 You can now run the main script: python SeQRNG_CTM_1.4.py")
        else:
            print("\n⚠️  CTM authentication test failed!")
            print("🔧 For detailed debugging, run: python debug_ctm.py")
            print("📋 For help, run: python test_ctm_fix.py --help") 