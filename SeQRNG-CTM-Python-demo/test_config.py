#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple Configuration Test Script
Tests the new flexible protocol detection from .env file
"""

import os
import sys

def test_config():
    """Test the configuration loading"""
    print("🧪 Testing Configuration Loading")
    print("=" * 40)
    
    try:
        from config import get_config
        config = get_config()
        
        # Load configurations
        seqrng_config = config.get_seqrng_config()
        ctm_config = config.get_ctm_config()
        
        print("✅ Configuration loaded successfully!")
        print()
        print("📡 SeQRNG Configuration:")
        print(f"   Original: {seqrng_config['ip_address']}")
        print(f"   Normalized: {seqrng_config['base_url']}")
        print(f"   Protocol: {seqrng_config['base_url'].split('://')[0]}")
        print()
        print("🔐 CTM Configuration:")
        print(f"   Original: {ctm_config['ip_address']}")
        print(f"   Normalized: {ctm_config['base_url']}")
        print(f"   Protocol: {ctm_config['base_url'].split('://')[0]}")
        print()
        
        # Test URL construction
        print("🔗 URL Examples:")
        print(f"   SeQRNG API: {seqrng_config['base_url'].rstrip('/')}/api/v1/get_data")
        print(f"   CTM Auth: {ctm_config['base_url'].rstrip('/')}/api/v1/auth/tokens")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        print("\n🔧 To fix this, create a .env file with:")
        print("   SEQRNG_IP_ADDRESS=your_ip_or_url")
        print("   SEQRNG_API_TOKEN=your_token")
        print("   CTM_IP_ADDRESS=your_ip_or_url")
        print("   CTM_USERNAME=your_username")
        print("   CTM_PASSWORD=your_password")
        print("   CTM_DOMAIN=your_domain")
        return False

def show_examples():
    """Show configuration examples"""
    print("\n📋 Configuration Examples:")
    print("=" * 40)
    print()
    print("Option 1: IP only (uses HTTP by default)")
    print("   SEQRNG_IP_ADDRESS=**********")
    print("   CTM_IP_ADDRESS=**********")
    print()
    print("Option 2: Explicit HTTP")
    print("   SEQRNG_IP_ADDRESS=http://**********")
    print("   CTM_IP_ADDRESS=http://**********")
    print()
    print("Option 3: Explicit HTTPS")
    print("   SEQRNG_IP_ADDRESS=https://**********")
    print("   CTM_IP_ADDRESS=https://**********")
    print()
    print("Option 4: With ports")
    print("   SEQRNG_IP_ADDRESS=http://**********:8080")
    print("   CTM_IP_ADDRESS=https://**********:8443")
    print()
    print("Option 5: Hostnames")
    print("   SEQRNG_IP_ADDRESS=https://seqrng.example.com")
    print("   CTM_IP_ADDRESS=https://ctm.example.com")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--examples":
        show_examples()
    else:
        if test_config():
            print("\n🎉 Configuration test passed!")
            print("🚀 You can now run: python SeQRNG_CTM_1.4.py")
        else:
            print("\n⚠️  Configuration test failed!")
            print("   Run: python test_config.py --examples")
            print("   to see configuration examples") 