#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CTM Key Manager - Script Principal
==================================

Script principal que proporciona un menú interactivo para gestionar llaves en
Thales CipherTrust Manager con material cuántico del SeQRNG.

Funcionalidades:
- Crear nuevas llaves
- Actualizar versiones de llaves existentes
- Listar llaves disponibles

Autor: Basado en SeQRNG_CTM_1.4.py
"""

import subprocess
import sys
import os
from typing import Optional

def print_header():
    """Print the main header"""
    print("🔐 SeQRNG - CTM Key Upload Manager")
    print("==================")
    print()

def print_menu():
    """Print the main menu"""
    print("Select an option:")
    print()
    print("1. 🔑 Create new key(s)")
    print("   Create a new key with quantum material from SeQRNG and upload to CTM")
    print()
    print("2. 🔄 Create new version of existing key")
    print("   Create a new version of an existing key with new quantum material")
    print()
    print("3. 📋 List available keys")
    print("   Show all available keys in CTM")
    print()
    print("4. 🔧 Check configuration & connection")
    print("   Verify SeQRNG and CTM configuration and connectivity")
    print()
    print("5. ❌ Exit")
    print()

def get_user_choice() -> Optional[int]:
    """Get user choice from menu"""
    try:
        choice = input("Option: ").strip()
        if choice == "5" or choice.lower() in ["exit", "quit"]:
            return 5
        return int(choice)
    except ValueError:
        return None

def validate_script_exists(script_name: str) -> bool:
    """Check if a script exists"""
    return os.path.exists(script_name)

def run_script(script_name: str, description: str, args: list = None) -> bool:
    """
    Run a script and handle errors
    
    Args:
        script_name (str): The name of the script to run.
        description (str): A description of what the script does.
        args (list, optional): A list of command-line arguments to pass to the script.
    """
    print(f"\n🚀 {description}")
    print("=" * 50)
    
    # Don't show execution info for ctm_list_keys.py and SeQRNG_CTM_1.4.py
    if script_name not in ['ctm_list_keys.py', 'SeQRNG_CTM_1.4.py']:
        print(f"🔍 Executing: {script_name}")
    
    try:
        # Use the virtual environment Python if available
        venv_python = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'venv', 'bin', 'python')
        if os.path.exists(venv_python):
            python_executable = venv_python
        else:
            python_executable = sys.executable
            
        # Build command with optional arguments
        command = [python_executable, script_name]
        if args:
            command.extend(args)
            
        # Run the script without check=True to handle exit codes manually
        result = subprocess.run(command, capture_output=False)
        
        # Don't show exit code for ctm_list_keys.py and SeQRNG_CTM_1.4.py
        if script_name not in ['ctm_list_keys.py', 'SeQRNG_CTM_1.4.py']:
            print(f"🔍 Script returned exit code: {result.returncode}")
        
        # Handle different exit codes
        if result.returncode == 0:
            # Don't show success message for ctm_list_keys.py and SeQRNG_CTM_1.4.py
            if script_name not in ['ctm_list_keys.py', 'SeQRNG_CTM_1.4.py']:
                print("\n✅ Operation completed successfully")
            return True
        elif result.returncode == 1:
            # Exit code 1 usually means user cancelled or operation failed gracefully
            print("\n⚠️  Operation was cancelled or failed gracefully")
            print("   This could be due to:")
            print("   • User cancelled the operation")
            print("   • QRNG connectivity issues")
            print("   • Configuration problems")
            return False
        elif result.returncode == 2:
            # Exit code 2 usually means key name already exists
            print("\n⚠️  Key name already exists or similar validation error")
            return False
        else:
            print(f"\n❌ Script exited with unexpected code: {result.returncode}")
            return False
            
    except FileNotFoundError:
        print(f"\n❌ Script not found: {script_name}")
        return False
    except KeyboardInterrupt:
        print(f"\n⚠️  Operation cancelled by user")
        return False
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        print(f"   Error type: {type(e).__name__}")
        return False

def suggest_list_keys() -> bool:
    """Suggest listing keys first"""
    print("\n💡 To update a key, you will need its ID.")
    response = input("Do you want to list available keys first? (y/n): ").strip().lower()
    return response in ['y', 'yes']

def wait_for_user():
    """Wait for user to press Enter"""
    input("\nPress Enter to continue...")

def main():
    """Main function"""
    # Check if required scripts exist
    required_scripts = {
        'SeQRNG_CTM_1.4.py': 'Script to create keys',
        'ctm_key_version_updater.py': 'Script to update versions',
        'ctm_list_keys.py': 'Script to list keys',
        'check_production_config.py': 'Script to check configuration'
    }
    
    missing_scripts = []
    for script, description in required_scripts.items():
        if not validate_script_exists(script):
            missing_scripts.append(f"   - {script}: {description}")
    
    if missing_scripts:
        print("❌ Required scripts not found:")
        for script in missing_scripts:
            print(script)
        print("\n🔧 Make sure all scripts are in the same directory.")
        sys.exit(1)
    
    print("✅ All required scripts found")
    
    while True:
        # Clear screen (optional)
        os.system('cls' if os.name == 'nt' else 'clear')
        
        # Print header and menu
        print_header()
        print_menu()
        
        # Get user choice
        choice = get_user_choice()
        
        if choice is None:
            print("❌ Invalid option. Please enter a number from 1 to 5.")
            wait_for_user()
            continue
        
        if choice == 1:
            # Create new key
            success = run_script('SeQRNG_CTM_1.4.py', 'Creating new key...')
            wait_for_user()
            
        elif choice == 2:
            # Update key version
            if suggest_list_keys():
                # Run list keys in update mode
                run_script('ctm_list_keys.py', 'Listing keys for update...', args=['--update-mode'])
            else:
                # Run the standalone update script if user declines listing
                run_script('ctm_key_version_updater.py', 'Updating key version...')
            wait_for_user()
            
        elif choice == 3:
            # List keys
            success = run_script('ctm_list_keys.py', 'Listing available keys...')
            wait_for_user()
            
        elif choice == 4:
            # Check configuration
            success = run_script('check_production_config.py', 'Checking configuration and connection...')
            wait_for_user()
            
        elif choice == 5:
            # Exit
            print("\n👋 Thank you for using SeQRNG - CTM Manager!")
            sys.exit(0)
            
        else:
            print("❌ Invalid option. Please enter a number from 1 to 5.")
            wait_for_user()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1) 