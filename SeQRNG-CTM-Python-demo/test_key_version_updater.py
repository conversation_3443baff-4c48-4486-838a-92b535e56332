#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Script for CTM Key Version Updater
=======================================

Script de prueba para verificar la funcionalidad del actualizador de versiones
de llaves en CTM.

Autor: Basado en SeQRNG_CTM_1.4.py
"""

import sys
import json
from unittest.mock import patch, MagicMock

# Import the functions to test
try:
    from ctm_key_version_updater import (
        load_configuration,
        ctm_create_key_version,
        ctm_get_key_info
    )
    from ctm_list_keys import ctm_list_keys, display_key_info
    print("✅ Módulos importados correctamente")
except ImportError as e:
    print(f"❌ Error importando módulos: {e}")
    sys.exit(1)

def test_configuration_loading():
    """Test configuration loading functionality"""
    print("\n🧪 Probando carga de configuración...")
    
    try:
        # This will use the actual config system
        seqrng_config, ctm_config = load_configuration()
        
        # Check that we got the expected structure
        assert 'base_url' in seqrng_config, "SeQRNG config missing base_url"
        assert 'api_token' in seqrng_config, "SeQRNG config missing api_token"
        assert 'base_url' in ctm_config, "CTM config missing base_url"
        assert 'username' in ctm_config, "CTM config missing username"
        assert 'password' in ctm_config, "CTM config missing password"
        assert 'domain' in ctm_config, "CTM config missing domain"
        
        print("✅ Configuración cargada correctamente")
        print(f"   📡 SeQRNG: {seqrng_config['base_url']}")
        print(f"   🔐 CTM: {ctm_config['base_url']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en carga de configuración: {e}")
        return False

def test_ctm_api_functions():
    """Test CTM API functions with mock data"""
    print("\n🧪 Probando funciones de API de CTM...")
    
    # Mock data
    mock_base_url = "https://ctm.example.com"
    mock_api_key = "test_api_key"
    mock_key_id = "test-key-id-123"
    mock_key_material = b"test_key_material_32_bytes_long"
    mock_algorithm = "aes"
    
    # Test ctm_create_key_version with mock
    with patch('ctm_key_version_updater.requests.post') as mock_post:
        # Mock successful response
        mock_response = MagicMock()
        mock_response.status_code = 201
        mock_response.json.return_value = {
            "id": mock_key_id,
            "version": 2,
            "createdAt": "2024-01-15T10:30:00Z"
        }
        mock_post.return_value = mock_response
        
        success = ctm_create_key_version(mock_base_url, mock_api_key, mock_key_id, mock_key_material, mock_algorithm)
        
        if success:
            print("✅ Función ctm_create_key_version funciona correctamente")
        else:
            print("❌ Función ctm_create_key_version falló")
            return False
    
    # Test ctm_get_key_info with mock
    with patch('ctm_key_version_updater.requests.get') as mock_get:
        # Mock successful response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "id": mock_key_id,
            "name": "test_key",
            "algorithm": "aes",
            "state": "Active",
            "version": 1,
            "createdAt": "2024-01-15T10:00:00Z"
        }
        mock_get.return_value = mock_response
        
        key_info = ctm_get_key_info(mock_base_url, mock_api_key, mock_key_id)
        
        if key_info and key_info.get('algorithm') == 'aes':
            print("✅ Función ctm_get_key_info funciona correctamente")
        else:
            print("❌ Función ctm_get_key_info falló")
            return False
    
    return True

def test_list_keys_functions():
    """Test list keys functions with mock data"""
    print("\n🧪 Probando funciones de listado de llaves...")
    
    # Mock data
    mock_base_url = "https://ctm.example.com"
    mock_api_key = "test_api_key"
    mock_keys = [
        {
            "id": "key-1",
            "name": "test_key_1",
            "algorithm": "aes",
            "state": "Active",
            "version": 1,
            "createdAt": "2024-01-15T10:00:00Z"
        },
        {
            "id": "key-2", 
            "name": "test_key_2",
            "algorithm": "hmac-sha256",
            "state": "Active",
            "version": 1,
            "createdAt": "2024-01-15T11:00:00Z"
        }
    ]
    
    # Test ctm_list_keys with mock
    with patch('ctm_list_keys.requests.get') as mock_get:
        # Mock successful response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "resources": mock_keys,
            "total": 2
        }
        mock_get.return_value = mock_response
        
        keys, total = ctm_list_keys(mock_base_url, mock_api_key, limit=10)
        
        if keys and len(keys) == 2 and total == 2:
            print("✅ Función ctm_list_keys funciona correctamente")
        else:
            print("❌ Función ctm_list_keys falló")
            return False
    
    # Test display_key_info
    try:
        display_key_info(mock_keys[0], show_details=False)
        print("✅ Función display_key_info funciona correctamente")
    except Exception as e:
        print(f"❌ Función display_key_info falló: {e}")
        return False
    
    return True

def test_material_formatting():
    """Test material formatting for different algorithms"""
    print("\n🧪 Probando formateo de material para diferentes algoritmos...")
    
    # Test data
    test_material = b"test_key_material_32_bytes_long"
    
    # Test hex encoding for symmetric algorithms
    hex_material = test_material.hex()
    if len(hex_material) == 64:  # 32 bytes = 64 hex chars
        print("✅ Formateo hexadecimal correcto para algoritmos simétricos")
    else:
        print("❌ Formateo hexadecimal incorrecto")
        return False
    
    # Test base64 encoding for asymmetric algorithms
    import base64
    b64_material = base64.b64encode(test_material).decode('utf-8')
    if len(b64_material) > 0:
        print("✅ Formateo base64 correcto para algoritmos asimétricos")
    else:
        print("❌ Formateo base64 incorrecto")
        return False
    
    return True

def run_all_tests():
    """Run all tests"""
    print("🧪 INICIANDO PRUEBAS DEL ACTUALIZADOR DE VERSIONES")
    print("=" * 50)
    
    tests = [
        ("Carga de configuración", test_configuration_loading),
        ("Funciones de API de CTM", test_ctm_api_functions),
        ("Funciones de listado de llaves", test_list_keys_functions),
        ("Formateo de material", test_material_formatting)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Ejecutando: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASÓ")
            else:
                print(f"❌ {test_name}: FALLÓ")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 RESULTADOS: {passed}/{total} pruebas pasaron")
    
    if passed == total:
        print("🎉 ¡Todas las pruebas pasaron! El sistema está listo para usar.")
        return True
    else:
        print("⚠️  Algunas pruebas fallaron. Revisa la configuración.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1) 