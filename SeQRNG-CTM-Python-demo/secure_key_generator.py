#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Secure Key Name Generator
========================

Generador seguro de nombres de claves usando entropía criptográfica
"""

import secrets
import hashlib
import time
import uuid
import re
from typing import List, Dict, Any
from datetime import datetime

class SecureKeyNameGenerator:
    """Generador seguro de nombres de claves"""
    
    def __init__(self):
        self.used_names = set()  # Cache de nombres usados
        self.max_attempts = 100
        self.min_name_length = 10
        self.min_entropy_ratio = 0.5
    
    def generate_secure_name(self, base_name: str, length: int = 16) -> str:
        """
        Generar nombre seguro usando entropía criptográfica
        
        Args:
            base_name: Nombre base para la clave
            length: Longitud del sufijo aleatorio
            
        Returns:
            str: Nombre seguro único
            
        Raises:
            ValueError: Si no se puede generar un nombre único
        """
        # Validar entrada
        if not base_name or not base_name.strip():
            raise ValueError("Base name cannot be empty")
        
        base_name = base_name.strip()
        length = max(8, min(length, 32))  # Limitar longitud entre 8 y 32
        
        for attempt in range(self.max_attempts):
            # Generar sufijo usando secrets (criptográficamente seguro)
            suffix = secrets.token_hex(length // 2)  # 2 chars por byte
            
            # Crear nombre completo
            full_name = f"{base_name}_{suffix}"
            
            # Verificar que no se haya usado
            if full_name not in self.used_names:
                self.used_names.add(full_name)
                return full_name
        
        raise ValueError(f"Could not generate unique name after {self.max_attempts} attempts")
    
    def generate_timestamped_name(self, base_name: str) -> str:
        """
        Generar nombre con timestamp para mayor unicidad
        
        Args:
            base_name: Nombre base para la clave
            
        Returns:
            str: Nombre con timestamp
        """
        if not base_name or not base_name.strip():
            raise ValueError("Base name cannot be empty")
        
        base_name = base_name.strip()
        # Usar formato con microsegundos para garantizar unicidad en operaciones rápidas
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        return f"{base_name}_{timestamp}"
    
    def generate_hash_based_name(self, base_name: str, data: bytes) -> str:
        """
        Generar nombre basado en hash de datos
        
        Args:
            base_name: Nombre base para la clave
            data: Datos para generar hash
            
        Returns:
            str: Nombre basado en hash
        """
        if not base_name or not base_name.strip():
            raise ValueError("Base name cannot be empty")
        
        if not data:
            raise ValueError("Data cannot be empty")
        
        base_name = base_name.strip()
        
        # Generar hash SHA-256 de los datos
        data_hash = hashlib.sha256(data).hexdigest()[:16]
        return f"{base_name}_{data_hash}"
    
    def generate_uuid_name(self, base_name: str) -> str:
        """
        Generar nombre usando UUID
        
        Args:
            base_name: Nombre base para la clave
            
        Returns:
            str: Nombre con UUID
        """
        if not base_name or not base_name.strip():
            raise ValueError("Base name cannot be empty")
        
        base_name = base_name.strip()
        unique_id = str(uuid.uuid4()).replace('-', '')[:16]
        return f"{base_name}_{unique_id}"
    
    def generate_sequential_secure_name(self, base_name: str, sequence_number: int) -> str:
        """
        Generar nombre secuencial pero seguro
        
        Args:
            base_name: Nombre base para la clave
            sequence_number: Número de secuencia
            
        Returns:
            str: Nombre secuencial seguro
        """
        if not base_name or not base_name.strip():
            raise ValueError("Base name cannot be empty")
        
        base_name = base_name.strip()
        
        # Formatear el número de secuencia con padding
        return f"{base_name}_{sequence_number:06d}"
    
    def validate_name_security(self, name: str) -> dict:
        """
        Validar seguridad de un nombre de clave
        
        Args:
            name: Nombre a validar
            
        Returns:
            dict: Resultado de validación
        """
        result = {
            'is_secure': True,
            'issues': [],
            'entropy_score': 0,
            'recommendations': []
        }
        
        if not name:
            result['is_secure'] = False
            result['issues'].append('Name is empty')
            return result
        
        # Verificar longitud mínima
        if len(name) < self.min_name_length:
            result['is_secure'] = False
            result['issues'].append(f'Name too short: {len(name)} < {self.min_name_length}')
            result['recommendations'].append('Increase name length')
        
        # Verificar si contiene solo números (predecible)
        if re.match(r'^.*_\d+$', name) and not re.search(r'[a-fA-F]', name.split('_')[-1]):
            result['is_secure'] = False
            result['issues'].append('Numeric suffix detected (predictable)')
            result['recommendations'].append('Use cryptographic random suffix')
        
        # Verificar si es completamente numérico
        if name.replace('_', '').isdigit():
            result['is_secure'] = False
            result['issues'].append('Name is purely numeric')
            result['recommendations'].append('Include alphabetic characters')
        
        # Calcular entropía aproximada
        unique_chars = len(set(name.lower()))
        result['entropy_score'] = unique_chars / len(name)
        
        if result['entropy_score'] < self.min_entropy_ratio:
            result['is_secure'] = False
            result['issues'].append(f'Low entropy detected: {result["entropy_score"]:.2f} < {self.min_entropy_ratio}')
            result['recommendations'].append('Use more diverse characters')
        
        # Verificar patrones predecibles
        if re.search(r'(test|demo|example|temp|tmp)', name.lower()):
            result['issues'].append('Contains predictable words')
            result['recommendations'].append('Avoid common words')
        
        # Verificar si ya fue usado
        if name in self.used_names:
            result['is_secure'] = False
            result['issues'].append('Name already used')
            result['recommendations'].append('Generate unique name')
        
        return result
    
    def generate_batch_names(self, base_name: str, count: int, method: str = 'secure', start_sequence_from: int = 1) -> List[str]:
        """
        Generar múltiples nombres seguros
        
        Args:
            base_name: Nombre base
            count: Número de nombres a generar
            method: Método de generación ('secure', 'timestamped', 'uuid', 'sequential')
            start_sequence_from: Número de inicio para el método secuencial
            
        Returns:
            List[str]: Lista de nombres generados
        """
        if count <= 0:
            raise ValueError("Count must be positive")
        
        if count > 1000:
            raise ValueError("Count too large, maximum 1000")
        
        names = []
        
        for i in range(count):
            try:
                if method == 'secure':
                    name = self.generate_secure_name(base_name)
                elif method == 'timestamped':
                    name = self.generate_timestamped_name(base_name)
                elif method == 'uuid':
                    name = self.generate_uuid_name(base_name)
                elif method == 'sequential':
                    sequence_number = start_sequence_from + i
                    name = self.generate_sequential_secure_name(base_name, sequence_number)
                else:
                    raise ValueError(f"Unknown method: {method}")
                
                names.append(name)
                
            except ValueError as e:
                print(f"Warning: Could not generate name {i+1}: {e}")
                # Continuar con el siguiente
        
        return names
    
    def clear_cache(self):
        """Limpiar cache de nombres usados"""
        self.used_names.clear()
    
    def get_cache_stats(self) -> dict:
        """Obtener estadísticas del cache"""
        return {
            'cached_names': len(self.used_names),
            'max_attempts': self.max_attempts,
            'min_name_length': self.min_name_length,
            'min_entropy_ratio': self.min_entropy_ratio
        }
    
    def suggest_secure_name(self, base_name: str) -> str:
        """
        Sugerir un nombre seguro basado en el nombre base
        
        Args:
            base_name: Nombre base
            
        Returns:
            str: Nombre seguro sugerido
        """
        # Validar y limpiar nombre base
        if not base_name or not base_name.strip():
            base_name = "key"
        else:
            base_name = base_name.strip()
        
        # Remover caracteres problemáticos
        base_name = re.sub(r'[^a-zA-Z0-9_-]', '_', base_name)
        base_name = base_name.strip('_')
        
        # Generar nombre seguro
        return self.generate_secure_name(base_name)
    
    def analyze_name_pattern(self, names: List[str]) -> dict:
        """
        Analizar patrón de nombres para detectar problemas
        
        Args:
            names: Lista de nombres a analizar
            
        Returns:
            dict: Análisis del patrón
        """
        if not names:
            return {'error': 'No names provided'}
        
        analysis = {
            'total_names': len(names),
            'unique_names': len(set(names)),
            'duplicates': len(names) - len(set(names)),
            'avg_length': sum(len(name) for name in names) / len(names),
            'security_issues': [],
            'recommendations': []
        }
        
        # Verificar duplicados
        if analysis['duplicates'] > 0:
            analysis['security_issues'].append(f'{analysis["duplicates"]} duplicate names found')
            analysis['recommendations'].append('Ensure name uniqueness')
        
        # Verificar longitud promedio
        if analysis['avg_length'] < self.min_name_length:
            analysis['security_issues'].append(f'Average length too short: {analysis["avg_length"]:.1f}')
            analysis['recommendations'].append('Increase name length')
        
        # Verificar patrones
        numeric_suffixes = sum(1 for name in names if re.match(r'^.*_\d+$', name))
        if numeric_suffixes > len(names) * 0.8:
            analysis['security_issues'].append('Too many numeric suffixes')
            analysis['recommendations'].append('Use cryptographic random suffixes')
        
        return analysis 