#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug Script para SeQRNG
=======================

Script para diagnosticar problemas de conexión con SeQRNG
"""

import requests
import json
import sys
from config import get_config

def test_seqrng_connection():
    """Probar conexión al SeQRNG y mostrar respuesta detallada"""
    
    print("🔍 Diagnóstico de Conexión SeQRNG")
    print("=" * 50)
    
    try:
        # Cargar configuración
        config = get_config()
        seqrng_config = config.get_seqrng_config()
        
        base_url = seqrng_config['ip_address']
        api_token = seqrng_config['api_token']
        ssl_verify = seqrng_config['ssl_verify']
        
        print(f"📡 Configuración SeQRNG:")
        print(f"   URL: {base_url}")
        print(f"   Token: {api_token[:10]}...")
        print(f"   SSL Verify: {ssl_verify}")
        
        # Construir URL de API
        if base_url.startswith(('http://', 'https://')):
            api_url = f"{base_url.rstrip('/')}/api/v1/get_data"
        else:
            api_url = f"https://{base_url}/api/v1/get_data"
        
        print(f"\n🌐 URL de API: {api_url}")
        
        # Preparar request
        headers = {'Authorization': f'Bearer {api_token}'}
        data = {
            'request_type': 'data',
            'package_size': 32,
            'package_total': 1,
            'output_type': 'base64',
            'self_testing_report': '1'
        }
        
        print(f"\n📤 Request:")
        print(f"   Headers: {headers}")
        print(f"   Data: {data}")
        
        # Hacer request
        print(f"\n🔄 Enviando request...")
        response = requests.post(api_url, headers=headers, data=data, verify=ssl_verify, timeout=10)
        
        print(f"\n📥 Response:")
        print(f"   Status Code: {response.status_code}")
        print(f"   Headers: {dict(response.headers)}")
        print(f"   Content Type: {response.headers.get('content-type', 'N/A')}")
        
        # Intentar parsear JSON
        try:
            content = json.loads(response.content)
            print(f"\n📋 JSON Response:")
            print(f"   Type: {type(content)}")
            print(f"   Keys: {list(content.keys()) if isinstance(content, dict) else 'No es dict'}")
            
            if isinstance(content, dict):
                print(f"   Content: {json.dumps(content, indent=2)}")
                
                # Analizar estructura
                if 'success' in content:
                    print(f"\n✅ Sección 'success' encontrada:")
                    print(f"   Keys: {list(content['success'].keys())}")
                    
                    if 'data' in content['success']:
                        print(f"   ✅ 'data' encontrado: {type(content['success']['data'])}")
                    elif 'base64_data' in content['success']:
                        print(f"   ✅ 'base64_data' encontrado: {type(content['success']['base64_data'])}")
                    else:
                        print(f"   ❌ Ni 'data' ni 'base64_data' encontrados")
                        
                elif 'error' in content:
                    print(f"\n❌ Error en respuesta:")
                    print(f"   Error: {content['error']}")
                else:
                    print(f"\n⚠️  Estructura inesperada")
                    
        except json.JSONDecodeError as e:
            print(f"\n❌ Error parseando JSON: {e}")
            print(f"   Raw content: {response.content}")
            
    except Exception as e:
        print(f"\n❌ Error inesperado: {e}")
        print(f"   Type: {type(e).__name__}")
        import traceback
        traceback.print_exc()

def test_different_output_types():
    """Probar diferentes tipos de output"""
    
    print(f"\n🧪 Probando Diferentes Tipos de Output")
    print("=" * 50)
    
    try:
        config = get_config()
        seqrng_config = config.get_seqrng_config()
        
        base_url = seqrng_config['ip_address']
        api_token = seqrng_config['api_token']
        ssl_verify = seqrng_config['ssl_verify']
        
        if base_url.startswith(('http://', 'https://')):
            api_url = f"{base_url.rstrip('/')}/api/v1/get_data"
        else:
            api_url = f"https://{base_url}/api/v1/get_data"
        
        headers = {'Authorization': f'Bearer {api_token}'}
        
        # Probar diferentes tipos de output
        output_types = ['base64', 'hex', 'alphanumeric']
        
        for output_type in output_types:
            print(f"\n📤 Probando output_type: {output_type}")
            
            data = {
                'request_type': 'data',
                'package_size': 16,
                'package_total': 1,
                'output_type': output_type,
                'self_testing_report': '1'
            }
            
            try:
                response = requests.post(api_url, headers=headers, data=data, verify=ssl_verify, timeout=10)
                response.raise_for_status()
                
                content = json.loads(response.content)
                print(f"   Status: {response.status_code}")
                print(f"   Keys: {list(content.keys()) if isinstance(content, dict) else 'No es dict'}")
                
                if isinstance(content, dict) and 'success' in content:
                    success_keys = list(content['success'].keys())
                    print(f"   Success keys: {success_keys}")
                    
                    # Mostrar el primer valor encontrado
                    for key in ['data', 'base64_data', 'hex_data', 'alphanumeric_data']:
                        if key in content['success']:
                            value = content['success'][key]
                            print(f"   {key}: {value[:50]}{'...' if len(str(value)) > 50 else ''}")
                            break
                            
            except Exception as e:
                print(f"   ❌ Error: {e}")
                
    except Exception as e:
        print(f"❌ Error en test de output types: {e}")

def main():
    """Función principal"""
    
    print("🚀 Debug de SeQRNG")
    print("=" * 50)
    
    # Test 1: Conexión básica
    test_seqrng_connection()
    
    # Test 2: Diferentes tipos de output
    test_different_output_types()
    
    print(f"\n🎯 Resumen:")
    print(f"   • Revisa la estructura de respuesta arriba")
    print(f"   • Verifica que el token sea válido")
    print(f"   • Confirma que el SeQRNG esté funcionando")
    print(f"   • Revisa la configuración SSL si es necesario")

if __name__ == "__main__":
    main() 