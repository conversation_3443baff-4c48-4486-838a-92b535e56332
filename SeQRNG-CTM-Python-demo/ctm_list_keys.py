#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CTM Key Lister
==============

Script auxiliar para listar las llaves disponibles en Thales CipherTrust Manager
y obtener sus IDs para usar con el script de actualización de versiones.

Autor: Basado en SeQRNG_CTM_1.4.py
"""

# Ensure we use the virtual environment Python if available
import sys
import os

# Check if we're running from the project directory and venv exists
project_dir = os.path.dirname(os.path.abspath(__file__))
venv_python = os.path.join(project_dir, 'venv', 'bin', 'python')

if os.path.exists(venv_python):
    # Use the virtual environment Python
    if sys.executable != venv_python:
        os.execv(venv_python, [venv_python] + sys.argv)

from urllib3.exceptions import InsecureRequestWarning
import requests
import json
import sys
import warnings

# Suppress SSL warnings for development
warnings.filterwarnings('ignore', category=InsecureRequestWarning)

from sq_ctm_modules_v1 import ctm_get_api_key
from config import get_config
from ctm_key_version_updater import update_key_version

def load_configuration():
    """
    Load and validate configuration from secure sources
    Returns configuration dictionaries for CTM
    """
    try:
        config = get_config()
        
        # Validate configuration
        if not config.validate_config():
            print("\n❌ Configuration validation failed!")
            print("\n🔧 Available setup options:")
            print("1. Run: python config.py --setup (interactive setup)")
            print("2. Run: python config.py --create-examples (create example files)")
            print("3. Set environment variables manually")
            print("4. Create .env file manually")
            sys.exit(1)
        
        # Get configuration
        ctm_config = config.get_ctm_config()
        
        return ctm_config
        
    except Exception as e:
        print(f"\n❌ Configuration error: {e}")
        print("\n🔧 To fix this, run one of the following:")
        print("  python config.py --setup")
        print("  python config.py --create-examples")
        sys.exit(1)

def ctm_list_keys(base_url, api_key, limit=100, offset=0, name_filter=None):
    """
    List keys from CTM with optional filtering
    
    Args:
        base_url (str): CTM base URL
        api_key (str): CTM API key
        limit (int): Maximum number of keys to return
        offset (int): Offset for pagination
        name_filter (str): Optional name filter
    
    Returns:
        tuple: (list of keys, total count, response info) or (None, 0, None) if error
    """
    headers = {'Authorization': f'Bearer {api_key}'}
    params = {
        'limit': limit,
        'offset': offset
    }
    
    if name_filter:
        params['name'] = name_filter
    
    # Use base_url directly if it's a complete URL, otherwise treat as IP for backward compatibility
    if base_url.startswith(('http://', 'https://')):
        api_url = f"{base_url.rstrip('/')}/api/v1/vault/keys2"
    else:
        # Backward compatibility: treat as IP address
        api_url = f"https://{base_url}/api/v1/vault/keys2"
    
    try:
        response = requests.get(api_url, headers=headers, params=params, verify=False)
        
        if response.status_code == 200:
            content = response.json()
            
            # Handle different response formats
            if isinstance(content, dict) and 'resources' in content:
                keys = content['resources']
                total = content.get('total', len(keys))
                response_info = {
                    'type': 'paginated',
                    'total': total,
                    'limit': content.get('limit'),
                    'offset': content.get('offset'),
                    'has_more': content.get('hasMore', False)
                }
            elif isinstance(content, list):
                keys = content
                total = len(keys)
                response_info = {
                    'type': 'list',
                    'total': total,
                    'limit': None,
                    'offset': None,
                    'has_more': False
                }
            else:
                keys = [content] if content else []
                total = len(keys)
                response_info = {
                    'type': 'single',
                    'total': total,
                    'limit': None,
                    'offset': None,
                    'has_more': False
                }
            
            # Filter out None values and ensure all keys are dictionaries
            # Allow keys with empty or None meta attributes
            if keys is not None:
                filtered_keys = []
                for key in keys:
                    if key is not None and isinstance(key, dict):
                        filtered_keys.append(key)
                    elif key is not None:
                        print(f"⚠️  Ignorando llave con formato inesperado: {type(key)}")
                    # Note: We don't filter out keys with empty meta - they are valid
                
                keys = filtered_keys
                response_info['filtered_total'] = len(keys)
            
            return keys, total, response_info
        else:
            print(f"❌ Failed to list keys: HTTP {response.status_code}")
            print(f"📋 Response: {response.text}")
            return None, 0, None
            
    except Exception as e:
        print(f"❌ Error listing keys: {e}")
        return None, 0, None

def display_key_info(key, show_details=False):
    """
    Display formatted key information
    
    Args:
        key (dict): Key information dictionary
        show_details (bool): Whether to show detailed information
    """
    # Validate that key is not None and is a dictionary
    if key is None:
        print("⚠️  Key with null data detected")
        print("-" * 50)
        return
    
    if not isinstance(key, dict):
        print(f"⚠️  Key with unexpected format: {type(key)}")
        print("-" * 50)
        return
    
    # Basic information (always shown)
    key_id = key.get('id', 'N/A')
    name = key.get('name', 'N/A')
    algorithm = key.get('algorithm', 'N/A')
    state = key.get('state', 'N/A')
    created_at = key.get('createdAt', 'N/A')
    version = key.get('version', 'N/A')
    size = key.get('size', 'N/A')
    object_type = key.get('objectType', 'N/A')
    
    # Check if key is archived based on archiveDate
    archive_date = key.get('archiveDate', None)
    is_archived = "📦 Archived" if archive_date else "✅ Active"
    
    print(f"🔑 ID: {key_id}")
    print(f"📝 Name: {name}")
    print(f"🔐 Algorithm: {algorithm}")
    print(f"📏 Size: {size} bits")
    print(f"📦 Type: {object_type}")
    print(f"📊 State: {state}")
    print(f"📅 Created: {created_at}")
    print(f"🔄 Version: {version}")
    print(f"📦 Status: {is_archived}")
    
    if show_details:
        # Detailed information
        account = key.get('account', 'N/A')
        application = key.get('application', 'N/A')
        usage_mask = key.get('usageMask', 'N/A')
        unexportable = key.get('unexportable', 'N/A')
        undeletable = key.get('undeletable', 'N/A')
        format_type = key.get('format', 'N/A')
        updated_at = key.get('updatedAt', 'N/A')
        
        print(f"👤 Account: {account}")
        print(f"📱 Application: {application}")
        print(f"🎯 Usage mask: {usage_mask}")
        print(f"🔒 Unexportable: {unexportable}")
        print(f"🗑️  Undeletable: {undeletable}")
        print(f"📋 Format: {format_type}")
        print(f"📝 Updated: {updated_at}")
        
        # Show aliases if available
        aliases = key.get('aliases', [])
        if aliases and isinstance(aliases, list):
            alias_names = []
            for alias in aliases:
                if isinstance(alias, dict):
                    alias_name = alias.get('alias', '')
                    if alias_name:
                        alias_names.append(alias_name)
            if alias_names:
                print(f"🏷️  Aliases: {', '.join(alias_names)}")
        
        # Show labels if available
        labels = key.get('labels', {})
        if labels and isinstance(labels, dict):
            label_items = []
            for label_key, label_value in labels.items():
                label_items.append(f"{label_key}: {label_value}")
            if label_items:
                print(f"🏷️  Labels: {', '.join(label_items)}")
        
        # Show description if available
        description = key.get('description', '')
        if description:
            print(f"📄 Description: {description}")
        
        # Show archive information if available
        if archive_date:
            print(f"📦 Archive date: {archive_date}")
    
    print("-" * 50)

def main():
    """
    Main function to list CTM keys
    """
    
    # Load secure configuration
    ctm_config = load_configuration()
    
    # Extract configuration values
    ctm_base_url = ctm_config['base_url'] 
    ctm_login_data = {
        "name": ctm_config['username'],
        "password": ctm_config['password'],
        "domain": ctm_config['domain']
    }
    
    # Get API key from CTM
    ctm_api_key = ctm_get_api_key(ctm_base_url, ctm_login_data)
    
    # Set default values
    name_filter = None
    offset = 0
    show_details = True
    
    # Ask for limit
    try:
        limit_input = input("📊 Maximum number of keys to show (Enter for 50): ").strip()
        limit = int(limit_input) if limit_input else 50
    except ValueError:
        limit = 50
    
    print(f"\n🔍 Searching for keys...")
    print()
    
    # List keys
    keys, total, response_info = ctm_list_keys(ctm_base_url, ctm_api_key, limit=limit, offset=offset, name_filter=name_filter)
    
    if keys is None:
        print("❌ Error getting the list of keys")
        sys.exit(1)
    
    if not keys:
        print("📭 No keys found matching the criteria")
        sys.exit(0)
    
    # Show response information
    if response_info:
        print(f"\n📊 Response information:")
        print(f"   Type: {response_info['type']}")
        print(f"   Total on server: {response_info['total']}")
        print(f"   Requested limit: {limit}")
        print(f"   Applied limit: {response_info.get('limit', 'N/A')}")
        print(f"   Offset: {response_info.get('offset', 'N/A')}")
        print(f"   Has more pages: {response_info.get('has_more', False)}")
        print(f"   After filtering: {response_info.get('filtered_total', len(keys))}")
        
        # Offer to get more keys if pagination is available
        if response_info.get('has_more', False):
            print(f"\n💡 More keys available. Total on server: {response_info['total']}")
            get_more = input("Get more keys? (y/n, Enter for no): ").strip().lower()
            if get_more == 'y':
                next_offset = offset + limit
                print(f"🔍 Getting keys from offset {next_offset}...")
                more_keys, more_total, more_info = ctm_list_keys(ctm_base_url, ctm_api_key, limit=limit, offset=next_offset, name_filter=name_filter)
                if more_keys:
                    keys.extend(more_keys)
                    print(f"✅ Added {len(more_keys)} more keys. Total: {len(keys)}")
                else:
                    print("❌ Could not get more keys")
    

    
    # Display results
    print(f"📊 Results: {len(keys)} of {total} keys found")
    
    # Show statistics about keys
    if keys:
        algorithm_stats = {}
        object_type_stats = {}
        state_stats = {}
        size_stats = {}
        archive_stats = {}
        
        for key in keys:
            # Count algorithms
            algorithm = key.get('algorithm', 'Unknown')
            algorithm_stats[algorithm] = algorithm_stats.get(algorithm, 0) + 1
            
            # Count object types
            object_type = key.get('objectType', 'Unknown')
            object_type_stats[object_type] = object_type_stats.get(object_type, 0) + 1
            
            # Count states
            state = key.get('state', 'Unknown')
            state_stats[state] = state_stats.get(state, 0) + 1
            
            # Count sizes
            size = key.get('size', 'Unknown')
            size_stats[size] = size_stats.get(size, 0) + 1
            
            # Count archive status
            archive_date = key.get('archiveDate', None)
            archive_status = "Archived" if archive_date else "Active"
            archive_stats[archive_status] = archive_stats.get(archive_status, 0) + 1
        
        print(f"\n📈 Statistics:")
        print(f"\n🔐 Algorithms:")
        for algo, count in algorithm_stats.items():
            print(f"   {algo}: {count}")
        
        print(f"\n📦 Object types:")
        for obj_type, count in object_type_stats.items():
            print(f"   {obj_type}: {count}")
        
        print(f"\n📊 States:")
        for state, count in state_stats.items():
            print(f"   {state}: {count}")
        
        print(f"\n📏 Sizes (bits):")
        for size, count in size_stats.items():
            print(f"   {size}: {count}")
        
        print(f"\n📦 Archive Status:")
        for status, count in archive_stats.items():
            print(f"   {status}: {count}")
    
    print("=" * 50)
    
    for i, key in enumerate(keys, 1):
        print(f"\n📋 Key #{i}")
        display_key_info(key, show_details)
    
    print(f"\n✅ Listing completed. {len(keys)} keys shown.")
    
    # Check if running in update mode from command line argument
    update_mode = "--update-mode" in sys.argv
    
    # Ask if user wants to update a key
    if keys:
        print("=" * 50)
        
        # If in update mode, skip the question and proceed
        if update_mode:
            print("🔧 Proceeding with key update...")
            update_choice = 'y'
        else:
            update_choice = input("🔧 Do you want to update a key with new quantum material? (y/n): ").strip().lower()

        if update_choice == 'y':
            while True:
                key_id_to_update = input("🔑 Enter the ID of the key to update: ").strip()
                
                # Validate that the ID exists in the listed keys
                if any(key['id'] == key_id_to_update for key in keys):
                    print("-" * 50)
                    update_key_version(key_id_to_update)
                    break
                else:
                    print(f"❌ Error: The ID '{key_id_to_update}' is not in the list of displayed keys.")
                    retry = input("Try again? (y/n): ").strip().lower()
                    if retry != 'y':
                        break
    else:
        if name_filter:
            print(f"\n💡 To use a key with the update script:")
            print(f"   python ctm_key_version_updater.py")
            print(f"   And use the ID of the desired key")

if __name__ == "__main__":
    main() 