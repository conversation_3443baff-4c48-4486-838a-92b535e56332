# 🚀 SeQRNG-CTM Deployment Guide

## 📋 Table of Contents

- [📋 Prerequisites](#-prerequisites)
- [🔧 Installation Steps](#-installation-steps)
- [🔐 Security Hardening](#-security-hardening)
- [🏃‍♂️ Running the Application](#️-running-the-application)
- [📊 Configuration Priority Order](#-configuration-priority-order)
- [🔍 Troubleshooting](#-troubleshooting)
- [📱 Monitoring and Maintenance](#-monitoring-and-maintenance)
- [🆘 Emergency Procedures](#-emergency-procedures)
- [📞 Support Information](#-support-information)

## 📋 Prerequisites

- Python 3.7 or higher
- Access to SeQRNG device
- Access to Thales CipherTrust Manager
- Network connectivity between server and both devices

## 🔧 Installation Steps

### 1. Server Setup

```bash
# Clone or copy the application files to your server
cd /opt/seqrng-ctm  # or your preferred directory

# Install Python dependencies
pip install -r requirements.txt
```

### 2. Configuration Setup

Choose one of the following configuration methods:

#### Option A: Interactive Setup (Recommended for first-time setup)
```bash
python config.py --setup
```

#### Option B: Using Environment Variables
```bash
# Set environment variables (recommended for production)
export SEQRNG_IP_ADDRESS="your.seqrng.ip.address"
export SEQRNG_API_TOKEN="your_seqrng_token"
export CTM_IP_ADDRESS="your.ctm.ip.address"
export CTM_USERNAME="your_ctm_username"
export CTM_PASSWORD="your_ctm_password"
export CTM_DOMAIN="your_ctm_domain"
```

#### Option C: Using .env File
```bash
# Create example files
python config.py --create-examples

# Copy and edit the .env file
cp .env.example .env
nano .env  # Edit with your actual credentials
```

#### Option D: Configuration File
```bash
# Create example files
python config.py --create-examples

# Copy and edit the config file
cp config.json.example config.json
nano config.json  # Edit with your actual credentials
```

### 3. Production Scripts Setup

```bash
# Generate production scripts (no virtual environment required)
./create_production_scripts.sh

# Verify scripts were created
ls -la production_scripts/
# Should show:
# - sq-ctm-manager.py (executable)
# - SeQRNG_CTM_1.4.py (executable)
# - ctm_list_keys.py (executable)
# - etc...
```

## 🔐 Security Hardening

### ✅ Implemented Security Features

1. **No Hardcoded Credentials**: All sensitive data is externalized
2. **Environment Variable Support**: Secure configuration management
3. **Configuration Validation**: Automatic validation of required settings
4. **Multiple Configuration Sources**: Flexibility for different deployment scenarios
5. **Git Ignore**: Sensitive files excluded from version control
6. **JWT Token Validation**: Automatic token expiration and validity checks
7. **Secure Key Name Generation**: Cryptographically secure key names
8. **Entropy Quality Validation**: Quantum material quality verification

### 🔒 Additional Security Recommendations

#### 1. Run as Non-Root User
```bash
# Create dedicated user
useradd -r -s /bin/false seqrng-ctm
chown -R seqrng-ctm:seqrng-ctm /opt/seqrng-ctm
```

#### 2. File Permissions
```bash
# Restrict access to configuration files
chmod 600 .env config.json
chown app_user:app_group .env config.json

# Protect production scripts
chmod 755 production_scripts/*
chmod 755 venv/bin/python
```

#### 3. Network Segmentation
- Place application in DMZ or isolated network segment
- Use VPN for remote access
- Implement network monitoring

#### 4. Certificate Management
- Use proper SSL/TLS certificates
- Implement certificate pinning if possible
- Regular certificate rotation

#### 5. Firewall Configuration
```bash
# Allow only necessary ports
# SeQRNG API (adjust port as needed)
ufw allow from <CTM_IP> to any port 443
# CTM API  
ufw allow from <SEQRNG_IP> to any port 443
```

#### 6. Logging and Monitoring
- Enable application logging
- Monitor for failed authentication attempts
- Set up alerts for unusual activity

#### 7. Backup and Recovery
- Regular backup of configuration files (encrypted)
- Document recovery procedures
- Test disaster recovery scenarios

## 🏃‍♂️ Running the Application

### Development/Testing
```bash
# Activate virtual environment
source venv/bin/activate

# Run main script
python sq-ctm-manager.py

# Or run individual scripts
python SeQRNG_CTM_1.4.py
python ctm_list_keys.py
python ctm_key_version_updater.py
```

### Production (Recommended)
```bash
# Use production scripts directly (no venv activation needed)
./production_scripts/sq-ctm-manager.py
./production_scripts/SeQRNG_CTM_1.4.py
./production_scripts/ctm_list_keys.py
./production_scripts/ctm_key_version_updater.py
```

### Production with Systemd Service
Create `/etc/systemd/system/seqrng-ctm.service`:

```ini
[Unit]
Description=SeQRNG CTM Integration Service
After=network.target

[Service]
Type=simple
User=seqrng-ctm
Group=seqrng-ctm
WorkingDirectory=/opt/seqrng-ctm
Environment=PYTHONPATH=/opt/seqrng-ctm
ExecStart=/usr/bin/python3 /opt/seqrng-ctm/SeQRNG_CTM_1.4.py
Restart=always
RestartSec=10

# Security settings
NoNewPrivileges=yes
ProtectSystem=strict
ProtectHome=yes
ReadWritePaths=/opt/seqrng-ctm/logs

[Install]
WantedBy=multi-user.target
```

Start the service:
```bash
systemctl daemon-reload
systemctl enable seqrng-ctm
systemctl start seqrng-ctm
```

### Global Installation (Optional)
```bash
# Make scripts available globally
sudo cp production_scripts/* /usr/local/bin/

# Now can be run from anywhere
sq-ctm-manager.py
SeQRNG_CTM_1.4.py
ctm_list_keys.py
```

### Add to PATH
```bash
# Add to user PATH
echo 'export PATH=$PATH:/path/to/SQQ-CTM/production_scripts' >> ~/.bashrc
source ~/.bashrc

# Or add to system PATH
echo 'export PATH=$PATH:/path/to/SQQ-CTM/production_scripts' | sudo tee -a /etc/environment
```

## 📊 Configuration Priority Order

The application loads configuration in this order (highest to lowest precedence):

1. **Environment Variables** (highest priority)
2. **.env File**
3. **config.json File** (lowest priority)

## 🔍 Troubleshooting

### Configuration Issues

#### Testing Configuration
```bash
# Test configuration loading
python config.py --create-examples
python -c "from config import get_config; config=get_config(); config.validate_config()"

# Check production configuration
./production_scripts/check_production_config.py

# Run comprehensive configuration fix test
python test_config_fix.py
```

#### Configuration Access Problems
**Problem**: `'Config' object has no attribute 'seqrng'`

**Solution**: The application uses dictionary-style access to configuration:
```python
# ❌ Incorrect (old style)
sq_base_url = config.seqrng.ip_address

# ✅ Correct (new style)
seqrng_config = config.get_seqrng_config()
sq_base_url = seqrng_config['ip_address']
```

### Connection Issues

#### Network Connectivity
```bash
# Test network connectivity
ping <SEQRNG_IP>
ping <CTM_IP>
telnet <SEQRNG_IP> 443
telnet <CTM_IP> 443

# Debug CTM connection with detailed output
python debug_ctm.py

# Debug SeQRNG connection
python debug_seqrng.py
```

#### SeQRNG Connection Timeouts
**Problem**: SeQRNG connection times out or fails

**Improvements Implemented**:
- Reduced timeout from 30s to 10s for faster feedback
- Enhanced error messages with specific connection details
- User confirmation required before PRNG fallback

```bash
# Test QRNG fallback improvements
python test_qrng_fallback.py
```

### Authentication Issues

#### Error 401 - CTM Authentication
**Problem**: `CTM authentication failed: HTTP 401`

**Enhanced Solution**: The CTM authentication has been improved to handle multiple response formats:

1. **Run detailed diagnostics**:
   ```bash
   python debug_ctm.py
   ```

2. **Verify credentials** in your configuration:
   - Check `.env` file or environment variables
   - Verify username, password, and domain are correct
   - Ensure protocol (HTTP vs HTTPS) matches your CTM setup

3. **Test connectivity**:
   ```bash
   ping <CTM_IP>
   telnet <CTM_IP> 443
   ```

4. **Check CTM response format**: The improved authentication handles:
   - JSON responses with `token`, `access_token`, `api_key`, etc.
   - Plain text responses (fallback method)
   - Various HTTP status codes with descriptive errors

#### JWT Token Validation Issues
**New Security Feature**: JWT tokens are now validated for expiration and integrity.

**Symptoms**: Warning messages about expired or invalid tokens

**Solutions**:
```bash
# Check JWT validation
python -c "from jwt_validator import is_token_expired; print('JWT validation available')"

# Test security improvements
python test_security_improvements.py
```

### Key Management Issues

#### Duplicate Key Protection
**Problem**: Attempting to create a key that already exists

**Enhanced Protection**: The system now prevents accidental key overwrites:

1. **No automatic overwrite** - System blocks overwrite attempts
2. **Clear options provided**:
   - Option 1: Enter a new key name
   - Option 2: Go to main menu to create new version
   - Option 3: Cancel operation

```bash
# Test existing key handling
python test_existing_key_handling.py
```

#### Key Name Generation Security
**Enhancement**: Key names are now generated using cryptographically secure methods:

- **Before**: Predictable names using `random.randint()`
- **After**: Secure names using `secrets.token_hex()`

**Testing**:
```bash
# Verify secure key generation
python -c "from secure_key_generator import generate_secure_name; print('Secure generation available')"
```

### Quality and Security Validation

#### Entropy Quality Warnings
**New Feature**: The system now validates the quality of random data from SeQRNG/PRNG.

**Metrics Checked**:
- Shannon entropy
- Chi-squared distribution test
- Run tests for pattern detection
- Overall quality score (0-100)

**If you see entropy warnings**:
1. Check SeQRNG device status
2. Verify network connection quality
3. Consider using PRNG fallback for testing only

```bash
# Test entropy validation
python -c "from entropy_validator import validate_entropy_quality; print('Entropy validation available')"
```

### SSL Certificate Issues

#### Development Environment
```bash
# Disable SSL verification for development
export VERIFY_SSL=false
export ENVIRONMENT=development
```

#### Production Environment
```bash
# Enable SSL with proper certificates
export VERIFY_SSL=true
export ENVIRONMENT=production

# Custom certificate paths (if needed)
export SEQRNG_CERT_PATH="/path/to/custom/cert.crt"
export CTM_CERT_PATH="/path/to/ctm/cert.crt"
```

#### SSL Configuration Test
```bash
# Test SSL configuration
python test_ssl_config.py
```

### Virtual Environment Issues

#### Production Scripts (Recommended)
```bash
# Use production scripts (no manual venv activation needed)
./production_scripts/sq-ctm-manager.py
./production_scripts/SeQRNG_CTM_1.4.py
```

#### Manual Virtual Environment
```bash
# Activate manually if needed
source venv/bin/activate
python sq-ctm-manager.py

# Or use direct path
./venv/bin/python sq-ctm-manager.py
```

### Common Error Solutions

#### Error: "Permission denied"
```bash
# Check and fix script permissions
ls -la production_scripts/
chmod +x production_scripts/*

# Verify virtual environment
ls -la venv/bin/python
chmod +x venv/bin/python
```

#### Error: "No module named 'jwt'"
```bash
# Install missing security dependencies
./venv/bin/pip install PyJWT cryptography

# Verify all dependencies
./venv/bin/python -c "import jwt, cryptography, secrets; print('All security modules OK')"

# Reinstall all requirements
./venv/bin/pip install -r requirements.txt
```

#### Error: "Script not found"
```bash
# Verify original scripts exist
ls -la *.py

# Regenerate production scripts
./create_production_scripts.sh

# Verify production scripts were created
ls -la production_scripts/
```

#### Error: "IndexError: list index out of range" (CTM)
**Problem**: Old CTM parsing logic failed with unexpected response format

**Solution**: This has been fixed in the latest version. The CTM authentication now:
- Tries JSON parsing first
- Falls back to text parsing with validation
- Provides detailed error messages

```bash
# Test the CTM fix
python test_ctm_fix.py
```

### Security-Related Issues

#### JWT Validation Errors
```bash
# Check if PyJWT is available
python -c "import jwt; print('PyJWT available')"

# Test JWT validation functionality
python test_security_improvements.py
```

#### Entropy Validation Warnings
```bash
# Check entropy validator
python -c "from entropy_validator import validate_entropy_quality; print('Entropy validator available')"

# Test with sample data
python test_security_improvements.py
```

#### Secure Key Generation Issues
```bash
# Verify secrets module (Python 3.6+)
python -c "import secrets; print('Secrets module available')"

# Test secure key generation
python -c "from secure_key_generator import generate_secure_name; print(generate_secure_name('test'))"
```

### Performance Issues

#### Slow SeQRNG Connections
- **Timeout reduced**: From 30s to 10s for faster feedback
- **Connection pooling**: Reuse connections when possible
- **Better error reporting**: Specific timeout vs connection errors

#### Large Key Operations
- **Batch processing**: Process multiple keys efficiently
- **Progress indicators**: Show progress for long operations
- **Memory management**: Efficient handling of large key data

## 📱 Monitoring and Maintenance

### Log Monitoring
```bash
# Create log directory
mkdir -p /var/log/sqq-ctm
chmod 755 /var/log/sqq-ctm

# Run with logging
./production_scripts/sq-ctm-manager.py 2>&1 | tee /var/log/sqq-ctm/$(date +%Y%m%d_%H%M%S).log
```

### Performance Monitoring
- Monitor CPU and memory usage
- Track response times
- Monitor network connections

### Security Monitoring
- Monitor failed authentication attempts
- Track configuration changes
- Monitor for unusual network activity

### Automation (Cron Jobs)
```bash
# Add to crontab
crontab -e

# Verify configuration daily at 6 AM
0 6 * * * /path/to/SQQ-CTM/production_scripts/check_production_config.py >> /var/log/sqq-ctm/daily_check.log 2>&1

# Backup configuration daily at 2 AM
0 2 * * * cp /path/to/SQQ-CTM/.env /backup/sqq-ctm/.env.$(date +\%Y\%m\%d)
```

## 🆘 Emergency Procedures

### Lost Configuration
1. Stop the application
2. Recreate configuration using interactive setup
3. Verify configuration
4. Restart application

### Security Breach
1. Immediately stop the application
2. Change all credentials (SeQRNG tokens, CTM passwords)
3. Review logs for suspicious activity
4. Update configuration with new credentials
5. Restart application with monitoring

### System Failure
1. Check system resources (disk, memory, network)
2. Review application logs
3. Verify configuration files
4. Restart application
5. If issues persist, contact support

## 📞 Support Information

For technical support:
- Check logs in `/opt/seqrng-ctm/logs/`
- Review this deployment guide
- Contact system administrator
- Document any issues for troubleshooting

### Verification Commands
```bash
# Check configuration
./production_scripts/check_production_config.py

# Debug CTM connection
python debug_ctm.py

# Test SSL configuration
python test_ssl_config.py

# Verify security improvements
./production_scripts/test_security_improvements.py

# Test specific fixes
python test_config_fix.py         # Configuration access fix
python test_ctm_fix.py           # CTM authentication fix
python test_qrng_fallback.py     # QRNG fallback improvements
python test_existing_key_handling.py  # Key protection features
```

### Security Verification Checklist
- [ ] ✅ **JWT Validation**: `python -c "import jwt; print('PyJWT available')"`
- [ ] ✅ **Secure Key Names**: `python -c "import secrets; print('Secrets available')"`
- [ ] ✅ **Entropy Validation**: `python -c "from entropy_validator import validate_entropy_quality; print('OK')"`
- [ ] ✅ **Configuration Fix**: `python test_config_fix.py`
- [ ] ✅ **CTM Authentication Fix**: `python test_ctm_fix.py`
- [ ] ✅ **Key Protection**: `python test_existing_key_handling.py`
- [ ] ✅ **QRNG Fallback**: `python test_qrng_fallback.py`

### Production Checklist
- [ ] ✅ Virtual environment configured
- [ ] ✅ Dependencies installed (including PyJWT, cryptography)
- [ ] ✅ Production scripts generated
- [ ] ✅ Environment variables configured
- [ ] ✅ SSL certificates configured
- [ ] ✅ File permissions correct
- [ ] ✅ Security tests passing
- [ ] ✅ Main script working
- [ ] ✅ Logs configured
- [ ] ✅ Configuration backup
- [ ] ✅ Security improvements verified

## 🔐 Security Improvements Summary

### Implemented Enhancements
The system now includes these critical security features:

1. **JWT Token Validation**
   - Automatic validation of token expiration
   - Warning messages for expired tokens
   - Graceful handling of invalid tokens

2. **Secure Key Name Generation**
   - Cryptographically secure random names
   - Eliminates predictable patterns
   - Multiple generation methods available

3. **Entropy Quality Validation**
   - Shannon entropy analysis
   - Chi-squared distribution tests
   - Quality scoring (0-100 scale)
   - Detection of low-quality randomness

4. **Enhanced Error Handling**
   - Improved CTM authentication robustness
   - Better SeQRNG connection management
   - Clearer error messages and guidance

5. **Key Protection Features**
   - Prevention of accidental key overwrites
   - Clear options when duplicates detected
   - Safe key versioning workflow

### Security Test Suite
Run the comprehensive security test suite:
```bash
python test_security_improvements.py
```

This will verify:
- JWT validation functionality
- Secure key name generation
- Entropy quality validation
- Integration with existing systems

## 🚨 Important Security Notes

### For Production Deployment
- **Always use HTTPS** in production environments
- **Enable SSL verification** (`VERIFY_SSL=true`)
- **Use strong, unique credentials** for all services
- **Regularly rotate API tokens** and passwords
- **Monitor logs** for authentication failures
- **Backup configuration files** securely (encrypted)

### Security Monitoring
- Watch for JWT expiration warnings
- Monitor entropy quality scores
- Check for failed authentication attempts
- Review key creation patterns
- Verify SSL certificate validity

---

**🎯 Summary**

**For production:**
1. **NO activate venv manually** ✅
2. **Use production scripts** ✅
3. **Configure environment variables** ✅
4. **Verify SSL certificates** ✅
5. **Run security tests** ✅

**Main command:**
```bash
./production_scripts/sq-ctm-manager.py
```

**Security verification:**
```bash
python test_security_improvements.py
```

Your system is ready for secure production deployment with enhanced security features! 🚀🔐 