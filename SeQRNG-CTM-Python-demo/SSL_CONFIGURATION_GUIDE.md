# 🔒 Guía de Configuración SSL

Esta guía explica cómo configurar la verificación SSL para las conexiones SeQRNG-CTM en diferentes entornos.

## 📋 Índice

1. [Configuración Básica](#configuración-básica)
2. [Entornos de Desarrollo](#entornos-de-desarrollo)
3. [Entornos de Producción](#entornos-de-producción)
4. [Configuración de Certificados](#configuración-de-certificados)
5. [Solución de Problemas](#solución-de-problemas)
6. [Ejemplos de Configuración](#ejemplos-de-configuración)

## 🚀 Configuración Básica

### Variables de Entorno Principales

```bash
# Tipo de entorno
ENVIRONMENT=development|production

# Habilitar/deshabilitar verificación SSL
VERIFY_SSL=true|false

# Rutas de certificados (opcional)
SEQRNG_CERT_PATH=/path/to/seqrng-cert.crt
CTM_CERT_PATH=/path/to/ctm-cert.crt
```

### Comportamiento por Defecto

| Entorno | VERIFY_SSL | SeQRNG | CTM |
|---------|------------|--------|-----|
| Development | true | Sistema CA | Sistema CA |
| Development | false | Deshabilitado | Deshabilitado |
| Production | true | `/etc/pki/tls/certs/apache-selfsigned.crt` | Sistema CA |
| Production | false | Deshabilitado | Deshabilitado |

## 🧪 Entornos de Desarrollo

### Configuración Mínima

```bash
# .env file
ENVIRONMENT=development
VERIFY_SSL=true

SEQRNG_IP_ADDRESS=localhost
SEQRNG_API_TOKEN=your_token

CTM_IP_ADDRESS=https://ctm.example.com
CTM_USERNAME=your_username
CTM_PASSWORD=your_password
CTM_DOMAIN=your_domain
```

### Con Certificado Personalizado

```bash
# .env file
ENVIRONMENT=development
VERIFY_SSL=true

SEQRNG_IP_ADDRESS=localhost
SEQRNG_API_TOKEN=your_token
SEQRNG_CERT_PATH=/path/to/your/local/certificate.crt

CTM_IP_ADDRESS=https://ctm.example.com
CTM_USERNAME=your_username
CTM_PASSWORD=your_password
CTM_DOMAIN=your_domain
CTM_CERT_PATH=/path/to/ctm-certificate.crt
```

### SSL Deshabilitado (Solo para Desarrollo)

```bash
# .env file
ENVIRONMENT=development
VERIFY_SSL=false

# ... resto de configuración
```

## 🏭 Entornos de Producción

### Configuración Estándar

```bash
# .env file
ENVIRONMENT=production
VERIFY_SSL=true

SEQRNG_IP_ADDRESS=https://seqrng.example.com
SEQRNG_API_TOKEN=your_token

CTM_IP_ADDRESS=https://ctm.example.com
CTM_USERNAME=your_username
CTM_PASSWORD=your_password
CTM_DOMAIN=your_domain
```

### Certificado de Producción

En producción, el sistema automáticamente busca el certificado de SeQRNG en:
```
/etc/pki/tls/certs/apache-selfsigned.crt
```

Si el certificado está en una ubicación diferente, puedes especificarlo:

```bash
# .env file
ENVIRONMENT=production
VERIFY_SSL=true

SEQRNG_IP_ADDRESS=https://seqrng.example.com
SEQRNG_API_TOKEN=your_token
SEQRNG_CERT_PATH=/custom/path/to/certificate.crt

# ... resto de configuración
```

## 📜 Configuración de Certificados

### Tipos de Certificados Soportados

1. **Certificados de Autoridad Certificadora (CA)**
   - Let's Encrypt
   - DigiCert
   - GlobalSign
   - Certificados corporativos

2. **Certificados Autofirmados**
   - Certificados locales
   - Certificados de desarrollo

3. **Certificados de Sistema**
   - Certificados CA del sistema operativo
   - Certificados de certifi

### Obtención de Certificados

#### Para SeQRNG (Producción)
```bash
# El certificado ya debe estar en:
/etc/pki/tls/certs/apache-selfsigned.crt

# Verificar que existe:
ls -la /etc/pki/tls/certs/apache-selfsigned.crt
```

#### Para CTM
```bash
# Obtener certificado del servidor CTM
openssl s_client -connect ctm.example.com:443 -showcerts </dev/null 2>/dev/null | openssl x509 -outform PEM > ctm-cert.pem

# Configurar en .env
CTM_CERT_PATH=/path/to/ctm-cert.pem
```

### Verificación de Certificados

```bash
# Verificar formato del certificado
openssl x509 -in certificate.crt -text -noout

# Verificar que el certificado es válido
openssl verify certificate.crt
```

## 🔧 Solución de Problemas

### Error: SSL Certificate Verification Failed

**Síntomas:**
```
SSLError: [SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed
```

**Soluciones:**

1. **Verificar que el certificado existe:**
   ```bash
   ls -la /etc/pki/tls/certs/apache-selfsigned.crt
   ```

2. **Verificar formato del certificado:**
   ```bash
   openssl x509 -in /etc/pki/tls/certs/apache-selfsigned.crt -text -noout
   ```

3. **Usar certificado personalizado:**
   ```bash
   # En .env
   SEQRNG_CERT_PATH=/path/to/correct/certificate.crt
   ```

4. **Deshabilitar SSL temporalmente (solo desarrollo):**
   ```bash
   # En .env
   VERIFY_SSL=false
   ```

### Error: certifi not available

**Síntomas:**
```
ImportError: No module named 'certifi'
```

**Solución:**
```bash
pip install certifi
```

### Error: Connection Refused

**Síntomas:**
```
ConnectionError: [Errno 111] Connection refused
```

**Soluciones:**

1. **Verificar que el servicio está ejecutándose:**
   ```bash
   # Para SeQRNG
   systemctl status apache2
   
   # Para CTM
   # Verificar conectividad al servidor CTM
   ```

2. **Verificar firewall:**
   ```bash
   # Verificar puertos abiertos
   netstat -tlnp | grep :443
   ```

## 📝 Ejemplos de Configuración

### Desarrollo Local

```bash
# .env
ENVIRONMENT=development
VERIFY_SSL=true

SEQRNG_IP_ADDRESS=localhost
SEQRNG_API_TOKEN=1|dev_token_123

CTM_IP_ADDRESS=https://dev-ctm.company.com
CTM_USERNAME=dev_user
CTM_PASSWORD=dev_password
CTM_DOMAIN=Development
```

### Desarrollo con Certificados

```bash
# .env
ENVIRONMENT=development
VERIFY_SSL=true

SEQRNG_IP_ADDRESS=https://dev-seqrng.company.com
SEQRNG_API_TOKEN=1|dev_token_123
SEQRNG_CERT_PATH=/home/<USER>/certs/dev-seqrng.crt

CTM_IP_ADDRESS=https://dev-ctm.company.com
CTM_USERNAME=dev_user
CTM_PASSWORD=dev_password
CTM_DOMAIN=Development
CTM_CERT_PATH=/home/<USER>/certs/dev-ctm.crt
```

### Producción

```bash
# .env
ENVIRONMENT=production
VERIFY_SSL=true

SEQRNG_IP_ADDRESS=https://seqrng.company.com
SEQRNG_API_TOKEN=1|prod_token_456

CTM_IP_ADDRESS=https://ctm.company.com
CTM_USERNAME=prod_user
CTM_PASSWORD=prod_password
CTM_DOMAIN=Production
```

### Producción con Certificados Personalizados

```bash
# .env
ENVIRONMENT=production
VERIFY_SSL=true

SEQRNG_IP_ADDRESS=https://seqrng.company.com
SEQRNG_API_TOKEN=1|prod_token_456
SEQRNG_CERT_PATH=/etc/ssl/certs/seqrng-custom.crt

CTM_IP_ADDRESS=https://ctm.company.com
CTM_USERNAME=prod_user
CTM_PASSWORD=prod_password
CTM_DOMAIN=Production
CTM_CERT_PATH=/etc/ssl/certs/ctm-custom.crt
```

## 🧪 Pruebas

### Script de Prueba SSL

```bash
# Probar configuración SSL
python test_ssl_config.py
```

### Script de Debug CTM

```bash
# Probar conexión a CTM
python debug_ctm.py
```

### Script Principal

```bash
# Probar funcionalidad completa
python SeQRNG_CTM_1.4.py
```

## 🔒 Consideraciones de Seguridad

### ✅ Buenas Prácticas

1. **Siempre usar HTTPS en producción**
2. **Verificar certificados en producción**
3. **Usar certificados de CA confiables**
4. **Rotar certificados regularmente**
5. **Monitorear expiración de certificados**

### ❌ Malas Prácticas

1. **Deshabilitar SSL en producción**
2. **Usar certificados autofirmados en producción**
3. **Ignorar errores de certificados**
4. **Usar certificados expirados**
5. **Compartir certificados entre entornos**

## 📞 Soporte

Si encuentras problemas con la configuración SSL:

1. Ejecuta `python test_ssl_config.py` para diagnosticar
2. Verifica los logs del sistema
3. Contacta al equipo de infraestructura
4. Revisa la documentación del proveedor de certificados

---

**Nota:** Esta configuración SSL es compatible con versiones anteriores del código. Los scripts existentes seguirán funcionando con `verify=False` como fallback. 