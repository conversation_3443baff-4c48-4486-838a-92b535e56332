#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Check production configuration and debug 401 errors
"""

import os
import sys

def check_env_file():
    """Check if .env file exists and show its contents"""
    print("🔍 Checking .env file...")
    
    if os.path.exists('.env'):
        print("✅ .env file found")
        print("\n📋 Current .env contents:")
        print("-" * 40)
        with open('.env', 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    # Hide sensitive data for security
                    if 'PASSWORD' in line or 'TOKEN' in line:
                        key, value = line.split('=', 1)
                        print(f"{key}=***HIDDEN***")
                    else:
                        print(line)
        print("-" * 40)
        return True
    else:
        print("❌ .env file not found")
        return False

def check_configuration():
    """Check the loaded configuration"""
    print("\n🔍 Checking loaded configuration...")
    
    try:
        from config import get_config
        config = get_config()
        
        seqrng_config = config.get_seqrng_config()
        ctm_config = config.get_ctm_config()
        
        print("✅ Configuration loaded successfully")
        print(f"📡 SeQRNG: {seqrng_config['base_url']}")
        print(f"🔐 CTM: {ctm_config['base_url']}")
        print(f"👤 CTM Username: {ctm_config['username']}")
        print(f"🏢 CTM Domain: {ctm_config['domain']}")
        
        return True, config
        
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False, None

def test_qrng_connection(config):
    """Test QRNG connection with current configuration"""
    print("\n🔍 Testing QRNG connection...")
    
    try:
        from interface_seqrng_v2 import sq_get_random_bytes
        
        seqrng_config = config.get_seqrng_config()
        
        print(f"📡 Testing connection to: {seqrng_config['base_url']}")
        print(f"🔑 Using API token: {'***HIDDEN***' if seqrng_config['api_token'] else '❌ NOT SET'}")
        
        # Try to get a small amount of random bytes (1 byte) to test connectivity
        print("🌊 Requesting 32 bytes from QRNG...")
        
        qrng_key, errstr, etystr, etystatus = sq_get_random_bytes(
            32,   # 1 byte for testing
            1,   # 1 package
            seqrng_config['base_url'],
            seqrng_config['api_token'],
            seqrng_config['ssl_verify']
        )
        
        if qrng_key and len(qrng_key) == 32:
            print("✅ QRNG connection successful!")
            print(f"   Received: {len(qrng_key)} byte(s)")
            print(f"   Entropy status: {etystatus}")
            return True
        else:
            print("❌ QRNG connection failed!")
            print(f"   Error: {errstr}")
            print(f"   Entropy status: {etystatus}")
            return False
            
    except Exception as e:
        print(f"❌ QRNG connection test error: {e}")
        return False

def test_ctm_connection():
    """Test CTM connection with current configuration"""
    print("\n🔍 Testing CTM connection...")
    
    try:
        from debug_ctm import test_ctm_connection
        from config import get_config
        
        config = get_config()
        ctm_config = config.get_ctm_config()
        
        success = test_ctm_connection(
            ctm_config['base_url'],
            ctm_config['username'],
            ctm_config['password'],
            ctm_config['domain']
        )
        
        return success
        
    except Exception as e:
        print(f"❌ Connection test error: {e}")
        return False

def show_qrng_troubleshooting():
    """Show troubleshooting steps for QRNG connection issues"""
    print("\n🔧 QRNG Connection Troubleshooting Steps:")
    print("=" * 50)
    print()
    print("1. ✅ Verify QRNG credentials:")
    print("   - Check SEQRNG_API_TOKEN in .env file")
    print("   - Ensure token is valid and not expired")
    print()
    print("2. ✅ Verify QRNG URL:")
    print("   - Check if SEQRNG_IP_ADDRESS is correct")
    print("   - Try with/without protocol (http:// or https://)")
    print("   - Verify port number if specified")
    print()
    print("3. ✅ Check network connectivity:")
    print("   - ping <qrng_ip>")
    print("   - telnet <qrng_ip> <port>")
    print("   - curl -X GET <qrng_url>/api/v1/status")
    print()
    print("4. ✅ Verify QRNG service:")
    print("   - Check if QRNG service is running")
    print("   - Check if API endpoint is accessible")
    print("   - Verify QRNG is generating entropy")
    print()
    print("5. ✅ Common solutions:")
    print("   - Try HTTP instead of HTTPS: SEQRNG_IP_ADDRESS=http://**************")
    print("   - Try HTTPS with port: SEQRNG_IP_ADDRESS=https://**************:1982")
    print("   - Check if API token has correct permissions")
    print("   - Verify QRNG is not in maintenance mode")
    print()

def show_401_troubleshooting():
    """Show troubleshooting steps for 401 errors"""
    print("\n🔧 401 Error Troubleshooting Steps:")
    print("=" * 50)
    print()
    print("1. ✅ Verify credentials:")
    print("   - Check username and password in .env file")
    print("   - Ensure domain is correct")
    print()
    print("2. ✅ Verify CTM URL:")
    print("   - Check if CTM_IP_ADDRESS is correct")
    print("   - Try with/without protocol (http:// or https://)")
    print()
    print("3. ✅ Check network connectivity:")
    print("   - ping <ctm_ip>")
    print("   - telnet <ctm_ip> <port>")
    print()
    print("4. ✅ Verify CTM service:")
    print("   - Check if CTM is running")
    print("   - Check if authentication endpoint is accessible")
    print()
    print("5. ✅ Common solutions:")
    print("   - Try HTTP instead of HTTPS: CTM_IP_ADDRESS=http://***********")
    print("   - Try HTTPS with port: CTM_IP_ADDRESS=https://***********:443")
    print("   - Check if credentials are case-sensitive")
    print()

def main():
    """Main function"""
    print("🔧 Production Configuration Check")
    print("=" * 40)
    
    # Check .env file
    env_ok = check_env_file()
    
    # Check configuration
    config_ok, config = check_configuration()
    
    # Test QRNG connection
    qrng_ok = False
    if config_ok and config:
        qrng_ok = test_qrng_connection(config)
    
    # Test CTM connection
    ctm_ok = False
    if config_ok:
        ctm_ok = test_ctm_connection()
    
    # Summary
    print("\n📊 Summary:")
    print(f"   .env file: {'✅ OK' if env_ok else '❌ Missing'}")
    print(f"   Configuration: {'✅ OK' if config_ok else '❌ Error'}")
    print(f"   QRNG Connection: {'✅ OK' if qrng_ok else '❌ Failed'}")
    print(f"   CTM Connection: {'✅ OK' if ctm_ok else '❌ Failed'}")
    
    # Show troubleshooting if needed
    if not qrng_ok:
        show_qrng_troubleshooting()
    
    if not ctm_ok:
        show_401_troubleshooting()
    
    return qrng_ok and ctm_ok

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎉 All checks passed!")
        print("🚀 You can now run: python sq-ctm-manager.py")
    else:
        print("\n⚠️  Some checks failed!")
        print("🔧 Review the troubleshooting steps above")
        sys.exit(1) 