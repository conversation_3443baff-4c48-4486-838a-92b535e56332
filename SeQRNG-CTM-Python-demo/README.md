# 🔐 SeQRNG-CTM Integration

**Secure Production-Ready Integration** between a SeQRNG quantum random number generator and Thales CipherTrust Manager (CTM). This project provides a robust, secure, and user-friendly solution for managing cryptographic keys with high-quality quantum entropy.

## 📋 Table of Contents

- [🚀 Quick Start](#-quick-start)
- [✨ Key Features](#-key-features)
- [🔐 Security Enhancements](#-security-enhancements)
- [🔧 Installation](#-installation)
- [⚙️ Configuration](#️-configuration)
- [🏃‍♂️ Running the Application](#️-running-the-application)
- [🔍 Troubleshooting](#-troubleshooting)
- [📚 Architecture & Modules](#-architecture--modules)
- [📞 Support](#-support)

---

## 🚀 Quick Start

For a fast setup, use the automated installation script. This will set up the environment and generate the necessary production scripts.

```bash
# Make the script executable
chmod +x install.sh

# Run the installer with sudo
sudo ./install.sh
```

After installation, the main entry point is the `sq-ctm-manager.py` script located in the `production_scripts` directory.

```bash
# Run the main manager
./production_scripts/sq-ctm-manager.py
```

This manager provides an interactive menu to:
1.  Create new cryptographic keys.
2.  Create new versions of existing keys.
3.  List all available keys in CTM.
4.  Check system configuration and connectivity.

---

## ✨ Key Features

- **Interactive Menu System**: A central, user-friendly interface (`sq-ctm-manager.py`) for all key management operations.
- **Quantum-Powered Keys**: Generates cryptographic keys using true random numbers from the SeQRNG device.
- **Key Versioning**: Safely update existing keys with new quantum material without overwriting them.
- **Duplicate Key Protection**: Prevents accidental key overwriting by providing clear options if a key name already exists.
- **Robust Fallback**: If the SeQRNG is unavailable, the system transparently asks for user confirmation before falling back to a standard PRNG, ensuring security awareness.
- **Flexible Configuration**: Supports configuration via environment variables, `.env` files, or a `config.json` file.
- **Production-Ready Scripts**: Generates standalone scripts that can be run in production without needing to activate a Python virtual environment manually.
- **Multiple Algorithms**: Supports AES, ARIA, and various HMAC algorithms.

---

## 🔐 Security Enhancements

This application has been hardened with several critical security features:

1.  **JWT Token Validation**:
    *   **What it does**: Automatically validates JSON Web Tokens (JWTs) from CTM to ensure they are not expired or invalid.
    *   **Why it matters**: Prevents the use of compromised or old credentials, enhancing authentication security.

2.  **Secure Key Name Generation**:
    *   **What it does**: Uses a cryptographically secure random generator (`secrets` module) to create unpredictable key names.
    *   **Why it matters**: Protects against enumeration attacks where an attacker could guess key names based on predictable patterns.

3.  **Entropy Quality Validation**:
    *   **What it does**: Analyzes the random data from the SeQRNG (or the fallback PRNG) to ensure it meets high-quality randomness standards (e.g., Shannon entropy).
    *   **Why it matters**: Guarantees that the generated cryptographic keys are based on high-quality, unpredictable entropy, which is fundamental to their security.

4.  **No Hardcoded Credentials**:
    *   **What it does**: All sensitive information like API tokens and passwords are externalized from the source code.
    *   **Why it matters**: Prevents credentials from being accidentally exposed in version control systems like Git.

5.  **Environment-Specific SSL Configuration**:
    *   **What it does**: Allows for different SSL/TLS certificate verification settings for development and production environments.
    *   **Why it matters**: Enforces strict, secure connections in production while allowing flexibility for local testing.

---

## 🔧 Installation

For detailed installation and deployment instructions, including server hardening and running the application as a service, please refer to the **[DEPLOYMENT.md](DEPLOYMENT.md)** guide.

---

## ⚙️ Configuration

The application can be configured in three ways, in the following order of precedence:

1.  **Environment Variables (Highest Priority)**: Ideal for production and containerized environments.
2.  **.env File**: A simple and effective way to manage variables for local development.
3.  **config.json File (Lowest Priority)**: For configurations that are less likely to change.

#### Connection Formats
The system supports flexible formats for SeQRNG and CTM addresses, including:
- IP addresses (`*************`)
- Hostnames (`ctm.example.com`)
- Full URLs (`https://seqrng.your-domain.com:8443`)

For examples and detailed setup, run `python config.py --create-examples` to generate template files.

---

## 🏃‍♂️ Running the Application

### Production (Recommended)
Use the scripts generated in the `production_scripts/` directory. They are self-contained and do not require manual activation of a virtual environment.

```bash
./production_scripts/sq-ctm-manager.py
```

### Development
For development, you can run the scripts directly using the Python interpreter from the virtual environment.

```bash
# Activate virtual environment
source venv/bin/activate

# Run the main script
python sq-ctm-manager.py
```

---

## 🔍 Troubleshooting

This project includes several tools to help diagnose issues:

- **Configuration Check**: `check_production_config.py`
  - Verifies that all required configuration settings are present and that the application can connect to both SeQRNG and CTM.

- **CTM Connection Debugger**: `debug_ctm.py`
  - Provides a detailed analysis of the connection and authentication process with the CipherTrust Manager, which is useful for resolving authentication errors (like HTTP 401).

- **Fix for Configuration Access**:
  - **Problem**: Earlier versions incorrectly accessed configuration data.
  - **Solution**: The code was updated to use dictionary-style access (e.g., `config['key']`) instead of attribute-style (e.g., `config.key`), making it more robust.

- **Fix for CTM Authentication**:
  - **Problem**: The system was fragile and failed if the CTM API returned an unexpected response format.
  - **Solution**: The authentication logic was improved to handle multiple response formats (JSON, plain text) and includes better error handling.

---

## 📚 Architecture & Modules

- **`sq-ctm-manager.py`**: The main interactive entry point for users.
- **`SeQRNG_CTM_1.4.py`**: The core script for generating and uploading keys.
- **`interface_seqrng_v2.py`**: Module for interacting with the SeQRNG API and handling the PRNG fallback.
- **`sq_ctm_modules_v1.py`**: Module for interacting with the CipherTrust Manager API.
- **`config.py`**: Manages application configuration from various sources.
- **Security Modules**:
  - **`jwt_validator.py`**: Validates JWTs.
  - **`secure_key_generator.py`**: Generates secure, random key names.
  - **`entropy_validator.py`**: Validates the quality of random data.

---

## 📞 Support

For any issues, please consult the **[DEPLOYMENT.md](DEPLOYMENT.md)** guide first. If the problem persists, gather the output from the troubleshooting scripts (`check_production_config.py`, `debug_ctm.py`) to help diagnose the issue.
