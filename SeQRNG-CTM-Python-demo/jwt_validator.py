#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JWT Token Validator
==================

Validación y manejo seguro de tokens JWT para CTM
"""

import jwt
from datetime import datetime, timedelta
import time
from typing import Dict, Any, Optional

class JWTValidator:
    """Validación y manejo seguro de tokens JWT"""
    
    def __init__(self, config=None):
        self.config = config
        self.token_cache = {}  # Cache de tokens válidos
        self.cache_duration = 300  # 5 minutos
    
    def validate_jwt_token(self, token: str, issuer: str = None) -> dict:
        """
        Validar token JWT sin verificar firma (modo inseguro para compatibilidad)
        
        Args:
            token: Token JWT a validar
            issuer: Issuer esperado (opcional)
            
        Returns:
            dict: Payload del token si válido
            
        Raises:
            ValueError: Si el token es inválido o expirado
        """
        try:
            # Decodificar sin verificar firma (modo inseguro)
            payload = jwt.decode(token, options={"verify_signature": False})
            
            # Validar expiración
            exp = payload.get('exp')
            if exp:
                current_time = int(time.time())
                if current_time > exp:
                    raise ValueError(f"Token expired at {datetime.fromtimestamp(exp)}")
            
            # Validar issuer si se especifica
            if issuer and payload.get('iss') != issuer:
                raise ValueError(f"Invalid issuer: expected {issuer}, got {payload.get('iss')}")
            
            # Validar tiempo de emisión (iat)
            iat = payload.get('iat')
            if iat:
                current_time = int(time.time())
                if current_time < iat:
                    raise ValueError(f"Token issued in the future: {datetime.fromtimestamp(iat)}")
            
            return payload
            
        except jwt.InvalidTokenError as e:
            raise ValueError(f"Invalid JWT token: {e}")
        except Exception as e:
            raise ValueError(f"Error validating JWT: {e}")
    
    def is_token_valid(self, token: str, issuer: str = None) -> bool:
        """Verificar si un token es válido"""
        try:
            self.validate_jwt_token(token, issuer)
            return True
        except ValueError:
            return False
    
    def is_token_expired(self, token: str) -> bool:
        """Verificar si un token está expirado"""
        try:
            payload = jwt.decode(token, options={"verify_signature": False})
            exp = payload.get('exp')
            if exp:
                return int(time.time()) > exp
            return False
        except Exception:
            return True
    
    def get_token_info(self, token: str) -> dict:
        """Obtener información del token sin validar"""
        try:
            payload = jwt.decode(token, options={"verify_signature": False})
            return {
                'expires_at': datetime.fromtimestamp(payload.get('exp', 0)) if payload.get('exp') else None,
                'issued_at': datetime.fromtimestamp(payload.get('iat', 0)) if payload.get('iat') else None,
                'issuer': payload.get('iss'),
                'subject': payload.get('sub'),
                'audience': payload.get('aud'),
                'is_expired': self.is_token_expired(token),
                'time_until_expiry': self.get_time_until_expiry(token)
            }
        except Exception:
            return {'error': 'Invalid token format'}
    
    def get_time_until_expiry(self, token: str) -> Optional[timedelta]:
        """Obtener tiempo restante hasta expiración"""
        try:
            payload = jwt.decode(token, options={"verify_signature": False})
            exp = payload.get('exp')
            if exp:
                current_time = int(time.time())
                if current_time < exp:
                    return timedelta(seconds=exp - current_time)
                else:
                    return timedelta(seconds=0)  # Ya expirado
            return None
        except Exception:
            return None
    
    def should_refresh_token(self, token: str, refresh_threshold: int = 300) -> bool:
        """
        Determinar si un token debe renovarse
        
        Args:
            token: Token a verificar
            refresh_threshold: Segundos antes de expiración para renovar
            
        Returns:
            bool: True si debe renovarse
        """
        time_until_expiry = self.get_time_until_expiry(token)
        if time_until_expiry:
            return time_until_expiry.total_seconds() < refresh_threshold
        return True  # Si no se puede determinar, asumir que debe renovarse
    
    def log_token_info(self, token: str, context: str = ""):
        """Log información del token para debugging"""
        token_info = self.get_token_info(token)
        
        if 'error' in token_info:
            print(f"⚠️ JWT Token Error: {token_info['error']}")
            return
        
        print(f"🔑 JWT Token Info {context}:")
        print(f"   Issuer: {token_info.get('issuer', 'Unknown')}")
        print(f"   Subject: {token_info.get('subject', 'Unknown')}")
        print(f"   Issued: {token_info.get('issued_at', 'Unknown')}")
        print(f"   Expires: {token_info.get('expires_at', 'Unknown')}")
        print(f"   Expired: {'Yes' if token_info.get('is_expired') else 'No'}")
        
        time_until_expiry = token_info.get('time_until_expiry')
        if time_until_expiry:
            if time_until_expiry.total_seconds() > 0:
                print(f"   Time until expiry: {time_until_expiry}")
            else:
                print(f"   ⚠️ Token expired {abs(time_until_expiry)} ago")
        
        # Advertencias
        if token_info.get('is_expired'):
            print(f"   ⚠️ WARNING: Token is expired!")
        elif time_until_expiry and time_until_expiry.total_seconds() < 300:
            print(f"   ⚠️ WARNING: Token expires soon!") 