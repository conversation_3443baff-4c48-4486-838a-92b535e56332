#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug script for CTM connection and authentication issues
"""

import requests
import json
import sys
from urllib3.exceptions import InsecureRequestWarning

def test_ctm_connection(base_url, username, password, domain, ssl_verify=None):
    """Test CTM connection and authentication with SSL verification"""
    print(f"🔍 Testing CTM connection to: {base_url}")
    print(f"   Username: {username}")
    print(f"   Domain: {domain}")
    
    # Show SSL verification status
    if ssl_verify is False:
        print(f"   SSL Verification: Disabled (development mode)")
    elif isinstance(ssl_verify, str):
        print(f"   SSL Verification: Custom certificate ({ssl_verify})")
    else:
        print(f"   SSL Verification: System CA certificates")
    print()
    
    # Construct login data
    login_data = {
        "name": username,
        "password": password,
        "domain": domain
    }
    
    # Construct API URL
    if base_url.startswith(('http://', 'https://')):
        api_url = f"{base_url.rstrip('/')}/api/v1/auth/tokens"
    else:
        api_url = f"https://{base_url}/api/v1/auth/tokens"
    
    print(f"📡 API URL: {api_url}")
    
    # Create a copy of login_data with hidden password for display
    display_data = login_data.copy()
    display_data['password'] = '***HIDDEN***'
    print(f"📤 Request data: {json.dumps(display_data, indent=2)}")
    print()
    
    # Suppress SSL warnings only if verification is disabled
    if ssl_verify is False:
        requests.packages.urllib3.disable_warnings(category=InsecureRequestWarning)
    
    try:
        # Make the request
        print("🔄 Making authentication request...")
        response = requests.post(
            api_url, 
            json=login_data, 
            verify=ssl_verify, 
            timeout=30,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"📥 Response Status: {response.status_code}")
        print(f"📥 Response Headers: {dict(response.headers)}")
        print()
        
        # Check status code
        if response.status_code != 200:
            print(f"❌ Authentication failed: HTTP {response.status_code}")
            print(f"   Response: {response.text}")
            return False
        
        # Try to parse response
        print("🔍 Analyzing response...")
        print(f"   Content-Type: {response.headers.get('Content-Type', 'Not specified')}")
        print(f"   Response length: {len(response.text)} characters")
        print()
        
        # Try JSON parsing
        try:
            json_data = response.json()
            print("✅ Response is valid JSON:")
            
            # Create a copy with hidden sensitive data
            display_json = json_data.copy()
            for field in ['jwt', 'token', 'access_token', 'api_key', 'key', 'value']:
                if field in display_json:
                    display_json[field] = '***HIDDEN***'
            
            print(json.dumps(display_json, indent=2))
            
            # Look for token fields
            token_fields = ['token', 'access_token', 'api_key', 'key', 'value']
            found_token = None
            
            for field in token_fields:
                if field in json_data:
                    found_token = json_data[field]
                    print(f"✅ Found token in field '{field}': ***HIDDEN***")
                    break
            
            if not found_token:
                print("⚠️  No token field found in JSON response")
                print("   Available fields:", list(json_data.keys()))
            
        except json.JSONDecodeError as e:
            print(f"⚠️  Response is not valid JSON: {e}")
            print("   Raw response:")
            print(response.text)
            
            # Try string parsing as fallback
            if '"' in response.text:
                data = response.text.split('"')[1::2]
                print(f"   String parsing found {len(data)} quoted values:")
                for i, item in enumerate(data):
                    print(f"     [{i}]: {item}")
        
        return True
        
    except requests.exceptions.ConnectionError as e:
        print(f"❌ Connection Error: {e}")
        return False
    except requests.exceptions.Timeout as e:
        print(f"❌ Timeout Error: {e}")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Request Error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")
        return False

def main():
    """Main function"""
    print("🔧 CTM Connection Debug Tool")
    print("=" * 40)
    
    try:
        from config import get_config
        config = get_config()
        
        # Load CTM configuration
        ctm_config = config.get_ctm_config()
        
        print(f"📋 Loaded CTM configuration:")
        print(f"   Base URL: {ctm_config['base_url']}")
        print(f"   Username: {ctm_config['username']}")
        print(f"   Domain: {ctm_config['domain']}")
        print()
        
        # Test connection
        success = test_ctm_connection(
            ctm_config['base_url'],
            ctm_config['username'],
            ctm_config['password'],
            ctm_config['domain'],
            ctm_config['ssl_verify']
        )
        
        if success:
            print("\n✅ CTM connection test completed successfully")
        else:
            print("\n❌ CTM connection test failed")
            
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        print("\n🔧 To fix this, ensure your .env file contains:")
        print("   CTM_IP_ADDRESS=your_ctm_ip_or_url")
        print("   CTM_USERNAME=your_username")
        print("   CTM_PASSWORD=your_password")
        print("   CTM_DOMAIN=your_domain")
        print("   ENVIRONMENT=development (or production)")
        print("   VERIFY_SSL=true (or false for development)")

if __name__ == "__main__":
    main() 