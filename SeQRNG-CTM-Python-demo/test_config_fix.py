#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Script para Verificar Corrección de Configuración
=====================================================

Este script verifica que la corrección del error de configuración funciona correctamente.
"""

import sys
import os
import importlib.util

def test_config_loading():
    """Probar la carga de configuración"""
    
    print("🧪 Test de Carga de Configuración")
    print("=" * 40)
    
    try:
        from config import get_config
        
        config = get_config()
        print("✅ Configuración cargada correctamente")
        
        # Probar SeQRNG config
        seqrng_config = config.get_seqrng_config()
        print(f"✅ SeQRNG config: {seqrng_config['ip_address']}")
        
        # Probar CTM config
        ctm_config = config.get_ctm_config()
        print(f"✅ CTM config: {ctm_config['ip_address']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error cargando configuración: {e}")
        return False

def test_script_import():
    """Probar la importación del script corregido"""
    
    print("\n🧪 Test de Importación de Script")
    print("=" * 40)
    
    try:
        # Usar importlib para manejar el nombre con puntos
        spec = importlib.util.spec_from_file_location("SeQRNG_CTM_1_4", "SeQRNG_CTM_1.4.py")
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        print("✅ Script SeQRNG_CTM_1.4.py importado correctamente")
        
        # Verificar que la función main existe
        if hasattr(module, 'main'):
            print("✅ Función main encontrada")
        else:
            print("❌ Función main no encontrada")
            return False
            
        # Verificar que la función handle_existing_key existe
        if hasattr(module, 'handle_existing_key'):
            print("✅ Función handle_existing_key encontrada")
        else:
            print("❌ Función handle_existing_key no encontrada")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error importando script: {e}")
        return False

def test_main_script():
    """Probar el script principal"""
    
    print("\n🧪 Test de Script Principal")
    print("=" * 40)
    
    try:
        # Verificar que el script principal existe
        if not os.path.exists('sq-ctm-manager.py'):
            print("❌ Script principal sq-ctm-manager.py no encontrado")
            return False
            
        print("✅ Script principal encontrado")
        
        # Verificar que los scripts requeridos existen
        required_scripts = [
            'SeQRNG_CTM_1.4.py',
            'ctm_key_version_updater.py',
            'ctm_list_keys.py',
            'check_production_config.py'
        ]
        
        missing_scripts = []
        for script in required_scripts:
            if not os.path.exists(script):
                missing_scripts.append(script)
            else:
                print(f"✅ {script} encontrado")
        
        if missing_scripts:
            print(f"❌ Scripts faltantes: {missing_scripts}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error verificando scripts: {e}")
        return False

def test_production_scripts():
    """Probar scripts de producción"""
    
    print("\n🧪 Test de Scripts de Producción")
    print("=" * 40)
    
    try:
        # Verificar que el directorio existe
        if not os.path.exists('production_scripts'):
            print("❌ Directorio production_scripts no encontrado")
            return False
            
        print("✅ Directorio production_scripts encontrado")
        
        # Verificar scripts de producción
        production_scripts = [
            'production_scripts/sq-ctm-manager.py',
            'production_scripts/SeQRNG_CTM_1.4.py',
            'production_scripts/ctm_list_keys.py'
        ]
        
        for script in production_scripts:
            if os.path.exists(script):
                print(f"✅ {script} encontrado")
            else:
                print(f"❌ {script} no encontrado")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error verificando scripts de producción: {e}")
        return False

def main():
    """Función principal de pruebas"""
    
    print("🚀 Test de Corrección de Configuración")
    print("=" * 50)
    
    # Test 1: Carga de configuración
    test1_result = test_config_loading()
    
    # Test 2: Importación de script
    test2_result = test_script_import()
    
    # Test 3: Script principal
    test3_result = test_main_script()
    
    # Test 4: Scripts de producción
    test4_result = test_production_scripts()
    
    print("\n📊 Resumen de Tests:")
    print(f"   Configuración: {'✅' if test1_result else '❌'}")
    print(f"   Importación: {'✅' if test2_result else '❌'}")
    print(f"   Script principal: {'✅' if test3_result else '❌'}")
    print(f"   Scripts producción: {'✅' if test4_result else '❌'}")
    
    if all([test1_result, test2_result, test3_result, test4_result]):
        print("\n🎉 ¡Todos los tests pasaron! El error de configuración está corregido.")
        print("\n🎯 Ahora puedes ejecutar:")
        print("   python sq-ctm-manager.py")
        print("   ./production_scripts/sq-ctm-manager.py")
    else:
        print("\n❌ Algunos tests fallaron. Revisa los errores arriba.")
    
    return all([test1_result, test2_result, test3_result, test4_result])

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 