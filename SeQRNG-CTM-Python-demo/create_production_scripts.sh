#!/bin/bash
# Script para crear versiones de producción de los scripts principales

echo "🔧 Creando scripts de producción..."
echo "=================================="

# Obtener la ruta absoluta del directorio actual
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
VENV_PYTHON="$SCRIPT_DIR/venv/bin/python"

# Verificar que el venv existe
if [ ! -f "$VENV_PYTHON" ]; then
    echo "❌ Error: Virtual environment no encontrado en $VENV_PYTHON"
    echo "   Ejecuta primero: ./activate_env.sh"
    exit 1
fi

echo "✅ Virtual environment encontrado: $VENV_PYTHON"

# Crear directorio para scripts de producción
mkdir -p production_scripts

# Función para crear script de producción
create_production_script() {
    local original_script="$1"
    local production_script="production_scripts/$(basename "$original_script")"
    
    if [ ! -f "$original_script" ]; then
        echo "⚠️  Script original no encontrado: $original_script"
        return
    fi
    
    echo "📝 Creando: $production_script"
    
    # Crear script con shebang que apunte al venv
    cat > "$production_script" << EOF
#!$VENV_PYTHON
# -*- coding: utf-8 -*-
"""
Script de producción generado automáticamente
Ejecuta: $original_script
"""

import sys
import os

# Obtener la ruta absoluta del directorio del proyecto
PROJECT_DIR = "$SCRIPT_DIR"
SCRIPT_PATH = os.path.join(PROJECT_DIR, "$original_script")

# Agregar el directorio del proyecto al path para importar módulos
sys.path.insert(0, PROJECT_DIR)

# Cambiar al directorio del proyecto
os.chdir(PROJECT_DIR)

# Ejecutar el script original
exec(open(SCRIPT_PATH).read())
EOF
    
    # Hacer ejecutable
    chmod +x "$production_script"
    echo "✅ Creado: $production_script"
}

# Crear scripts de producción
echo ""
echo "🔨 Generando scripts de producción..."

create_production_script "sq-ctm-manager.py"
create_production_script "SeQRNG_CTM_1.4.py"
create_production_script "ctm_key_version_updater.py"
create_production_script "ctm_list_keys.py"
create_production_script "check_production_config.py"
create_production_script "test_security_improvements.py"

echo ""
echo "🎉 Scripts de producción creados en: production_scripts/"
echo ""
echo "📋 Uso en producción:"
echo "   ./production_scripts/sq-ctm-manager.py"
echo "   ./production_scripts/SeQRNG_CTM_1.4.py"
echo "   ./production_scripts/ctm_list_keys.py"
echo ""
echo "🔧 Para hacer los scripts ejecutables desde cualquier lugar:"
echo "   sudo cp production_scripts/* /usr/local/bin/"
echo "   # O agregar al PATH: export PATH=\$PATH:$(pwd)/production_scripts" 