# SequreQuantum - Despliegue con Docker Compose

Este documento describe cómo desplegar la aplicación completa SequreQuantum en una VPS Ubuntu usando Docker Compose.

## Arquitectura

La aplicación consta de 4 servicios:

1. **PostgreSQL Database** (puerto 5432)
2. **Flask API (SeQRNG)** (puerto 3001)
3. **NestJS API** (puerto 3000)
4. **React Frontend** (puerto 80)

## Requisitos Previos

### En la VPS Ubuntu:

1. **Docker y Docker Compose**:
```bash
# Actualizar el sistema
sudo apt update && sudo apt upgrade -y

# Instalar Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Agregar usuario al grupo docker
sudo usermod -aG docker $USER

# Instalar Docker Compose
sudo apt install docker-compose-plugin -y

# Reiniciar sesión para aplicar cambios de grupo
exit
```

2. **Git**:
```bash
sudo apt install git -y
```

## Despliegue

### 1. Clonar el repositorio

```bash
git clone <URL_DEL_REPOSITORIO> SequreQuantum
cd SequreQuantum
```

### Despliegue Rápido (Recomendado)

Para un despliegue automatizado, usa el script incluido:

```bash
# Hacer el script ejecutable (si no lo está)
chmod +x deploy.sh

# Ejecutar despliegue automatizado
./deploy.sh
```

El script verificará automáticamente:
- Instalación de Docker y Docker Compose
- Permisos necesarios
- Estructura del proyecto
- Configuración del archivo .env

### Despliegue Manual

### 2. Configurar variables de entorno

Si usaste el script de despliegue automático, este paso ya está hecho. Para configuración manual:

Editar el archivo `.env` con tus configuraciones específicas:

```bash
nano .env
```

**IMPORTANTE**: Cambiar los siguientes valores por los de tu entorno:

- `DB_PASSWORD`: Contraseña segura para PostgreSQL
- `JWT_SECRET`: Clave secreta para JWT (mínimo 32 caracteres)
- `JWT_REFRESH_SECRET`: Clave secreta para refresh tokens
- `SEQRNG_IP_ADDRESS`: IP/URL de tu servidor SeQRNG
- `SEQRNG_API_TOKEN`: Token de API de SeQRNG
- `CTM_IP_ADDRESS`: IP de tu CipherTrust Manager
- `CTM_USERNAME`: Usuario de CTM
- `CTM_PASSWORD`: Contraseña de CTM
- `CTM_DOMAIN`: Dominio de CTM

### 3. Configurar permisos del archivo .env

```bash
chmod 600 .env
```

### 4. Construir y levantar los servicios

```bash
# Construir las imágenes
docker compose build

# Levantar todos los servicios
docker compose up -d

# Verificar que todos los servicios estén corriendo
docker compose ps
```

### 5. Verificar el despliegue

```bash
# Ver logs de todos los servicios
docker compose logs

# Ver logs de un servicio específico
docker compose logs nestjs-api
docker compose logs seqrng-api
docker compose logs frontend
docker compose logs postgres
```

### 6. Acceder a la aplicación

- **Frontend**: http://tu-vps-ip (puerto 80)
- **NestJS API**: http://tu-vps-ip:3000
- **NestJS API Docs**: http://tu-vps-ip:3000/api
- **Flask API**: http://tu-vps-ip:3001
- **Flask API Docs**: http://tu-vps-ip:3001/docs

## Scripts Incluidos

### Script de Monitoreo

```bash
# Verificar estado de todos los servicios
./monitor.sh

# Monitoreo detallado con uso de recursos y logs
./monitor.sh --detailed
```

### Script de Limpieza

```bash
# Limpieza básica (contenedores e imágenes no utilizadas)
./cleanup.sh

# Opciones específicas
./cleanup.sh stop              # Parar servicios
./cleanup.sh clean-containers  # Limpiar contenedores
./cleanup.sh clean-images      # Limpiar imágenes
./cleanup.sh backup-db         # Crear backup de BD
./cleanup.sh restore-db        # Restaurar backup de BD
./cleanup.sh help              # Ver todas las opciones
```

## Comandos Útiles

### Gestión de servicios

```bash
# Parar todos los servicios
docker compose down

# Parar y eliminar volúmenes (¡CUIDADO! Elimina la base de datos)
docker compose down -v

# Reiniciar un servicio específico
docker compose restart nestjs-api

# Ver logs en tiempo real
docker compose logs -f

# Ejecutar comando en un contenedor
docker compose exec nestjs-api bash
docker compose exec postgres psql -U sqq_user -d sqq_database
```

### Mantenimiento

```bash
# Actualizar imágenes
docker compose pull
docker compose up -d

# Limpiar imágenes no utilizadas
docker system prune -a

# Backup de la base de datos
docker compose exec postgres pg_dump -U sqq_user sqq_database > backup.sql

# Restaurar base de datos
docker compose exec -T postgres psql -U sqq_user sqq_database < backup.sql
```

## Configuración de Firewall

Si tienes UFW habilitado, permite los puertos necesarios:

```bash
sudo ufw allow 80
sudo ufw allow 3000
sudo ufw allow 3001
sudo ufw allow 5432  # Solo si necesitas acceso externo a PostgreSQL
```

## Configuración de Nginx (Opcional)

**NOTA**: El frontend ya incluye Nginx configurado y corre en el puerto 80. Solo necesitas configurar Nginx externo si quieres:
- Usar un dominio personalizado
- Configurar SSL/HTTPS
- Balanceador de carga

Para usar un dominio y SSL:

```bash
sudo apt install nginx certbot python3-certbot-nginx -y
```

Crear configuración de Nginx en `/etc/nginx/sites-available/sequrequantum`:

```nginx
server {
    listen 80;
    server_name tu-dominio.com;

    # Frontend (ya tiene Nginx interno)
    location / {
        proxy_pass http://localhost:80;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # API endpoints
    location /api {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Flask API
    location /seqrng {
        proxy_pass http://localhost:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

```bash
sudo ln -s /etc/nginx/sites-available/sequrequantum /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx

# Configurar SSL
sudo certbot --nginx -d tu-dominio.com
```

## Troubleshooting

### Problemas comunes:

1. **Servicios no inician**: Verificar logs con `docker compose logs`
2. **Base de datos no conecta**: Verificar variables de entorno y que PostgreSQL esté healthy
3. **Frontend no carga**: Verificar que NestJS API esté corriendo y accesible
4. **Errores de permisos**: Verificar que el usuario esté en el grupo docker

### Verificar conectividad entre servicios:

```bash
# Desde NestJS hacia PostgreSQL
docker compose exec nestjs-api ping postgres

# Desde NestJS hacia Flask API
docker compose exec nestjs-api ping seqrng-api
```

## Seguridad

1. Cambiar todas las contraseñas por defecto
2. Usar secretos fuertes para JWT
3. Configurar firewall apropiadamente
4. Mantener Docker y el sistema actualizados
5. Considerar usar Docker secrets para producción
6. Configurar SSL/TLS con certificados válidos
