import {
  Controller,
  Get,
  UseGuards,
  Request,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { CtmService } from './ctm.service';
import { CtmResponseDto } from './dto/ctm-response.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('User CTMs')
@ApiBearerAuth('JWT-auth')
@Controller('user/ctms')
@UseGuards(JwtAuthGuard)
export class UserCtmController {
  constructor(private readonly ctmService: CtmService) {}

  @Get()
  @ApiOperation({ summary: 'Obtener CTMs asignados al usuario autenticado' })
  @ApiResponse({
    status: 200,
    description: 'Lista de CTMs asignados al usuario',
    type: [CtmResponseDto],
  })
  @ApiResponse({ status: 401, description: 'No autorizado' })
  getUserCtms(@Request() req: any): Promise<CtmResponseDto[]> {
    const userId = req.user.id;
    return this.ctmService.getUserCtms(userId);
  }
}
