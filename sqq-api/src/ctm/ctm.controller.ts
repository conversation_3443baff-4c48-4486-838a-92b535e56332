import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { CtmService } from './ctm.service';
import { CreateCtmDto } from './dto/create-ctm.dto';
import { UpdateCtmDto } from './dto/update-ctm.dto';
import { GetCtmsDto } from './dto/get-ctms.dto';
import { AssignCtmDto, AssignUsersToCtmDto } from './dto/assign-ctm.dto';
import { CtmResponseDto } from './dto/ctm-response.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../users/entities/user.entity';

@ApiTags('CTM Management')
@ApiBearerAuth('JWT-auth')
@Controller('ctms')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(UserRole.ADMIN)
export class CtmController {
  constructor(private readonly ctmService: CtmService) {}

  @Post()
  @ApiOperation({ summary: 'Crear nuevo CTM (Solo Admin)' })
  @ApiResponse({
    status: 201,
    description: 'CTM creado exitosamente',
    type: CtmResponseDto,
  })
  @ApiResponse({ status: 403, description: 'Acceso denegado' })
  @ApiResponse({ status: 409, description: 'El nombre del CTM ya existe' })
  create(@Body() createCtmDto: CreateCtmDto): Promise<CtmResponseDto> {
    return this.ctmService.create(createCtmDto);
  }

  @Get()
  @ApiOperation({ summary: 'Obtener todos los CTMs (Solo Admin)' })
  @ApiResponse({
    status: 200,
    description: 'Lista de CTMs obtenida exitosamente',
  })
  @ApiResponse({ status: 403, description: 'Acceso denegado' })
  findAll(@Query() getCtmsDto: GetCtmsDto) {
    return this.ctmService.findAll(getCtmsDto);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Obtener CTM por ID (Solo Admin)' })
  @ApiParam({ name: 'id', description: 'ID del CTM', type: 'string' })
  @ApiResponse({
    status: 200,
    description: 'CTM encontrado',
    type: CtmResponseDto,
  })
  @ApiResponse({ status: 404, description: 'CTM no encontrado' })
  @ApiResponse({ status: 403, description: 'Acceso denegado' })
  findOne(@Param('id', ParseUUIDPipe) id: string): Promise<CtmResponseDto> {
    return this.ctmService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Actualizar CTM (Solo Admin)' })
  @ApiParam({ name: 'id', description: 'ID del CTM', type: 'string' })
  @ApiResponse({
    status: 200,
    description: 'CTM actualizado exitosamente',
    type: CtmResponseDto,
  })
  @ApiResponse({ status: 404, description: 'CTM no encontrado' })
  @ApiResponse({ status: 409, description: 'El nombre del CTM ya existe' })
  @ApiResponse({ status: 403, description: 'Acceso denegado' })
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateCtmDto: UpdateCtmDto,
  ): Promise<CtmResponseDto> {
    return this.ctmService.update(id, updateCtmDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Eliminar CTM (Solo Admin)' })
  @ApiParam({ name: 'id', description: 'ID del CTM', type: 'string' })
  @ApiResponse({
    status: 200,
    description: 'CTM eliminado exitosamente',
  })
  @ApiResponse({ status: 404, description: 'CTM no encontrado' })
  @ApiResponse({ status: 400, description: 'No se puede eliminar CTM con llaves asociadas' })
  @ApiResponse({ status: 403, description: 'Acceso denegado' })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<{ message: string }> {
    await this.ctmService.remove(id);
    return { message: 'CTM eliminado exitosamente' };
  }

  @Post(':id/assign-users')
  @ApiOperation({ summary: 'Asignar usuarios a un CTM (Solo Admin)' })
  @ApiParam({ name: 'id', description: 'ID del CTM', type: 'string' })
  @ApiResponse({
    status: 200,
    description: 'Usuarios asignados exitosamente al CTM',
  })
  @ApiResponse({ status: 404, description: 'CTM no encontrado' })
  @ApiResponse({ status: 400, description: 'Uno o más usuarios no encontrados o inactivos' })
  @ApiResponse({ status: 403, description: 'Acceso denegado' })
  async assignUsersToCtm(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() assignUsersDto: AssignUsersToCtmDto,
  ): Promise<{ message: string }> {
    await this.ctmService.assignUsersToCtm(id, assignUsersDto);
    return { message: 'Usuarios asignados exitosamente al CTM' };
  }

  @Post('assign-to-user/:userId')
  @ApiOperation({ summary: 'Asignar CTMs a un usuario (Solo Admin)' })
  @ApiParam({ name: 'userId', description: 'ID del usuario', type: 'string' })
  @ApiResponse({
    status: 200,
    description: 'CTMs asignados exitosamente al usuario',
  })
  @ApiResponse({ status: 404, description: 'Usuario no encontrado' })
  @ApiResponse({ status: 400, description: 'Uno o más CTMs no encontrados o inactivos' })
  @ApiResponse({ status: 403, description: 'Acceso denegado' })
  async assignCtmsToUser(
    @Param('userId', ParseUUIDPipe) userId: string,
    @Body() assignCtmDto: AssignCtmDto,
  ): Promise<{ message: string }> {
    await this.ctmService.assignCtmsToUser(userId, assignCtmDto);
    return { message: 'CTMs asignados exitosamente al usuario' };
  }

  @Delete(':ctmId/remove-from-user/:userId')
  @ApiOperation({ summary: 'Remover asignación de CTM de un usuario (Solo Admin)' })
  @ApiParam({ name: 'ctmId', description: 'ID del CTM', type: 'string' })
  @ApiParam({ name: 'userId', description: 'ID del usuario', type: 'string' })
  @ApiResponse({
    status: 200,
    description: 'CTM removido exitosamente del usuario',
  })
  @ApiResponse({ status: 404, description: 'Usuario no encontrado' })
  @ApiResponse({ status: 403, description: 'Acceso denegado' })
  async removeCtmFromUser(
    @Param('ctmId', ParseUUIDPipe) ctmId: string,
    @Param('userId', ParseUUIDPipe) userId: string,
  ): Promise<{ message: string }> {
    await this.ctmService.removeCtmFromUser(userId, ctmId);
    return { message: 'CTM removido exitosamente del usuario' };
  }

  @Get('user/:userId')
  @ApiOperation({ summary: 'Obtener CTMs asignados a un usuario (Solo Admin)' })
  @ApiParam({ name: 'userId', description: 'ID del usuario', type: 'string' })
  @ApiResponse({
    status: 200,
    description: 'Lista de CTMs del usuario',
    type: [CtmResponseDto],
  })
  @ApiResponse({ status: 404, description: 'Usuario no encontrado' })
  @ApiResponse({ status: 403, description: 'Acceso denegado' })
  getUserCtms(@Param('userId', ParseUUIDPipe) userId: string): Promise<CtmResponseDto[]> {
    return this.ctmService.getUserCtms(userId);
  }
}
