import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsBoolean, MaxLength, MinLength, IsUrl } from 'class-validator';

export class CreateCtmDto {
  @ApiProperty({
    example: 'ctm_production_01',
    description: 'Nombre único del CTM',
    minLength: 3,
    maxLength: 100,
  })
  @IsString()
  @MinLength(3)
  @MaxLength(100)
  name: string;

  @ApiProperty({
    example: 'CTM de producción para el equipo de desarrollo',
    description: 'Descripción del CTM',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @ApiProperty({
    example: 'https://ctm.example.com:443',
    description: 'Dirección IP/URL del CipherTrust Manager',
  })
  @IsString()
  @MaxLength(255)
  ipAddress: string;

  @ApiProperty({
    example: 'ctm_admin',
    description: 'Usuario del CipherTrust Manager',
  })
  @IsString()
  @MaxLength(100)
  username: string;

  @ApiProperty({
    example: 'ctm_password123',
    description: 'Contraseña del CipherTrust Manager',
  })
  @IsString()
  @MaxLength(128)
  password: string;

  @ApiProperty({
    example: 'root',
    description: 'Dominio del CipherTrust Manager',
  })
  @IsString()
  @MaxLength(100)
  domain: string;

  @ApiProperty({
    example: 'https://seqrng.example.com:1982',
    description: 'Dirección IP/URL del SeqRNG',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  seqrngIpAddress?: string;

  @ApiProperty({
    example: '1|cifovFzUWTeAPUeAYlZYefsY3tXFf9Z1TV5WV0YL',
    description: 'Token de API del SeqRNG',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  seqrngApiToken?: string;

  @ApiProperty({
    example: true,
    description: 'Si el CTM está activo',
    required: false,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}
