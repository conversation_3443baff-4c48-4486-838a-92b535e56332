import { ApiProperty } from '@nestjs/swagger';
import { Exclude, Expose, Type } from 'class-transformer';

export class CtmResponseDto {
  @ApiProperty({
    example: 'uuid-string',
    description: 'ID único del CTM',
  })
  @Expose()
  id: string;

  @ApiProperty({
    example: 'ctm_production_01',
    description: 'Nombre del CTM',
  })
  @Expose()
  name: string;

  @ApiProperty({
    example: 'CTM de producción para el equipo de desarrollo',
    description: 'Descripción del CTM',
  })
  @Expose()
  description: string | null;

  @ApiProperty({
    example: 'https://ctm.example.com:443',
    description: 'Dirección IP/URL del CipherTrust Manager',
  })
  @Expose()
  ipAddress: string;

  @ApiProperty({
    example: 'ctm_admin',
    description: 'Usuario del CipherTrust Manager',
  })
  @Expose()
  username: string;

  @ApiProperty({
    example: 'root',
    description: '<PERSON>inio del CipherTrust Manager',
  })
  @Expose()
  domain: string;

  @ApiProperty({
    example: 'https://seqrng.example.com:1982',
    description: 'Dirección IP/URL del SeqRNG',
  })
  @Expose()
  seqrngIpAddress: string | null;

  @ApiProperty({
    example: true,
    description: 'Si el CTM está activo',
  })
  @Expose()
  isActive: boolean;

  @ApiProperty({
    example: true,
    description: 'Si el CTM tiene configuración completa',
  })
  @Expose()
  hasCompleteConfiguration: boolean;

  @ApiProperty({
    example: true,
    description: 'Si el CTM tiene configuración SeqRNG',
  })
  @Expose()
  hasSeqrngConfiguration: boolean;

  @ApiProperty({
    example: '2024-01-01T00:00:00.000Z',
    description: 'Fecha de creación',
  })
  @Expose()
  @Type(() => Date)
  createdAt: Date;

  @ApiProperty({
    example: '2024-01-01T00:00:00.000Z',
    description: 'Fecha de última actualización',
  })
  @Expose()
  @Type(() => Date)
  updatedAt: Date;

  // Excluir campos sensibles
  @Exclude()
  password: string;

  @Exclude()
  seqrngApiToken: string | null;
}
