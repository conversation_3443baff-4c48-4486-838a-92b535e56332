import { ApiProperty } from '@nestjs/swagger';
import { IsUUID, IsArray, ArrayNotEmpty } from 'class-validator';

export class AssignCtmDto {
  @ApiProperty({
    example: ['uuid-1', 'uuid-2'],
    description: 'Array de IDs de CTMs a asignar al usuario',
    type: [String],
  })
  @IsArray()
  @ArrayNotEmpty()
  @IsUUID('4', { each: true })
  ctmIds: string[];
}

export class AssignUsersToCtmDto {
  @ApiProperty({
    example: ['user-uuid-1', 'user-uuid-2'],
    description: 'Array de IDs de usuarios a asignar al CTM',
    type: [String],
  })
  @IsArray()
  @ArrayNotEmpty()
  @IsUUID('4', { each: true })
  userIds: string[];
}
