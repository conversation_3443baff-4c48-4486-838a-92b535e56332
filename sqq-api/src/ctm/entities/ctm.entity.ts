import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToMany,
  OneToMany,
  JoinTable,
} from 'typeorm';
import { Exclude } from 'class-transformer';
import { User } from '../../users/entities/user.entity';
import { Key } from '../../keys/entities/key.entity';

@Entity('ctms')
export class Ctm {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 100, unique: true })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string | null;

  // CTM Configuration fields
  @Column({ type: 'varchar', length: 255 })
  ipAddress: string;

  @Column({ type: 'varchar', length: 100 })
  username: string;

  @Column({ type: 'text' })
  @Exclude()
  password: string;

  @Column({ type: 'varchar', length: 100 })
  domain: string;

  // SeqRNG Configuration fields
  @Column({ type: 'varchar', length: 255, nullable: true })
  seqrngIpAddress: string | null;

  @Column({ type: 'text', nullable: true })
  @Exclude()
  seqrngApiToken: string | null;

  @Column({ default: true })
  isActive: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relación many-to-many con usuarios
  @ManyToMany(() => User, user => user.ctms)
  @JoinTable({
    name: 'user_ctms',
    joinColumn: {
      name: 'ctmId',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'userId',
      referencedColumnName: 'id',
    },
  })
  users: User[];

  // Relación one-to-many con llaves
  @OneToMany(() => Key, key => key.ctm)
  keys: Key[];

  // Computed property to check if CTM has complete configuration
  get hasCompleteConfiguration(): boolean {
    return !!(
      this.ipAddress &&
      this.username &&
      this.password &&
      this.domain
    );
  }

  // Computed property to check if CTM has SeqRNG configuration
  get hasSeqrngConfiguration(): boolean {
    return !!(this.seqrngIpAddress && this.seqrngApiToken);
  }
}
