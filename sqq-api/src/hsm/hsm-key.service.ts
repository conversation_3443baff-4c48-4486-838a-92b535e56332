import { Injectable, NotFoundException, BadRequestException, Logger, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { HttpService } from '@nestjs/axios';
import { Hsm<PERSON><PERSON> } from './entities/hsm-key.entity';
import { Hsm } from './entities/hsm.entity';
import { User } from '../users/entities/user.entity';
import { CreateHsmKeyDto, HsmKeyResponseDto } from './dto/create-hsm-key.dto';
import { GetHsmKeysDto } from './dto/get-hsm-keys.dto';
import { HsmKeyType, HsmKeyStatus } from './enums/hsm-key-type.enum';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class HsmKeyService {
  private readonly logger = new Logger(HsmKeyService.name);

  constructor(
    @InjectRepository(Hsm<PERSON>ey)
    private hsmKeyRepository: Repository<HsmKey>,
    @InjectRepository(Hsm)
    private hsmRepository: Repository<Hsm>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private readonly httpService: HttpService,
  ) {}

  async createKey(userId: string, createHsmKeyDto: CreateHsmKeyDto): Promise<HsmKeyResponseDto> {
    this.logger.log(`Creating HSM key: ${createHsmKeyDto.keyName} for user: ${userId}`);

    // Verify user exists and is active
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['hsms'],
    });

    if (!user || !user.isActive) {
      throw new NotFoundException('User not found or inactive');
    }

    // Verify HSM exists and user has access
    const hsm = await this.hsmRepository.findOne({
      where: { id: createHsmKeyDto.hsmId },
      relations: ['users'],
    });

    if (!hsm || !hsm.isActive) {
      throw new NotFoundException('HSM not found or inactive');
    }

    // Check if user has access to this HSM
    const hasAccess = hsm.users.some(hsmUser => hsmUser.id === userId);
    if (!hasAccess) {
      throw new ForbiddenException('User does not have access to this HSM');
    }

    // Validate key parameters based on type
    this.validateKeyParameters(createHsmKeyDto);

    // Generate owner field
    const owner = this.generateOwner(user);

    // Create key record
    const hsmKey = this.hsmKeyRepository.create({
      keyName: createHsmKeyDto.keyName,
      keyType: createHsmKeyDto.keyType,
      keySize: createHsmKeyDto.keySize,
      eccCurveType: createHsmKeyDto.eccCurveType,
      status: HsmKeyStatus.PENDING,
      owner,
      userId,
      hsmId: createHsmKeyDto.hsmId,
    });

    const savedKey = await this.hsmKeyRepository.save(hsmKey);

    // Call HSM API to create the key
    try {
      await this.callHsmApi(savedKey, hsm);
    } catch (error) {
      this.logger.error(`Failed to create key in HSM: ${error.message}`);
      // Update key status to failed
      savedKey.status = HsmKeyStatus.FAILED;
      savedKey.errorMessage = error.message;
      await this.hsmKeyRepository.save(savedKey);
    }

    return this.mapToResponseDto(savedKey);
  }

  private validateKeyParameters(createHsmKeyDto: CreateHsmKeyDto): void {
    const { keyType, keySize, eccCurveType } = createHsmKeyDto;

    switch (keyType) {
      case HsmKeyType.AES:
        if (!keySize || ![128, 192, 256].includes(keySize)) {
          throw new BadRequestException('AES keys require keySize of 128, 192, or 256');
        }
        break;
      case HsmKeyType.RSA:
        if (!keySize || ![2048, 3072, 4096].includes(keySize)) {
          throw new BadRequestException('RSA keys require keySize of 2048, 3072, or 4096');
        }
        break;
      case HsmKeyType.EC:
        if (!eccCurveType) {
          throw new BadRequestException('EC keys require eccCurveType');
        }
        break;
      case HsmKeyType.KYBER:
      case HsmKeyType.DILITHIUM:
        // Post-quantum algorithms don't require additional parameters
        break;
      default:
        throw new BadRequestException('Invalid key type');
    }
  }

  private generateOwner(user: User): string {
    if (user.company) {
      return user.company.toLowerCase().replace(/\s+/g, '_');
    }
    return `${user.firstName}_${user.lastName}`.toLowerCase();
  }

  private async callHsmApi(hsmKey: HsmKey, hsm: Hsm): Promise<void> {
    const endpoint = hsm.getEndpointUrl(hsmKey.endpoint);
    const payload = hsmKey.getRequestPayload();

    this.logger.log(`Calling HSM API: ${endpoint}`);
    this.logger.debug(`Payload: ${JSON.stringify(payload)}`);

    try {
      const response = await firstValueFrom(
        this.httpService.post(endpoint, payload, {
          headers: {
            'Content-Type': 'application/json',
          },
          timeout: 30000, // 30 seconds timeout
        })
      );

      this.logger.log(`HSM API response: ${response.status}`);
      
      // Update key with success status
      hsmKey.status = HsmKeyStatus.CREATED;
      hsmKey.hsmResponse = JSON.stringify(response.data);
      
      // Extract HSM key ID if available in response
      if (response.data && response.data.keyId) {
        hsmKey.hsmKeyId = response.data.keyId;
      }

      await this.hsmKeyRepository.save(hsmKey);

    } catch (error) {
      this.logger.error(`HSM API call failed: ${error.message}`);
      
      // Update key with failure status
      hsmKey.status = HsmKeyStatus.FAILED;
      hsmKey.errorMessage = error.response?.data?.message || error.message;
      
      await this.hsmKeyRepository.save(hsmKey);
      
      throw new BadRequestException(`Failed to create key in HSM: ${error.message}`);
    }
  }

  async findUserKeys(userId: string, getHsmKeysDto: GetHsmKeysDto): Promise<HsmKeyResponseDto[]> {
    this.logger.log(`Fetching HSM keys for user: ${userId}`);

    const queryBuilder = this.hsmKeyRepository
      .createQueryBuilder('hsmKey')
      .leftJoinAndSelect('hsmKey.hsm', 'hsm')
      .where('hsmKey.userId = :userId', { userId });

    if (getHsmKeysDto.keyName) {
      queryBuilder.andWhere('hsmKey.keyName ILIKE :keyName', { 
        keyName: `%${getHsmKeysDto.keyName}%` 
      });
    }

    if (getHsmKeysDto.keyType) {
      queryBuilder.andWhere('hsmKey.keyType = :keyType', { 
        keyType: getHsmKeysDto.keyType 
      });
    }

    if (getHsmKeysDto.status) {
      queryBuilder.andWhere('hsmKey.status = :status', { 
        status: getHsmKeysDto.status 
      });
    }

    if (getHsmKeysDto.hsmId) {
      queryBuilder.andWhere('hsmKey.hsmId = :hsmId', { 
        hsmId: getHsmKeysDto.hsmId 
      });
    }

    queryBuilder.orderBy('hsmKey.createdAt', 'DESC');

    const keys = await queryBuilder.getMany();
    return keys.map(key => this.mapToResponseDto(key));
  }

  async findAllKeys(getHsmKeysDto: GetHsmKeysDto): Promise<HsmKeyResponseDto[]> {
    this.logger.log('Fetching all HSM keys');

    const queryBuilder = this.hsmKeyRepository
      .createQueryBuilder('hsmKey')
      .leftJoinAndSelect('hsmKey.hsm', 'hsm')
      .leftJoinAndSelect('hsmKey.user', 'user');

    if (getHsmKeysDto.keyName) {
      queryBuilder.andWhere('hsmKey.keyName ILIKE :keyName', { 
        keyName: `%${getHsmKeysDto.keyName}%` 
      });
    }

    if (getHsmKeysDto.keyType) {
      queryBuilder.andWhere('hsmKey.keyType = :keyType', { 
        keyType: getHsmKeysDto.keyType 
      });
    }

    if (getHsmKeysDto.status) {
      queryBuilder.andWhere('hsmKey.status = :status', { 
        status: getHsmKeysDto.status 
      });
    }

    if (getHsmKeysDto.hsmId) {
      queryBuilder.andWhere('hsmKey.hsmId = :hsmId', { 
        hsmId: getHsmKeysDto.hsmId 
      });
    }

    queryBuilder.orderBy('hsmKey.createdAt', 'DESC');

    const keys = await queryBuilder.getMany();
    return keys.map(key => this.mapToResponseDto(key));
  }

  async findKeysByHsm(userId: string, hsmId: string, getHsmKeysDto: GetHsmKeysDto): Promise<HsmKeyResponseDto[]> {
    this.logger.log(`Fetching HSM keys for HSM: ${hsmId} and user: ${userId}`);

    // Verify user exists and is active
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['hsms'],
    });

    if (!user || !user.isActive) {
      throw new NotFoundException('User not found or inactive');
    }

    // Verify HSM exists and user has access
    const hsm = await this.hsmRepository.findOne({
      where: { id: hsmId },
      relations: ['users'],
    });

    if (!hsm || !hsm.isActive) {
      throw new NotFoundException('HSM not found or inactive');
    }

    // Check if user has access to this HSM
    const hasAccess = hsm.users.some(hsmUser => hsmUser.id === userId);
    if (!hasAccess) {
      throw new ForbiddenException('User does not have access to this HSM');
    }

    const queryBuilder = this.hsmKeyRepository
      .createQueryBuilder('hsmKey')
      .leftJoinAndSelect('hsmKey.hsm', 'hsm')
      .leftJoinAndSelect('hsmKey.user', 'user')
      .where('hsmKey.hsmId = :hsmId', { hsmId })
      .andWhere('hsmKey.userId = :userId', { userId });

    if (getHsmKeysDto.keyName) {
      queryBuilder.andWhere('hsmKey.keyName ILIKE :keyName', {
        keyName: `%${getHsmKeysDto.keyName}%`
      });
    }

    if (getHsmKeysDto.keyType) {
      queryBuilder.andWhere('hsmKey.keyType = :keyType', {
        keyType: getHsmKeysDto.keyType
      });
    }

    if (getHsmKeysDto.status) {
      queryBuilder.andWhere('hsmKey.status = :status', {
        status: getHsmKeysDto.status
      });
    }

    queryBuilder.orderBy('hsmKey.createdAt', 'DESC');

    const keys = await queryBuilder.getMany();
    return keys.map(key => this.mapToResponseDto(key));
  }

  async findOne(keyId: string, userId?: string): Promise<HsmKeyResponseDto> {
    this.logger.log(`Fetching HSM key: ${keyId}`);

    const queryBuilder = this.hsmKeyRepository
      .createQueryBuilder('hsmKey')
      .leftJoinAndSelect('hsmKey.hsm', 'hsm')
      .leftJoinAndSelect('hsmKey.user', 'user')
      .where('hsmKey.id = :keyId', { keyId });

    if (userId) {
      queryBuilder.andWhere('hsmKey.userId = :userId', { userId });
    }

    const key = await queryBuilder.getOne();

    if (!key) {
      throw new NotFoundException('HSM key not found');
    }

    return this.mapToResponseDto(key);
  }

  private mapToResponseDto(hsmKey: HsmKey): HsmKeyResponseDto {
    return {
      id: hsmKey.id,
      keyName: hsmKey.keyName,
      keyType: hsmKey.keyType,
      status: hsmKey.status,
      keySize: hsmKey.keySize,
      eccCurveType: hsmKey.eccCurveType,
      hsmKeyId: hsmKey.hsmKeyId,
      owner: hsmKey.owner,
      createdAt: hsmKey.createdAt,
      updatedAt: hsmKey.updatedAt,
    };
  }
}
