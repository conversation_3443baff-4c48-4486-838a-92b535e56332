import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Hsm } from './hsm.entity';
import { HsmKeyType, EccCurveType, HsmKeyStatus } from '../enums/hsm-key-type.enum';

@Entity('hsm_keys')
export class HsmKey {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  keyName: string; // Nombre de la llave en el HSM

  @Column({
    type: 'enum',
    enum: HsmKeyType,
  })
  keyType: HsmKeyType;

  @Column({
    type: 'enum',
    enum: HsmKeyStatus,
    default: HsmKeyStatus.PENDING,
  })
  status: HsmKeyStatus;

  // Campos específicos para diferentes tipos de algoritmos
  @Column({ type: 'int', nullable: true })
  keySize: number | null; // Para AES y RSA

  @Column({
    type: 'enum',
    enum: EccCurveType,
    nullable: true,
  })
  eccCurveType: EccCurveType | null; // Para curvas elípticas

  // Campos para respuesta del HSM
  @Column({ type: 'text', nullable: true })
  hsmKeyId: string | null; // ID de la llave en el HSM

  @Column({ type: 'text', nullable: true })
  hsmResponse: string | null; // Respuesta completa del HSM (JSON)

  @Column({ type: 'text', nullable: true })
  errorMessage: string | null; // Mensaje de error si falla la creación

  @Column({ type: 'varchar', length: 100, nullable: true })
  owner: string | null; // Owner de la llave

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relación con el usuario que creó la llave
  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column({ type: 'uuid' })
  userId: string;

  // Relación con el HSM
  @ManyToOne(() => Hsm, hsm => hsm.keys, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'hsmId' })
  hsm: Hsm;

  @Column({ type: 'uuid' })
  hsmId: string;

  // Getters para información adicional
  get isSuccessful(): boolean {
    return this.status === HsmKeyStatus.CREATED;
  }

  get displayName(): string {
    return this.keyName;
  }

  // Método para obtener el endpoint correcto según el tipo de llave
  get endpoint(): string {
    switch (this.keyType) {
      case HsmKeyType.AES:
      case HsmKeyType.RSA:
        return '/keys';
      case HsmKeyType.EC:
        return '/keysEC';
      case HsmKeyType.KYBER:
      case HsmKeyType.DILITHIUM:
        return '/keysPQC';
      default:
        return '/keys';
    }
  }

  // Método para generar el payload de la request según el tipo
  getRequestPayload(): object {
    const basePayload = {
      keyName: this.keyName,
      keyType: this.keyType,
    };

    switch (this.keyType) {
      case HsmKeyType.AES:
      case HsmKeyType.RSA:
        return {
          ...basePayload,
          keySize: this.keySize,
        };
      case HsmKeyType.EC:
        return {
          ...basePayload,
          eccCurveType: this.eccCurveType,
        };
      case HsmKeyType.KYBER:
      case HsmKeyType.DILITHIUM:
        return basePayload;
      default:
        return basePayload;
    }
  }
}
