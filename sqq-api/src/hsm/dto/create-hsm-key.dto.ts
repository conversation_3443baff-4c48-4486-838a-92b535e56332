import { ApiProperty } from '@nestjs/swagger';
import { 
  IsString, 
  IsEnum, 
  IsOptional, 
  IsInt, 
  Min, 
  Max, 
  ValidateIf,
  IsUUID,
  MinLength,
  MaxLength
} from 'class-validator';
import { HsmKeyType, EccCurveType, KeySize } from '../enums/hsm-key-type.enum';

export class CreateHsmKeyDto {
  @ApiProperty({
    example: 'PKIRsakey4096',
    description: 'Nombre de la llave en el HSM',
    minLength: 3,
    maxLength: 255,
  })
  @IsString()
  @MinLength(3)
  @MaxLength(255)
  keyName: string;

  @ApiProperty({
    example: HsmKeyType.RSA,
    description: 'Tipo de algoritmo de la llave',
    enum: HsmKeyType,
  })
  @IsEnum(HsmKeyType)
  keyType: HsmKeyType;

  @ApiProperty({
    example: 4096,
    description: '<PERSON>a<PERSON> de la llave (requerido para AES y RSA)',
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(128)
  @Max(8192)
  @ValidateIf((o) => o.keyType === HsmKeyType.AES || o.keyType === HsmKeyType.RSA)
  keySize?: number;

  @ApiProperty({
    example: EccCurveType.SECT233K1,
    description: 'Tipo de curva elíptica (requerido para EC)',
    enum: EccCurveType,
    required: false,
  })
  @IsOptional()
  @IsEnum(EccCurveType)
  @ValidateIf((o) => o.keyType === HsmKeyType.EC)
  eccCurveType?: EccCurveType;

  @ApiProperty({
    example: 'uuid-hsm-id',
    description: 'ID del HSM donde crear la llave',
  })
  @IsUUID('4')
  hsmId: string;
}

export class HsmKeyResponseDto {
  @ApiProperty({
    example: 'uuid-string',
    description: 'ID único de la llave HSM',
  })
  id: string;

  @ApiProperty({
    example: 'PKIRsakey4096',
    description: 'Nombre de la llave',
  })
  keyName: string;

  @ApiProperty({
    example: HsmKeyType.RSA,
    description: 'Tipo de algoritmo',
    enum: HsmKeyType,
  })
  keyType: HsmKeyType;

  @ApiProperty({
    example: 'created',
    description: 'Estado de la llave',
  })
  status: string;

  @ApiProperty({
    example: 4096,
    description: 'Tamaño de la llave',
    required: false,
  })
  keySize?: number | null;

  @ApiProperty({
    example: EccCurveType.SECT233K1,
    description: 'Tipo de curva elíptica',
    enum: EccCurveType,
    required: false,
  })
  eccCurveType?: EccCurveType | null;

  @ApiProperty({
    example: 'hsm-key-id-123',
    description: 'ID de la llave en el HSM',
    required: false,
  })
  hsmKeyId?: string | null;

  @ApiProperty({
    example: 'user_company',
    description: 'Propietario de la llave',
    required: false,
  })
  owner?: string | null;

  @ApiProperty({
    example: '2024-01-01T00:00:00.000Z',
    description: 'Fecha de creación',
  })
  createdAt: Date;

  @ApiProperty({
    example: '2024-01-01T00:00:00.000Z',
    description: 'Fecha de última actualización',
  })
  updatedAt: Date;
}
