import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class HsmResponseDto {
  @ApiProperty({
    example: 'uuid-string',
    description: 'ID único del HSM',
  })
  @Expose()
  id: string;

  @ApiProperty({
    example: 'hsm_production_01',
    description: 'Nombre del HSM',
  })
  @Expose()
  name: string;

  @ApiProperty({
    example: 'HSM de producción para el equipo de desarrollo',
    description: 'Descripción del HSM',
  })
  @Expose()
  description: string | null;

  @ApiProperty({
    example: 'http://tomcatlinux:8080',
    description: 'URL base del HSM',
  })
  @Expose()
  url: string;

  @ApiProperty({
    example: true,
    description: 'Si el HSM está activo',
  })
  @Expose()
  isActive: boolean;

  @ApiProperty({
    example: true,
    description: 'Si el HSM tiene configuración completa',
  })
  @Expose()
  hasCompleteConfiguration: boolean;

  @ApiProperty({
    example: '2024-01-01T00:00:00.000Z',
    description: 'Fecha de creación',
  })
  @Expose()
  createdAt: Date;

  @ApiProperty({
    example: '2024-01-01T00:00:00.000Z',
    description: 'Fecha de última actualización',
  })
  @Expose()
  updatedAt: Date;
}
