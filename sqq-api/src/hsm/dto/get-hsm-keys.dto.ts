import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsEnum, IsUUID } from 'class-validator';
import { HsmKeyType, HsmKeyStatus } from '../enums/hsm-key-type.enum';

export class GetHsmKeysDto {
  @ApiProperty({
    example: 'PKIRsa',
    description: 'Filtrar por nombre de la llave (búsqueda parcial)',
    required: false,
  })
  @IsOptional()
  @IsString()
  keyName?: string;

  @ApiProperty({
    example: HsmKeyType.RSA,
    description: 'Filtrar por tipo de algoritmo',
    enum: HsmKeyType,
    required: false,
  })
  @IsOptional()
  @IsEnum(HsmKeyType)
  keyType?: HsmKeyType;

  @ApiProperty({
    example: HsmKeyStatus.CREATED,
    description: 'Filtrar por estado de la llave',
    enum: HsmKeyStatus,
    required: false,
  })
  @IsOptional()
  @IsEnum(HsmKeyStatus)
  status?: HsmKeyStatus;

  @ApiProperty({
    example: 'uuid-hsm-id',
    description: 'Filtrar por HSM específico',
    required: false,
  })
  @IsOptional()
  @IsUUID('4')
  hsmId?: string;
}
