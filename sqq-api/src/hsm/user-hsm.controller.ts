import {
  Controller,
  Get,
  UseGuards,
  Request,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { HsmService } from './hsm.service';
import { HsmResponseDto } from './dto/hsm-response.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('User HSMs')
@ApiBearerAuth('JWT-auth')
@Controller('user/hsms')
@UseGuards(JwtAuthGuard)
export class UserHsmController {
  constructor(private readonly hsmService: HsmService) {}

  @Get()
  @ApiOperation({ summary: 'Obtener HSMs asignados al usuario autenticado' })
  @ApiResponse({
    status: 200,
    description: 'Lista de HSMs asignados al usuario',
    type: [HsmResponseDto],
  })
  @ApiResponse({ status: 401, description: 'No autorizado' })
  getUserHsms(@Request() req: any): Promise<HsmResponseDto[]> {
    const userId = req.user.id;
    return this.hsmService.getUserHsms(userId);
  }
}
