import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';
import { HsmService } from './hsm.service';
import { HsmKeyService } from './hsm-key.service';
import { HsmController } from './hsm.controller';
import { UserHsmController } from './user-hsm.controller';
import { HsmKeyController } from './hsm-key.controller';
import { Hsm } from './entities/hsm.entity';
import { HsmKey } from './entities/hsm-key.entity';
import { User } from '../users/entities/user.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([Hsm, HsmKey, User]),
    HttpModule.register({
      timeout: 30000,
      maxRedirects: 5,
    }),
  ],
  controllers: [HsmController, UserHsmController, HsmKeyController],
  providers: [HsmService, HsmKeyService],
  exports: [HsmService, HsmKeyService],
})
export class HsmModule {}
