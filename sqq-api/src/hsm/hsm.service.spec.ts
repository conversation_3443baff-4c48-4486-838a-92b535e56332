import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { HsmService } from './hsm.service';
import { Hsm } from './entities/hsm.entity';
import { User } from '../users/entities/user.entity';

describe('HsmService', () => {
  let service: HsmService;
  let hsmRepository: Repository<Hsm>;
  let userRepository: Repository<User>;

  const mockRepository = {
    findOne: jest.fn(),
    find: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
    remove: jest.fn(),
    createQueryBuilder: jest.fn(() => ({
      andWhere: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      getMany: jest.fn(),
    })),
    findBy: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        HsmService,
        {
          provide: getRepositoryToken(Hsm),
          useValue: mockRepository,
        },
        {
          provide: getRepositoryToken(User),
          useValue: mockRepository,
        },
      ],
    }).compile();

    service = module.get<HsmService>(HsmService);
    hsmRepository = module.get<Repository<Hsm>>(getRepositoryToken(Hsm));
    userRepository = module.get<Repository<User>>(getRepositoryToken(User));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a new HSM', async () => {
      const createHsmDto = {
        name: 'test-hsm',
        description: 'Test HSM',
        url: 'http://localhost:8080',
      };

      const savedHsm = {
        id: 'uuid',
        ...createHsmDto,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        hasCompleteConfiguration: true,
      };

      mockRepository.findOne.mockResolvedValue(null);
      mockRepository.create.mockReturnValue(savedHsm);
      mockRepository.save.mockResolvedValue(savedHsm);

      const result = await service.create(createHsmDto);

      expect(result).toBeDefined();
      expect(result.name).toBe(createHsmDto.name);
      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: { name: createHsmDto.name },
      });
      expect(mockRepository.create).toHaveBeenCalledWith(createHsmDto);
      expect(mockRepository.save).toHaveBeenCalledWith(savedHsm);
    });
  });
});
