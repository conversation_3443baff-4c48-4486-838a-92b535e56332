import {
  MigrationInterface,
  QueryRunner,
  Table,
  TableForeignKey,
} from 'typeorm';

export class CreateKeysTable1720563000000 implements MigrationInterface {
  name = 'CreateKeysTable1720563000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create enum types (only if they don't exist)
    await queryRunner.query(`
      DO $$ BEGIN
        CREATE TYPE "keys_type_enum" AS ENUM('random_bytes', 'hex_key', 'alphanumeric_key');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    await queryRunner.query(`
      DO $$ BEGIN
        CREATE TYPE "keys_algorithm_enum" AS ENUM(
          'aes', 
          'aria', 
          'RSA', 
          'hmac-sha1', 
          'hmac-sha256', 
          'hmac-sha384', 
          'hmac-sha512'
        );
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    await queryRunner.query(`
      DO $$ BEGIN
        CREATE TYPE "keys_status_enum" AS ENUM('generated', 'uploaded_to_ctm', 'failed');
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    // Create keys table (only if it doesn't exist)
    const tableExists = await queryRunner.hasTable('keys');
    if (!tableExists) {
      await queryRunner.createTable(
        new Table({
          name: 'keys',
          columns: [
            {
              name: 'id',
              type: 'uuid',
              isPrimary: true,
              generationStrategy: 'uuid',
              default: 'uuid_generate_v4()',
            },
            {
              name: 'name',
              type: 'varchar',
              length: '255',
              isNullable: false,
            },
            {
              name: 'type',
              type: 'enum',
              enum: ['random_bytes', 'hex_key', 'alphanumeric_key'],
              isNullable: false,
            },
            {
              name: 'algorithm',
              type: 'enum',
              enum: [
                'aes',
                'aria',
                'RSA',
                'hmac-sha1',
                'hmac-sha256',
                'hmac-sha384',
                'hmac-sha512',
              ],
              isNullable: true,
            },
            {
              name: 'numBytes',
              type: 'int',
              isNullable: false,
            },
            {
              name: 'status',
              type: 'enum',
              enum: ['generated', 'uploaded_to_ctm', 'failed'],
              default: "'generated'",
              isNullable: false,
            },
            {
              name: 'owner',
              type: 'varchar',
              length: '100',
              isNullable: true,
            },
            {
              name: 'exportable',
              type: 'boolean',
              default: false,
              isNullable: false,
            },
            {
              name: 'uploadedToCtm',
              type: 'boolean',
              default: false,
              isNullable: false,
            },
            {
              name: 'ctmKeyName',
              type: 'text',
              isNullable: true,
            },
            {
              name: 'entropyReport',
              type: 'text',
              isNullable: true,
            },
            {
              name: 'errorMessage',
              type: 'text',
              isNullable: true,
            },
            {
              name: 'userId',
              type: 'uuid',
              isNullable: false,
            },
            {
              name: 'createdAt',
              type: 'timestamp',
              default: 'CURRENT_TIMESTAMP',
              isNullable: false,
            },
            {
              name: 'updatedAt',
              type: 'timestamp',
              default: 'CURRENT_TIMESTAMP',
              onUpdate: 'CURRENT_TIMESTAMP',
              isNullable: false,
            },
          ],
          indices: [
            {
              name: 'IDX_KEYS_USER_ID',
              columnNames: ['userId'],
            },
            {
              name: 'IDX_KEYS_TYPE',
              columnNames: ['type'],
            },
            {
              name: 'IDX_KEYS_STATUS',
              columnNames: ['status'],
            },
            {
              name: 'IDX_KEYS_CREATED_AT',
              columnNames: ['createdAt'],
            },
          ],
        }),
        true,
      );

      // Create foreign key constraint
      await queryRunner.createForeignKey(
        'keys',
        new TableForeignKey({
          columnNames: ['userId'],
          referencedColumnNames: ['id'],
          referencedTableName: 'users',
          onDelete: 'CASCADE',
          name: 'FK_KEYS_USER_ID',
        }),
      );
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key
    const tableExists = await queryRunner.hasTable('keys');
    if (tableExists) {
      await queryRunner.dropForeignKey('keys', 'FK_KEYS_USER_ID');
      // Drop table
      await queryRunner.dropTable('keys');
    }

    // Drop enum types
    await queryRunner.query(`DROP TYPE IF EXISTS "keys_status_enum"`);
    await queryRunner.query(`DROP TYPE IF EXISTS "keys_algorithm_enum"`);
    await queryRunner.query(`DROP TYPE IF EXISTS "keys_type_enum"`);
  }
}
