import { MigrationInterface, QueryRunner, TableColumn, TableForeignKey, TableIndex } from 'typeorm';

export class AddCtmIdToKeys1754000002000 implements MigrationInterface {
  name = 'AddCtmIdToKeys1754000002000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add ctmId column to keys table
    await queryRunner.addColumn(
      'keys',
      new TableColumn({
        name: 'ctmId',
        type: 'uuid',
        isNullable: true, // Initially nullable for migration
      }),
    );

    // Create foreign key constraint
    await queryRunner.createForeignKey(
      'keys',
      new TableForeignKey({
        columnNames: ['ctmId'],
        referencedColumnNames: ['id'],
        referencedTableName: 'ctms',
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      }),
    );

    // Create index for better performance
    await queryRunner.createIndex(
      'keys',
      new TableIndex({
        name: 'IDX_KEY_CTM',
        columnNames: ['ctmId'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop index
    await queryRunner.dropIndex('keys', 'IDX_KEY_CTM');
    
    // Drop foreign key
    const table = await queryRunner.getTable('keys');
    if (table) {
      const foreignKey = table.foreignKeys.find(fk => fk.columnNames.indexOf('ctmId') !== -1);
      if (foreignKey) {
        await queryRunner.dropForeignKey('keys', foreignKey);
      }
    }
    
    // Drop column
    await queryRunner.dropColumn('keys', 'ctmId');
  }
}
