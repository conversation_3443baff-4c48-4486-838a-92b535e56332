import { MigrationInterface, QueryRunner } from "typeorm";

export class AddKeyVersioning1753732862476 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add baseKeyId column
        await queryRunner.query(`
            ALTER TABLE "keys"
            ADD COLUMN "baseKeyId" uuid NULL
        `);

        // Add version column with default value 0
        await queryRunner.query(`
            ALTER TABLE "keys"
            ADD COLUMN "version" integer NOT NULL DEFAULT 0
        `);

        // Add foreign key constraint for baseKeyId
        await queryRunner.query(`
            ALTER TABLE "keys"
            ADD CONSTRAINT "FK_keys_baseKeyId"
            FOREIGN KEY ("baseKeyId") REFERENCES "keys"("id")
            ON DELETE CASCADE
        `);

        // Create index for better performance on version queries
        await queryRunner.query(`
            CREATE INDEX "IDX_keys_baseKeyId" ON "keys" ("baseKeyId")
        `);

        await queryRunner.query(`
            CREATE INDEX "IDX_keys_version" ON "keys" ("version")
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop indexes
        await queryRunner.query(`DROP INDEX "IDX_keys_version"`);
        await queryRunner.query(`DROP INDEX "IDX_keys_baseKeyId"`);

        // Drop foreign key constraint
        await queryRunner.query(`ALTER TABLE "keys" DROP CONSTRAINT "FK_keys_baseKeyId"`);

        // Drop columns
        await queryRunner.query(`ALTER TABLE "keys" DROP COLUMN "version"`);
        await queryRunner.query(`ALTER TABLE "keys" DROP COLUMN "baseKeyId"`);
    }

}
