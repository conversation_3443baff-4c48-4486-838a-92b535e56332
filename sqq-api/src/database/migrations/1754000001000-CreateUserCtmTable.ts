import { MigrationInterface, QueryRunner, Table, TableIndex, TableForeignKey } from 'typeorm';

export class CreateUserCtmTable1754000001000 implements MigrationInterface {
  name = 'CreateUserCtmTable1754000001000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create user_ctms junction table
    await queryRunner.createTable(
      new Table({
        name: 'user_ctms',
        columns: [
          {
            name: 'userId',
            type: 'uuid',
            isPrimary: true,
            isNullable: false,
          },
          {
            name: 'ctmId',
            type: 'uuid',
            isPrimary: true,
            isNullable: false,
          },
          {
            name: 'assignedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            isNullable: false,
          },
        ],
      }),
      true,
    );

    // Create foreign key constraints
    await queryRunner.createForeignKey(
      'user_ctms',
      new TableForeignKey({
        columnNames: ['userId'],
        referencedColumnNames: ['id'],
        referencedTableName: 'users',
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      }),
    );

    await queryRunner.createForeignKey(
      'user_ctms',
      new TableForeignKey({
        columnNames: ['ctmId'],
        referencedColumnNames: ['id'],
        referencedTableName: 'ctms',
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      }),
    );

    // Create indexes for better performance
    await queryRunner.createIndex(
      'user_ctms',
      new TableIndex({
        name: 'IDX_USER_CTM_USER',
        columnNames: ['userId'],
      }),
    );

    await queryRunner.createIndex(
      'user_ctms',
      new TableIndex({
        name: 'IDX_USER_CTM_CTM',
        columnNames: ['ctmId'],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.dropIndex('user_ctms', 'IDX_USER_CTM_CTM');
    await queryRunner.dropIndex('user_ctms', 'IDX_USER_CTM_USER');
    
    // Drop foreign keys
    const table = await queryRunner.getTable('user_ctms');
    if (table) {
      const foreignKeys = table.foreignKeys;
      for (const foreignKey of foreignKeys) {
        await queryRunner.dropForeignKey('user_ctms', foreignKey);
      }
    }
    
    // Drop table
    await queryRunner.dropTable('user_ctms');
  }
}
