import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class RemoveCtmFieldsFromUsers1754000004000 implements MigrationInterface {
  name = 'RemoveCtmFieldsFromUsers1754000004000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Remove CTM configuration fields from users table
    const ctmColumns = [
      'ctmIpAddress',
      'ctmUsername', 
      'ctmPassword',
      'ctmDomain',
      'seqrngIpAddress',
      'seqrngApiToken'
    ];

    for (const columnName of ctmColumns) {
      const hasColumn = await queryRunner.hasColumn('users', columnName);
      if (hasColumn) {
        await queryRunner.dropColumn('users', columnName);
        console.log(`Dropped column ${columnName} from users table`);
      }
    }

    console.log('Successfully removed CTM configuration fields from users table');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Re-add CTM configuration fields to users table
    const columnsToAdd = [
      new TableColumn({
        name: 'ctmIpAddress',
        type: 'varchar',
        length: '255',
        isNullable: true,
      }),
      new TableColumn({
        name: 'ctmUsername',
        type: 'varchar',
        length: '100',
        isNullable: true,
      }),
      new TableColumn({
        name: 'ctmPassword',
        type: 'text',
        isNullable: true,
      }),
      new TableColumn({
        name: 'ctmDomain',
        type: 'varchar',
        length: '100',
        isNullable: true,
      }),
      new TableColumn({
        name: 'seqrngIpAddress',
        type: 'varchar',
        length: '255',
        isNullable: true,
      }),
      new TableColumn({
        name: 'seqrngApiToken',
        type: 'text',
        isNullable: true,
      }),
    ];

    for (const column of columnsToAdd) {
      await queryRunner.addColumn('users', column);
    }

    console.log('Restored CTM configuration fields to users table');
  }
}
