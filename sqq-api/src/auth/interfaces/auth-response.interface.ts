export interface AuthResponse {
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: string;
    isActive: boolean;
    createdAt: Date;
    updatedAt: Date;
    // CTM Configuration fields
    ctmIpAddress?: string;
    ctmUsername?: string;
    ctmDomain?: string;
    // SeqRNG Configuration fields
    seqrngIpAddress?: string;
  };
  accessToken: string;
  refreshToken: string;
  // Servicios asignados al usuario
  services?: {
    ctms: Array<{
      id: string;
      name: string;
      description?: string;
      isActive: boolean;
      hasCompleteConfiguration: boolean;
    }>;
    hsms: Array<{
      id: string;
      name: string;
      description?: string;
      url: string;
      isActive: boolean;
      hasCompleteConfiguration: boolean;
    }>;
  };
}
