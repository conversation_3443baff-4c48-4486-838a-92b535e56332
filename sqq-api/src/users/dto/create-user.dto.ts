import { <PERSON><PERSON><PERSON>, IsS<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ength, IsO<PERSON>al, IsEnum, IsUrl, IsArray, IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { UserRole } from '../entities/user.entity';

export class CreateUserDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email del usuario',
  })
  @IsEmail()
  email: string;

  @ApiProperty({
    example: 'Juan',
    description: 'Nombre del usuario',
    minLength: 2,
    maxLength: 50,
  })
  @IsString()
  @MinLength(2)
  @MaxLength(50)
  firstName: string;

  @ApiProperty({
    example: 'Pérez',
    description: 'Apellido del usuario',
    minLength: 2,
    maxLength: 50,
  })
  @IsString()
  @MinLength(2)
  @MaxLength(50)
  lastName: string;

  @ApiProperty({
    example: 'Acme Corporation',
    description: 'Empresa del usuario',
    maxLength: 200,
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(200)
  company?: string;

  @ApiProperty({
    example: 'MiPassword123!',
    description: 'Contraseña del usuario',
    minLength: 8,
    maxLength: 128,
  })
  @IsString()
  @MinLength(8)
  @MaxLength(128)
  password: string;

  @ApiProperty({
    example: UserRole.USER,
    description: 'Rol del usuario',
    enum: UserRole,
    required: false,
  })
  @IsOptional()
  @IsEnum(UserRole)
  role?: UserRole;

  // CTM Configuration fields (optional, only for admin-created users)
  @ApiProperty({
    example: 'https://ctm.example.com:443',
    description: 'Dirección IP/URL del CipherTrust Manager',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  ctmIpAddress?: string;

  @ApiProperty({
    example: 'ctm_admin',
    description: 'Usuario del CipherTrust Manager',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  ctmUsername?: string;

  @ApiProperty({
    example: 'ctm_password123',
    description: 'Contraseña del CipherTrust Manager',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(128)
  ctmPassword?: string;

  @ApiProperty({
    example: 'root',
    description: 'Dominio del CipherTrust Manager',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  ctmDomain?: string;

  // SeqRNG Configuration fields (optional, only for admin-created users)
  @ApiProperty({
    example: 'https://seqrng.example.com:1982',
    description: 'Dirección IP/URL del SeqRNG',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  seqrngIpAddress?: string;

  @ApiProperty({
    example: '1|cifovFzUWTeAPUeAYlZYefsY3tXFf9Z1TV5WV0YL',
    description: 'Token de API del SeqRNG',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  seqrngApiToken?: string;

  // CTM and HSM assignments (optional, for admin-created users)
  @ApiProperty({
    example: ['uuid1', 'uuid2'],
    description: 'IDs de los CTMs asignados al usuario',
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  ctmIds?: string[];

  @ApiProperty({
    example: ['uuid3', 'uuid4'],
    description: 'IDs de los HSMs asignados al usuario',
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  hsmIds?: string[];
}
