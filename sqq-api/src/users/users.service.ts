import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from './entities/user.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { EncryptionService } from '../common/services/encryption.service';
import { Ctm } from '../ctm/entities/ctm.entity';
import { Hsm } from '../hsm/entities/hsm.entity';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Ctm)
    private readonly ctmRepository: Repository<Ctm>,
    @InjectRepository(Hsm)
    private readonly hsmRepository: Repository<Hsm>,
    private readonly encryptionService: EncryptionService,
  ) {}

  async create(createUserDto: CreateUserDto): Promise<User> {
    const existingUser = await this.userRepository.findOne({
      where: { email: createUserDto.email },
    });

    if (existingUser) {
      throw new ConflictException('Email already exists');
    }

    const userData = { ...createUserDto };

    // Encrypt CTM password if provided
    if (userData.ctmPassword) {
      userData.ctmPassword = this.encryptionService.encrypt(userData.ctmPassword);
    }

    // Encrypt SeqRNG API token if provided
    if (userData.seqrngApiToken) {
      userData.seqrngApiToken = this.encryptionService.encrypt(userData.seqrngApiToken);
    }

    // Remove CTM and HSM IDs from user data as they will be handled separately
    const { ctmIds, hsmIds, ...userDataWithoutRelations } = userData;

    const user = this.userRepository.create(userDataWithoutRelations);
    const savedUser = await this.userRepository.save(user);

    // Assign CTMs if provided
    if (ctmIds && ctmIds.length > 0) {
      const ctms = await this.ctmRepository.findByIds(ctmIds);
      if (ctms.length !== ctmIds.length) {
        throw new NotFoundException('One or more CTMs not found');
      }
      savedUser.ctms = ctms;
    }

    // Assign HSMs if provided
    if (hsmIds && hsmIds.length > 0) {
      const hsms = await this.hsmRepository.findByIds(hsmIds);
      if (hsms.length !== hsmIds.length) {
        throw new NotFoundException('One or more HSMs not found');
      }
      savedUser.hsms = hsms;
    }

    // Save user with relations if any were assigned
    if ((ctmIds && ctmIds.length > 0) || (hsmIds && hsmIds.length > 0)) {
      return this.userRepository.save(savedUser);
    }

    return savedUser;
  }

  async findAll(): Promise<User[]> {
    return this.userRepository.find({
      select: ['id', 'email', 'firstName', 'lastName', 'company', 'role', 'isActive', 'createdAt', 'updatedAt'],
    });
  }

  async findAllForAdmin(): Promise<User[]> {
    return this.userRepository.find({
      select: [
        'id',
        'email',
        'firstName',
        'lastName',
        'company',
        'role',
        'isActive',
        'createdAt',
        'updatedAt',
        // CTM Configuration fields
        'ctmIpAddress',
        'ctmUsername',
        'ctmDomain',
        // SeqRNG Configuration fields
        'seqrngIpAddress',
        // Note: ctmPassword and seqrngApiToken are excluded for security
      ],
      relations: ['ctms', 'hsms'],
    });
  }

  async findOne(id: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id },
      select: ['id', 'email', 'firstName', 'lastName', 'company', 'role', 'isActive', 'createdAt', 'updatedAt'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }

  async findOneForAdmin(id: string): Promise<User> {
    const user = await this.userRepository.findOne({
      where: { id },
      select: [
        'id',
        'email',
        'firstName',
        'lastName',
        'company',
        'role',
        'isActive',
        'createdAt',
        'updatedAt',
        // CTM Configuration fields
        'ctmIpAddress',
        'ctmUsername',
        'ctmDomain',
        // SeqRNG Configuration fields
        'seqrngIpAddress',
        // Note: ctmPassword and seqrngApiToken are excluded for security
      ],
      relations: ['ctms', 'hsms'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }

  async findByEmail(email: string): Promise<User | null> {
    return this.userRepository.findOne({
      where: { email },
    });
  }

  async update(id: string, updateUserDto: UpdateUserDto): Promise<User> {
    // First, get the current user with all fields including encrypted ones and relations
    const user = await this.userRepository.findOne({
      where: { id },
      select: [
        'id', 'email', 'firstName', 'lastName', 'company', 'role', 'isActive',
        'ctmIpAddress', 'ctmUsername', 'ctmPassword', 'ctmDomain',
        'seqrngIpAddress', 'seqrngApiToken', 'createdAt', 'updatedAt'
      ],
      relations: ['ctms', 'hsms'],
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (updateUserDto.email && updateUserDto.email !== user.email) {
      const existingUser = await this.userRepository.findOne({
        where: { email: updateUserDto.email },
      });

      if (existingUser) {
        throw new ConflictException('Email already exists');
      }
    }

    // Extract CTM and HSM IDs from update data
    const { ctmIds, hsmIds, ...updateData } = updateUserDto;

    // Handle sensitive fields - preserve existing values if null, undefined, or empty strings are provided
    if (updateData.ctmPassword === null || updateData.ctmPassword === undefined) {
      delete updateData.ctmPassword;
    } else if (updateData.ctmPassword === '') {
      // Empty string means don't update - preserve existing value
      delete updateData.ctmPassword;
    } else if (updateData.ctmPassword) {
      // Encrypt CTM password if provided and not empty
      updateData.ctmPassword = this.encryptionService.encrypt(updateData.ctmPassword);
    }

    if (updateData.seqrngApiToken === null || updateData.seqrngApiToken === undefined) {
      delete updateData.seqrngApiToken;
    } else if (updateData.seqrngApiToken === '') {
      // Empty string means don't update - preserve existing value
      delete updateData.seqrngApiToken;
    } else if (updateData.seqrngApiToken) {
      // Encrypt SeqRNG API token if provided and not empty
      updateData.seqrngApiToken = this.encryptionService.encrypt(updateData.seqrngApiToken);
    }

    // Update basic user data
    Object.assign(user, updateData);

    // Update CTM relations if provided
    if (ctmIds !== undefined) {
      if (ctmIds.length > 0) {
        const ctms = await this.ctmRepository.findByIds(ctmIds);
        if (ctms.length !== ctmIds.length) {
          throw new NotFoundException('One or more CTMs not found');
        }
        user.ctms = ctms;
      } else {
        user.ctms = [];
      }
    }

    // Update HSM relations if provided
    if (hsmIds !== undefined) {
      if (hsmIds.length > 0) {
        const hsms = await this.hsmRepository.findByIds(hsmIds);
        if (hsms.length !== hsmIds.length) {
          throw new NotFoundException('One or more HSMs not found');
        }
        user.hsms = hsms;
      } else {
        user.hsms = [];
      }
    }

    return this.userRepository.save(user);
  }

  async updateRefreshToken(userId: string, refreshToken: string | null): Promise<void> {
    await this.userRepository.update(userId, { refreshToken: refreshToken });
  }

  async findByIdWithPassword(id: string): Promise<User | null> {
    return this.userRepository.findOne({
      where: { id },
      select: [
        'id', 'email', 'firstName', 'lastName', 'company', 'role', 'isActive',
        'password', 'createdAt', 'updatedAt'
      ],
    });
  }

  async changePassword(userId: string, newPassword: string): Promise<void> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Actualizar la contraseña (se hasheará automáticamente por el hook @BeforeUpdate)
    user.password = newPassword;
    await this.userRepository.save(user);
  }

  async remove(id: string): Promise<void> {
    const user = await this.findOne(id);
    await this.userRepository.remove(user);
  }


}
