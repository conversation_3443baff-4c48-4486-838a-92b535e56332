/**
 * Script de prueba para verificar el endpoint /keys/by-hsm
 * Este script simula una llamada HTTP al endpoint para verificar que funciona correctamente
 */

const axios = require('axios');

// Configuración
const BASE_URL = 'http://localhost:3000';
const TEST_HSM_ID = 'e0d82f93-efbd-4c30-945f-b4c8e16ad38b'; // Reemplazar con un HSM ID real
const TEST_TOKEN = 'your-jwt-token-here'; // Reemplazar con un token JWT válido

async function testHsmEndpoint() {
  console.log('🧪 Iniciando pruebas del endpoint /keys/by-hsm...\n');

  try {
    // Prueba 1: Endpoint básico con hsmId
    console.log('=== PRUEBA 1: Endpoint básico ===');
    const url1 = `${BASE_URL}/keys/by-hsm?hsmId=${TEST_HSM_ID}&page=1&limit=10`;
    console.log('URL:', url1);
    
    try {
      const response1 = await axios.get(url1, {
        headers: {
          'Authorization': `Bearer ${TEST_TOKEN}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('✅ Status:', response1.status);
      console.log('✅ Response structure:', {
        hasKeys: Array.isArray(response1.data.keys),
        keysCount: response1.data.keys?.length || 0,
        total: response1.data.total,
        page: response1.data.page,
        limit: response1.data.limit,
        totalPages: response1.data.totalPages
      });
      console.log('✅ Prueba 1 exitosa\n');
      
    } catch (error) {
      if (error.response) {
        console.log('❌ Status:', error.response.status);
        console.log('❌ Error:', error.response.data);
        
        if (error.response.status === 400 && 
            error.response.data.message?.includes('HSM keys should be accessed through')) {
          console.log('🚨 PROBLEMA ENCONTRADO: El método getHsmKeys todavía tiene el código viejo');
          console.log('🔧 SOLUCIÓN: Reiniciar el servidor backend para aplicar los cambios');
        }
      } else {
        console.log('❌ Error de red:', error.message);
      }
      console.log('❌ Prueba 1 falló\n');
    }

    // Prueba 2: Endpoint con filtros adicionales
    console.log('=== PRUEBA 2: Endpoint con filtros ===');
    const url2 = `${BASE_URL}/keys/by-hsm?hsmId=${TEST_HSM_ID}&page=1&limit=5&type=aes&status=ACTIVE`;
    console.log('URL:', url2);
    
    try {
      const response2 = await axios.get(url2, {
        headers: {
          'Authorization': `Bearer ${TEST_TOKEN}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('✅ Status:', response2.status);
      console.log('✅ Filtros aplicados correctamente');
      console.log('✅ Prueba 2 exitosa\n');
      
    } catch (error) {
      if (error.response) {
        console.log('❌ Status:', error.response.status);
        console.log('❌ Error:', error.response.data);
      } else {
        console.log('❌ Error de red:', error.message);
      }
      console.log('❌ Prueba 2 falló\n');
    }

    // Prueba 3: Endpoint sin hsmId (debe fallar)
    console.log('=== PRUEBA 3: Endpoint sin hsmId (debe fallar) ===');
    const url3 = `${BASE_URL}/keys/by-hsm?page=1&limit=10`;
    console.log('URL:', url3);
    
    try {
      const response3 = await axios.get(url3, {
        headers: {
          'Authorization': `Bearer ${TEST_TOKEN}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('❌ No debería haber funcionado sin hsmId');
      console.log('❌ Prueba 3 falló (validación no funciona)\n');
      
    } catch (error) {
      if (error.response && error.response.status === 400) {
        console.log('✅ Status:', error.response.status);
        console.log('✅ Error esperado:', error.response.data.message);
        console.log('✅ Prueba 3 exitosa (validación funciona)\n');
      } else {
        console.log('❌ Error inesperado:', error.response?.data || error.message);
        console.log('❌ Prueba 3 falló\n');
      }
    }

    // Prueba 4: Verificar estructura de respuesta
    console.log('=== PRUEBA 4: Verificar estructura de respuesta ===');
    try {
      const response4 = await axios.get(`${BASE_URL}/keys/by-hsm?hsmId=${TEST_HSM_ID}&page=1&limit=1`, {
        headers: {
          'Authorization': `Bearer ${TEST_TOKEN}`,
          'Content-Type': 'application/json'
        }
      });
      
      const expectedFields = ['keys', 'total', 'page', 'limit', 'totalPages', 'successful', 'failed', 'uploadedToCtm'];
      const actualFields = Object.keys(response4.data);
      
      console.log('✅ Campos esperados:', expectedFields);
      console.log('✅ Campos recibidos:', actualFields);
      
      const missingFields = expectedFields.filter(field => !actualFields.includes(field));
      const extraFields = actualFields.filter(field => !expectedFields.includes(field));
      
      if (missingFields.length === 0 && extraFields.length === 0) {
        console.log('✅ Estructura de respuesta correcta');
        console.log('✅ Prueba 4 exitosa\n');
      } else {
        console.log('❌ Campos faltantes:', missingFields);
        console.log('❌ Campos extra:', extraFields);
        console.log('❌ Prueba 4 falló\n');
      }
      
    } catch (error) {
      console.log('❌ Error en prueba de estructura:', error.response?.data || error.message);
      console.log('❌ Prueba 4 falló\n');
    }

  } catch (error) {
    console.error('❌ Error general en las pruebas:', error.message);
  }
}

// Función para verificar si el servidor está corriendo
async function checkServerStatus() {
  try {
    const response = await axios.get(`${BASE_URL}/health`, { timeout: 5000 });
    console.log('✅ Servidor backend está corriendo');
    return true;
  } catch (error) {
    console.log('❌ Servidor backend no está disponible');
    console.log('🔧 Asegúrate de que el servidor esté corriendo en', BASE_URL);
    return false;
  }
}

// Ejecutar las pruebas
async function main() {
  console.log('🚀 Verificación del endpoint /keys/by-hsm\n');
  
  // Verificar que el servidor esté corriendo
  const serverRunning = await checkServerStatus();
  if (!serverRunning) {
    console.log('\n❌ No se pueden ejecutar las pruebas sin el servidor backend');
    return;
  }
  
  // Verificar configuración
  if (TEST_TOKEN === 'your-jwt-token-here') {
    console.log('⚠️  ADVERTENCIA: Usando token de prueba por defecto');
    console.log('🔧 Para pruebas reales, reemplaza TEST_TOKEN con un JWT válido\n');
  }
  
  if (TEST_HSM_ID === 'e0d82f93-efbd-4c30-945f-b4c8e16ad38b') {
    console.log('⚠️  ADVERTENCIA: Usando HSM ID de prueba por defecto');
    console.log('🔧 Para pruebas reales, reemplaza TEST_HSM_ID con un HSM ID válido\n');
  }
  
  // Ejecutar pruebas
  await testHsmEndpoint();
  
  console.log('🏁 Pruebas completadas');
  console.log('\n📋 RESUMEN:');
  console.log('- Si ves el error "HSM keys should be accessed through...", reinicia el servidor backend');
  console.log('- Si ves errores 401/403, verifica el token JWT');
  console.log('- Si ves errores 404, verifica que el HSM ID exista en la base de datos');
  console.log('- Si todas las pruebas pasan, el endpoint está funcionando correctamente');
}

// Ejecutar si se llama directamente
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testHsmEndpoint, checkServerStatus };
