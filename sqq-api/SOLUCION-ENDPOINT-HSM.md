# 🔧 Solución: Error "HSM keys should be accessed through /hsm-keys/by-hsm/:hsmId endpoint"

## 🚨 Problema
El frontend está recibiendo el error:
```json
{
    "message": "HSM keys should be accessed through /hsm-keys/by-hsm/:hsmId endpoint",
    "error": "Bad Request",
    "statusCode": 400,
    "timestamp": "2025-08-11T05:19:46.906Z",
    "path": "/keys/by-hsm?page=1&limit=100&hsmId=e0d82f93-efbd-4c30-945f-b4c8e16ad38b",
    "method": "GET"
}
```

## ✅ Solución Implementada

He implementado completamente el endpoint `/keys/by-hsm` con paginación en el backend NestJS. Los cambios incluyen:

### **1. Nuevo DTO para HSM Keys**
**Archivo**: `sqq-api/src/keys/dto/get-hsm-keys.dto.ts`
- ✅ Validación UUID para `hsmId`
- ✅ Parámetros de paginación (`page`, `limit`)
- ✅ Filtros (`type`, `algorithm`, `status`, `search`)

### **2. Controlador Actualizado**
**Archivo**: `sqq-api/src/keys/keys.controller.ts`
- ✅ Endpoint `@Get('by-hsm')` implementado
- ✅ Usa `GetHsmKeysDto` para validación
- ✅ Retorna `KeyListResponseDto` para consistencia

### **3. Servicio Implementado**
**Archivo**: `sqq-api/src/keys/keys.service.ts`
- ✅ Método `getHsmKeys()` completamente implementado
- ✅ Integración con `HsmKeyService`
- ✅ Paginación manual
- ✅ Conversión de formato HSM a formato frontend

### **4. Módulo Actualizado**
**Archivo**: `sqq-api/src/keys/keys.module.ts`
- ✅ Importado `HsmModule` para acceso a `HsmKeyService`

### **5. Frontend Actualizado**
**Archivos**: 
- `sqq-frontend/src/services/config/apiConfig.js`
- `sqq-frontend/src/services/keys/keyService.js`
- `sqq-frontend/src/hooks/useKeys.js`
- ✅ Endpoint `BY_HSM: '/keys/by-hsm'` configurado
- ✅ Método `getKeysByHsm()` implementado
- ✅ Soporte completo para paginación y filtros

## 🔄 Pasos para Resolver el Error

### **Paso 1: Verificar que los cambios están aplicados**
Los archivos han sido modificados correctamente. El método `getHsmKeys` en `keys.service.ts` ya NO lanza el error.

### **Paso 2: Reiniciar el servidor backend**
El error sugiere que el servidor está ejecutando código viejo. **REINICIA EL SERVIDOR BACKEND**:

```bash
# En el directorio sqq-api
npm run start:dev
# o
yarn start:dev
```

### **Paso 3: Verificar con el script de prueba**
Ejecuta el script de verificación:

```bash
# En el directorio sqq-api
node test-hsm-endpoint.js
```

### **Paso 4: Verificar en el frontend**
Una vez reiniciado el backend, el frontend debería funcionar correctamente.

## 📋 Verificación Manual

### **Request Esperado**:
```
GET /keys/by-hsm?hsmId=e0d82f93-efbd-4c30-945f-b4c8e16ad38b&page=1&limit=100
Authorization: Bearer <jwt-token>
```

### **Response Esperado**:
```json
{
  "keys": [
    {
      "id": "key-uuid",
      "name": "test-hsm-key",
      "algorithm": "aes",
      "type": "aes",
      "keySize": 256,
      "num_bytes": 32,
      "status": "ACTIVE",
      "isHsmKey": true,
      "hsmId": "hsm-uuid"
    }
  ],
  "total": 25,
  "page": 1,
  "limit": 100,
  "totalPages": 1,
  "successful": 20,
  "failed": 5,
  "uploadedToCtm": 0
}
```

## 🛠️ Troubleshooting

### **Si el error persiste después del reinicio:**

1. **Verificar logs del servidor**:
   ```bash
   # Buscar errores de inyección de dependencias
   grep -i "hsmkeyservice" logs/
   ```

2. **Verificar que HsmModule se importa correctamente**:
   - Archivo: `sqq-api/src/keys/keys.module.ts`
   - Debe incluir `HsmModule` en imports

3. **Verificar que HsmKeyService se exporta**:
   - Archivo: `sqq-api/src/hsm/hsm.module.ts`
   - Debe exportar `HsmKeyService`

4. **Limpiar caché de Node.js**:
   ```bash
   rm -rf node_modules/.cache
   npm run build
   ```

### **Si hay errores de validación:**

1. **Verificar que hsmId es un UUID válido**
2. **Verificar que el usuario tiene acceso al HSM**
3. **Verificar que el HSM existe y está activo**

## 🎯 Estado Final

Una vez aplicados todos los cambios y reiniciado el servidor:

- ✅ El endpoint `/keys/by-hsm` funciona con paginación
- ✅ El frontend puede obtener llaves HSM correctamente
- ✅ Los filtros y la paginación funcionan
- ✅ El formato de respuesta es consistente con `/keys/by-ctm`
- ✅ No más errores "HSM keys should be accessed through..."

## 🚀 Próximos Pasos

1. **Reiniciar el servidor backend** (paso más importante)
2. **Probar el endpoint** con el script de verificación
3. **Verificar en el frontend** que las llaves HSM se cargan correctamente
4. **Eliminar archivos de documentación obsoletos** que contengan código viejo

El endpoint está completamente implementado y debería funcionar una vez que el servidor se reinicie con los nuevos cambios.
