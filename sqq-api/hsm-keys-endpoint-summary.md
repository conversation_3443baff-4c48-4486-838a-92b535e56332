# 🔧 Implementación del Endpoint `/keys/by-hsm` con Paginación

## Resumen de Cambios

Se ha implementado correctamente el endpoint `/keys/by-hsm` en el backend NestJS para permitir obtener llaves HSM con paginación, similar al endpoint `/keys/by-ctm`.

## Cambios en el Backend

### **1. Nuevo DTO: `GetHsmKeysDto`**
**Archivo**: `sqq-api/src/keys/dto/get-hsm-keys.dto.ts`

```typescript
export class GetHsmKeysDto {
  @IsUUID(4, { message: 'hsmId must be a UUID' })
  hsmId: string;

  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'page must be an integer' })
  @Min(1, { message: 'page must be at least 1' })
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'limit must be an integer' })
  @Min(1, { message: 'limit must be at least 1' })
  @Max(100, { message: 'limit must be at most 100' })
  limit?: number = 10;

  @IsOptional()
  @IsString()
  @IsIn(['aes', 'rsa', 'ec', 'kyberkn', 'dilithiumkn'])
  type?: string;

  @IsOptional()
  @IsString()
  @IsIn(['aes', 'rsa', 'ec', 'kyberkn', 'dilithiumkn'])
  algorithm?: string;

  @IsOptional()
  @IsString()
  @IsIn(['ACTIVE', 'INACTIVE', 'FAILED'])
  status?: string;

  @IsOptional()
  @IsString()
  search?: string;
}
```

### **2. Actualización del Controlador**
**Archivo**: `sqq-api/src/keys/keys.controller.ts`

- ✅ Importado `GetHsmKeysDto`
- ✅ Actualizado endpoint `@Get('by-hsm')` para usar el nuevo DTO
- ✅ Mejorada documentación con Swagger
- ✅ Retorna `KeyListResponseDto` para consistencia

```typescript
@Get('by-hsm')
async getHsmKeys(
  @Query() getHsmKeysDto: GetHsmKeysDto,
  @Request() req: any,
): Promise<KeyListResponseDto> {
  const userId = req.user.id;
  const { hsmId, ...keysDto } = getHsmKeysDto;
  return this.keysService.getHsmKeys(userId, hsmId, keysDto as GetKeysDto);
}
```

### **3. Actualización del Servicio**
**Archivo**: `sqq-api/src/keys/keys.service.ts`

- ✅ Importado `HsmKeyService`
- ✅ Inyectado `HsmKeyService` en el constructor
- ✅ Implementado método `getHsmKeys()` con paginación manual
- ✅ Conversión de formato HSM a formato compatible con frontend

```typescript
async getHsmKeys(userId: string, hsmId: string, filters: GetKeysDto): Promise<KeyListResponseDto> {
  // Convierte filtros y obtiene llaves HSM
  const hsmKeys = await this.hsmKeyService.findKeysByHsm(userId, hsmId, hsmFilters);
  
  // Aplica paginación manual
  const paginatedKeys = hsmKeys.slice(startIndex, endIndex);
  
  // Convierte formato HSM a formato frontend
  const keys = paginatedKeys.map(hsmKey => ({
    id: hsmKey.id,
    name: hsmKey.keyName,
    algorithm: hsmKey.keyType,
    type: hsmKey.keyType,
    keySize: hsmKey.keySize,
    num_bytes: hsmKey.keySize ? Math.ceil(hsmKey.keySize / 8) : null,
    status: hsmKey.status,
    isHsmKey: true,
    // ... otros campos
  }));

  return {
    keys,
    total: hsmKeys.length,
    page,
    limit,
    totalPages: Math.ceil(hsmKeys.length / limit),
    successful: hsmKeys.filter(k => k.status === 'ACTIVE').length,
    failed: hsmKeys.filter(k => k.status === 'FAILED').length,
    uploadedToCtm: 0,
  };
}
```

### **4. Actualización del Módulo**
**Archivo**: `sqq-api/src/keys/keys.module.ts`

- ✅ Importado `HsmModule` para acceso a `HsmKeyService`

## Cambios en el Frontend

### **1. Configuración de API**
**Archivo**: `sqq-frontend/src/services/config/apiConfig.js`

- ✅ Agregado endpoint `BY_HSM: '/keys/by-hsm'`

### **2. Servicio de Llaves**
**Archivo**: `sqq-frontend/src/services/keys/keyService.js`

- ✅ Implementado método `getKeysByHsm(filters)`
- ✅ Soporte para parámetros de paginación (`page`, `limit`)
- ✅ Soporte para filtros (`hsmId`, `type`, `algorithm`, `status`, `search`)
- ✅ Logging de seguridad

### **3. Hook useKeys**
**Archivo**: `sqq-frontend/src/hooks/useKeys.js`

- ✅ Actualizado `getKeysByHsm()` para usar el nuevo endpoint
- ✅ Soporte para paginación del servidor
- ✅ Conversión de formato HSM a formato compatible
- ✅ Manejo de estadísticas

### **4. Índice de Servicios**
**Archivo**: `sqq-frontend/src/services/index.js`

- ✅ Agregado `getByHsm: (filters) => keyService.getKeysByHsm(filters)`

## Formato de Request/Response

### **Request**
```
GET /keys/by-hsm?hsmId=e0d82f93-efbd-4c30-945f-b4c8e16ad38b&page=1&limit=10&type=aes&status=ACTIVE
```

### **Response**
```json
{
  "keys": [
    {
      "id": "key-uuid",
      "name": "test-hsm-key",
      "algorithm": "aes",
      "type": "aes",
      "keySize": 256,
      "num_bytes": 32,
      "status": "ACTIVE",
      "createdAt": "2025-08-11T05:00:00.000Z",
      "isHsmKey": true,
      "hsmId": "hsm-uuid"
    }
  ],
  "total": 25,
  "page": 1,
  "limit": 10,
  "totalPages": 3,
  "successful": 20,
  "failed": 5,
  "uploadedToCtm": 0
}
```

## Validaciones Implementadas

1. **hsmId**: Debe ser un UUID válido
2. **page**: Entero >= 1
3. **limit**: Entero entre 1 y 100
4. **type/algorithm**: Debe ser uno de: `aes`, `rsa`, `ec`, `kyberkn`, `dilithiumkn`
5. **status**: Debe ser uno de: `ACTIVE`, `INACTIVE`, `FAILED`
6. **search**: String para búsqueda por nombre

## Estado Final

🎉 **El endpoint `/keys/by-hsm` está completamente implementado y funcional**:

- ✅ Backend acepta parámetros de paginación
- ✅ Frontend usa el endpoint correcto
- ✅ Soporte completo para filtros
- ✅ Formato de respuesta consistente con `/keys/by-ctm`
- ✅ Validaciones apropiadas
- ✅ Logging de seguridad
- ✅ Documentación Swagger

El endpoint ahora funciona exactamente como `/keys/by-ctm` pero para llaves HSM, con paginación completa y filtros.
