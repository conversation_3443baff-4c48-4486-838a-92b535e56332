#!/bin/bash

# SequreQuantum Deployment Script
# Este script automatiza el despliegue de la aplicación SequreQuantum

set -e

echo "🚀 SequreQuantum Deployment Script"
echo "=================================="

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Función para imprimir mensajes coloreados
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Verificar si Docker está instalado
check_docker() {
    print_status "Verificando Docker..."
    if ! command -v docker &> /dev/null; then
        print_error "Docker no está instalado. Por favor instala Docker primero."
        echo "Ejecuta: curl -fsSL https://get.docker.com -o get-docker.sh && sudo sh get-docker.sh"
        exit 1
    fi
    print_success "Docker está instalado"
}

# Verificar si Docker Compose está instalado
check_docker_compose() {
    print_status "Verificando Docker Compose..."
    if ! docker compose version &> /dev/null; then
        print_error "Docker Compose no está instalado."
        echo "Ejecuta: sudo apt install docker-compose-plugin -y"
        exit 1
    fi
    print_success "Docker Compose está instalado"
}

# Verificar permisos de Docker
check_docker_permissions() {
    print_status "Verificando permisos de Docker..."
    if ! docker ps &> /dev/null; then
        print_warning "No tienes permisos para ejecutar Docker sin sudo."
        print_warning "Agregando usuario al grupo docker..."
        sudo usermod -aG docker $USER
        print_warning "Necesitas cerrar sesión y volver a entrar para aplicar los cambios."
        print_warning "O ejecuta: newgrp docker"
        exit 1
    fi
    print_success "Permisos de Docker OK"
}

# Verificar archivo .env
check_env_file() {
    print_status "Verificando archivo .env..."
    if [ ! -f ".env" ]; then
        print_error "Archivo .env no encontrado."
        print_status "Creando archivo .env desde plantilla..."
        cp .env.example .env 2>/dev/null || {
            print_error "No se encontró .env.example. Asegúrate de tener el archivo .env configurado."
            exit 1
        }
        print_warning "Archivo .env creado. Por favor edítalo con tus configuraciones antes de continuar."
        print_warning "Ejecuta: nano .env"
        exit 1
    fi
    
    # Verificar permisos del archivo .env
    chmod 600 .env
    print_success "Archivo .env encontrado y permisos configurados"
}

# Verificar estructura de directorios
check_project_structure() {
    print_status "Verificando estructura del proyecto..."
    
    required_dirs=("sqq-api" "sqq-frontend" "sqq-seqrng-api")
    for dir in "${required_dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            print_error "Directorio $dir no encontrado."
            print_error "Asegúrate de estar en el directorio raíz del proyecto."
            exit 1
        fi
    done
    
    required_files=("docker-compose.yml" "sqq-api/Dockerfile" "sqq-frontend/Dockerfile" "sqq-seqrng-api/Dockerfile")
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            print_error "Archivo $file no encontrado."
            exit 1
        fi
    done
    
    print_success "Estructura del proyecto OK"
}

# Construir imágenes
build_images() {
    print_status "Construyendo imágenes Docker..."
    docker compose build --no-cache
    print_success "Imágenes construidas exitosamente"
}

# Levantar servicios
start_services() {
    print_status "Iniciando servicios..."
    docker compose up -d
    print_success "Servicios iniciados"
}

# Verificar estado de servicios
check_services() {
    print_status "Verificando estado de servicios..."
    sleep 10
    
    # Verificar que todos los contenedores estén corriendo
    if docker compose ps | grep -q "Exit"; then
        print_error "Algunos servicios no iniciaron correctamente:"
        docker compose ps
        print_status "Mostrando logs de servicios con errores..."
        docker compose logs
        exit 1
    fi
    
    print_success "Todos los servicios están corriendo"
}

# Mostrar información de acceso
show_access_info() {
    echo ""
    print_success "🎉 ¡Despliegue completado exitosamente!"
    echo ""
    echo "📱 Acceso a la aplicación:"
    echo "  Frontend:     http://$(hostname -I | awk '{print $1}')"
    echo "  NestJS API:   http://$(hostname -I | awk '{print $1}'):3000"
    echo "  API Docs:     http://$(hostname -I | awk '{print $1}'):3000/api"
    echo "  Flask API:    http://$(hostname -I | awk '{print $1}'):3001"
    echo "  Flask Docs:   http://$(hostname -I | awk '{print $1}'):3001/docs"
    echo ""
    echo "🔧 Comandos útiles:"
    echo "  Ver logs:           docker compose logs -f"
    echo "  Parar servicios:    docker compose down"
    echo "  Reiniciar:          docker compose restart"
    echo "  Estado servicios:   docker compose ps"
    echo ""
    echo "📖 Para más información, consulta DEPLOYMENT.md"
}

# Función principal
main() {
    echo ""
    print_status "Iniciando verificaciones previas..."
    
    check_docker
    check_docker_compose
    check_docker_permissions
    check_project_structure
    check_env_file
    
    echo ""
    print_status "Iniciando despliegue..."
    
    build_images
    start_services
    check_services
    
    show_access_info
}

# Manejar interrupciones
trap 'print_error "Despliegue interrumpido"; exit 1' INT TERM

# Ejecutar función principal
main "$@"
