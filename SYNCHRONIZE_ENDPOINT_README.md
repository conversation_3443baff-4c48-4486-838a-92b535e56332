# CTM Synchronization Endpoint

## Descripción

El endpoint de sincronización permite sincronizar las claves existentes en el ambiente CTM del usuario con la base de datos local. Este endpoint:

1. Obtiene todas las claves del ambiente CTM del usuario
2. Compara con las claves existentes en la base de datos (usando `ctmKeyId`)
3. Guarda solo las claves que no existen previamente en la base de datos
4. Retorna un resumen detallado de la sincronización

## Endpoints Implementados

### 1. SeQRNG API - Listar Claves de CTM

**Endpoint:** `POST /api/v1/ctm/keys/list`

**Descripción:** Obtiene todas las claves del ambiente CTM con paginación y filtros opcionales.

**Request Body:**
```json
{
  "ctm_config": {
    "ip_address": "*************",
    "username": "ctm_user",
    "password": "ctm_password",
    "domain": "ctm_domain"
  },
  "limit": 100,
  "offset": 0,
  "name_filter": "optional_filter"
}
```

**Response:**
```json
{
  "status": "success",
  "message": "Keys retrieved successfully from CTM",
  "data": {
    "success": true,
    "keys": [
      {
        "id": "24aae5e5-b627-4b0e-964a-f48af998ee2b",
        "uri": "kylo:kylo:vault:keys:1480633212136-v0",
        "name": "1480633212136",
        "algorithm": "AES",
        "size": 256,
        "state": "Active",
        "unexportable": false,
        "objectType": "Symmetric Key",
        "createdAt": "2016-12-01T23:00:10.072423Z"
      }
    ],
    "total": 1,
    "response_info": {
      "type": "paginated",
      "total": 1,
      "limit": 100,
      "offset": 0,
      "has_more": false
    }
  }
}
```

### 2. NestJS API - Sincronizar Claves

**Endpoint:** `POST /keys/synchronize`

**Descripción:** Sincroniza las claves del ambiente CTM del usuario con la base de datos local.

**Headers:**
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body:** Vacío (usa la configuración CTM del usuario autenticado)

**Response:**
```json
{
  "status": "success",
  "message": "Synchronized 5 new keys from CTM",
  "data": {
    "totalCtmKeys": 10,
    "existingInDatabase": 5,
    "newKeysSynchronized": 5,
    "failedSynchronizations": 0,
    "synchronizedKeys": [
      {
        "id": "uuid-generated-id",
        "ctmKeyId": "24aae5e5-b627-4b0e-964a-f48af998ee2b",
        "name": "1480633212136",
        "algorithm": "AES",
        "status": "synchronized"
      }
    ],
    "failedKeys": []
  }
}
```

## Lógica de Sincronización

1. **Obtener claves de CTM:** Se conecta al ambiente CTM del usuario y obtiene todas las claves disponibles
2. **Verificar existencia:** Compara los `ctmKeyId` con las claves existentes en la base de datos
3. **Filtrar nuevas claves:** Solo procesa las claves que no existen en la base de datos
4. **Mapear datos:** Convierte los datos de CTM al formato de la base de datos:
   - `algorithm`: Mapea algoritmos de CTM a enums locales
   - `type`: Determina el tipo de clave basado en el algoritmo
   - `status`: Se marca como `UPLOADED_TO_CTM`
   - `uploadedToCtm`: Se establece en `true`
   - `active`: Basado en el estado de la clave en CTM
5. **Guardar en base de datos:** Crea registros para las nuevas claves
6. **Retornar resumen:** Proporciona estadísticas detalladas de la sincronización

## Campos Mapeados

| Campo CTM | Campo Base de Datos | Descripción |
|-----------|-------------------|-------------|
| `id` | `ctmKeyId` | ID único de la clave en CTM |
| `name` | `ctmKeyName` y `name` | Nombre de la clave |
| `algorithm` | `algorithm` | Algoritmo mapeado a enum local |
| `size` | `numBytes` | Tamaño de la clave en bytes |
| `state` | `active` | Estado activo basado en CTM |
| `unexportable` | `exportable` | Invertido (unexportable -> !exportable) |

## Manejo de Errores

- **403 Forbidden:** Usuario no tiene configuración CTM
- **400 Bad Request:** Error en la sincronización o configuración inválida
- **500 Internal Server Error:** Error del servidor CTM o SeQRNG

## Casos de Uso

1. **Sincronización inicial:** Cuando un usuario configura CTM por primera vez
2. **Sincronización periódica:** Para mantener la base de datos actualizada
3. **Recuperación de claves:** Después de una migración o restauración

## Consideraciones de Seguridad

- Requiere autenticación JWT válida
- Usa la configuración CTM específica del usuario autenticado
- Las contraseñas CTM están encriptadas en la base de datos
- No expone material de claves, solo metadatos

## Pruebas

Ejecutar el script de prueba:

```bash
python test_synchronize_endpoint.py
```

**Nota:** Actualizar las variables `TEST_CTM_CONFIG` y `TEST_USER` con valores reales antes de ejecutar las pruebas.
