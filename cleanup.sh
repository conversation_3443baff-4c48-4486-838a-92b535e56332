#!/bin/bash

# SequreQuantum Cleanup Script
# Este script ayuda con tareas de limpieza y mantenimiento

set -e

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}=================================="
    echo -e "  SequreQuantum Cleanup Script"
    echo -e "==================================${NC}"
    echo ""
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Función para confirmar acciones peligrosas
confirm_action() {
    echo -e "${YELLOW}$1${NC}"
    read -p "¿Estás seguro? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Operación cancelada."
        return 1
    fi
    return 0
}

# Parar servicios
stop_services() {
    print_status "Parando servicios..."
    docker compose down
    print_success "Servicios parados"
}

# Limpiar contenedores parados
clean_containers() {
    print_status "Limpiando contenedores parados..."
    docker container prune -f
    print_success "Contenedores parados eliminados"
}

# Limpiar imágenes no utilizadas
clean_images() {
    print_status "Limpiando imágenes no utilizadas..."
    docker image prune -f
    print_success "Imágenes no utilizadas eliminadas"
}

# Limpiar volúmenes no utilizados
clean_volumes() {
    if confirm_action "⚠️  CUIDADO: Esto eliminará volúmenes no utilizados (incluyendo datos de base de datos si no están en uso)"; then
        print_status "Limpiando volúmenes no utilizados..."
        docker volume prune -f
        print_success "Volúmenes no utilizados eliminados"
    fi
}

# Limpiar redes no utilizadas
clean_networks() {
    print_status "Limpiando redes no utilizadas..."
    docker network prune -f
    print_success "Redes no utilizadas eliminadas"
}

# Limpieza completa del sistema Docker
full_docker_cleanup() {
    if confirm_action "⚠️  CUIDADO: Esto eliminará TODOS los contenedores, imágenes, volúmenes y redes no utilizados"; then
        print_status "Realizando limpieza completa del sistema Docker..."
        docker system prune -a -f --volumes
        print_success "Limpieza completa del sistema Docker completada"
    fi
}

# Eliminar datos de la aplicación (PELIGROSO)
remove_app_data() {
    if confirm_action "🚨 PELIGRO: Esto eliminará TODOS los datos de la aplicación incluyendo la base de datos"; then
        if confirm_action "🚨 ÚLTIMA CONFIRMACIÓN: ¿Realmente quieres eliminar todos los datos?"; then
            print_status "Parando servicios..."
            docker compose down -v
            print_status "Eliminando volúmenes de la aplicación..."
            docker volume rm $(docker volume ls -q | grep sequrequantum) 2>/dev/null || true
            print_success "Datos de la aplicación eliminados"
        fi
    fi
}

# Crear backup de la base de datos
backup_database() {
    print_status "Creando backup de la base de datos..."
    
    # Verificar si PostgreSQL está corriendo
    if ! docker compose ps postgres | grep -q "Up"; then
        print_error "PostgreSQL no está corriendo. Inicia los servicios primero."
        return 1
    fi
    
    # Crear directorio de backups si no existe
    mkdir -p backups
    
    # Crear backup con timestamp
    timestamp=$(date +"%Y%m%d_%H%M%S")
    backup_file="backups/sqq_database_backup_$timestamp.sql"
    
    docker compose exec -T postgres pg_dump -U sqq_user sqq_database > "$backup_file"
    
    if [ -f "$backup_file" ]; then
        print_success "Backup creado: $backup_file"
    else
        print_error "Error al crear backup"
        return 1
    fi
}

# Restaurar backup de la base de datos
restore_database() {
    print_status "Listando backups disponibles..."
    
    if [ ! -d "backups" ] || [ -z "$(ls -A backups/)" ]; then
        print_error "No se encontraron backups en el directorio backups/"
        return 1
    fi
    
    echo "Backups disponibles:"
    ls -la backups/*.sql 2>/dev/null || {
        print_error "No se encontraron archivos de backup (.sql)"
        return 1
    }
    
    echo ""
    read -p "Ingresa el nombre del archivo de backup (sin la ruta): " backup_file
    
    if [ ! -f "backups/$backup_file" ]; then
        print_error "Archivo de backup no encontrado: backups/$backup_file"
        return 1
    fi
    
    if confirm_action "⚠️  Esto sobrescribirá la base de datos actual con el backup seleccionado"; then
        print_status "Restaurando backup..."
        
        # Verificar si PostgreSQL está corriendo
        if ! docker compose ps postgres | grep -q "Up"; then
            print_error "PostgreSQL no está corriendo. Inicia los servicios primero."
            return 1
        fi
        
        docker compose exec -T postgres psql -U sqq_user sqq_database < "backups/$backup_file"
        print_success "Backup restaurado exitosamente"
    fi
}

# Mostrar uso de espacio
show_disk_usage() {
    print_status "Uso de espacio en disco por Docker..."
    echo ""
    docker system df
    echo ""
    print_status "Volúmenes de Docker..."
    docker volume ls
}

# Mostrar ayuda
show_help() {
    echo "Uso: $0 [OPCIÓN]"
    echo ""
    echo "Opciones:"
    echo "  stop              Parar todos los servicios"
    echo "  clean-containers  Limpiar contenedores parados"
    echo "  clean-images      Limpiar imágenes no utilizadas"
    echo "  clean-volumes     Limpiar volúmenes no utilizados (CUIDADO)"
    echo "  clean-networks    Limpiar redes no utilizadas"
    echo "  clean-all         Limpieza completa del sistema Docker (PELIGROSO)"
    echo "  remove-data       Eliminar todos los datos de la aplicación (MUY PELIGROSO)"
    echo "  backup-db         Crear backup de la base de datos"
    echo "  restore-db        Restaurar backup de la base de datos"
    echo "  disk-usage        Mostrar uso de espacio en disco"
    echo "  help              Mostrar esta ayuda"
    echo ""
    echo "Sin argumentos: Ejecuta limpieza básica (contenedores + imágenes)"
}

# Función principal
main() {
    print_header
    
    case "${1:-basic}" in
        "stop")
            stop_services
            ;;
        "clean-containers")
            clean_containers
            ;;
        "clean-images")
            clean_images
            ;;
        "clean-volumes")
            clean_volumes
            ;;
        "clean-networks")
            clean_networks
            ;;
        "clean-all")
            full_docker_cleanup
            ;;
        "remove-data")
            remove_app_data
            ;;
        "backup-db")
            backup_database
            ;;
        "restore-db")
            restore_database
            ;;
        "disk-usage")
            show_disk_usage
            ;;
        "help")
            show_help
            ;;
        "basic")
            print_status "Ejecutando limpieza básica..."
            clean_containers
            clean_images
            clean_networks
            print_success "Limpieza básica completada"
            ;;
        *)
            print_error "Opción no válida: $1"
            show_help
            exit 1
            ;;
    esac
}

# Ejecutar función principal
main "$@"
