#!/usr/bin/env python3
"""
Test script for the CTM synchronization endpoint
"""

import requests
import json
import sys

# Configuration
NESTJS_API_URL = "http://localhost:3000"
SEQRNG_API_URL = "http://localhost:3001"

# Test CTM configuration (replace with actual values)
TEST_CTM_CONFIG = {
    "ip_address": "your-ctm-ip",
    "username": "your-ctm-username", 
    "password": "your-ctm-password",
    "domain": "your-ctm-domain"
}

# Test user credentials (replace with actual values)
TEST_USER = {
    "email": "<EMAIL>",
    "password": "testpassword"
}

def test_seqrng_list_keys():
    """Test the SeQRNG API list keys endpoint directly"""
    print("🧪 Testing SeQRNG API list keys endpoint...")
    
    url = f"{SEQRNG_API_URL}/api/v1/ctm/keys/list"
    payload = {
        "ctm_config": TEST_CTM_CONFIG,
        "limit": 10,
        "offset": 0
    }
    
    try:
        response = requests.post(url, json=payload, timeout=30)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ SeQRNG API list keys endpoint working!")
            print(f"Response: {json.dumps(data, indent=2)}")
            return True
        else:
            print(f"❌ SeQRNG API error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing SeQRNG API: {e}")
        return False

def login_user():
    """Login and get JWT token"""
    print("🔐 Logging in user...")
    
    url = f"{NESTJS_API_URL}/auth/login"
    payload = TEST_USER
    
    try:
        response = requests.post(url, json=payload, timeout=30)
        
        if response.status_code == 200 or response.status_code == 201:
            data = response.json()
            token = data.get('access_token')
            if token:
                print("✅ Login successful!")
                return token
            else:
                print("❌ No access token in response")
                return None
        else:
            print(f"❌ Login failed: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error during login: {e}")
        return None

def test_nestjs_synchronize(token):
    """Test the NestJS synchronize endpoint"""
    print("🔄 Testing NestJS synchronize endpoint...")
    
    url = f"{NESTJS_API_URL}/keys/synchronize"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(url, headers=headers, timeout=60)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200 or response.status_code == 201:
            data = response.json()
            print("✅ NestJS synchronize endpoint working!")
            print(f"Response: {json.dumps(data, indent=2)}")
            return True
        else:
            print(f"❌ NestJS API error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing NestJS API: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Starting CTM Synchronization Endpoint Tests")
    print("=" * 50)
    
    # Test 1: SeQRNG API list keys endpoint
    if not test_seqrng_list_keys():
        print("❌ SeQRNG API test failed. Check your CTM configuration and SeQRNG API.")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    
    # Test 2: Login to NestJS API
    token = login_user()
    if not token:
        print("❌ Login failed. Check your user credentials and NestJS API.")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    
    # Test 3: Test NestJS synchronize endpoint
    if not test_nestjs_synchronize(token):
        print("❌ NestJS synchronize test failed.")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("✅ All tests passed! The synchronization endpoint is working correctly.")

if __name__ == "__main__":
    print("⚠️  IMPORTANT: Update the TEST_CTM_CONFIG and TEST_USER variables with your actual values before running this test.")
    print("Press Enter to continue or Ctrl+C to exit...")
    input()
    main()
