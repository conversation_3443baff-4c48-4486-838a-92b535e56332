#!/usr/bin/env python3
"""
SeQRNG-CTM REST API Entry Point
Simple entry point that uses the application factory pattern
"""

import os
from app import create_app

# Create the Flask application using the factory pattern
app = create_app()

if __name__ == '__main__':
    # Run the Flask app
    app.run(
        host='0.0.0.0',
        port=int(os.environ.get('PORT', 3001)),
        debug=os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'
    )