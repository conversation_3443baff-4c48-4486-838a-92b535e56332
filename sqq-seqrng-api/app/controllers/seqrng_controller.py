"""
SeQRNG Controller Module

This module provides REST API endpoints for quantum random number generation
using the SeQRNG (Sequre Quantum Random Number Generator) service.

Supported operations:
- Generate quantum random bytes with configurable length and packages
- Generate hexadecimal keys from quantum random data
- Generate alphanumeric keys from quantum random data
- Generate RSA key pairs using quantum entropy
- Support for both static and dynamic configuration

All endpoints return quantum entropy reports including fidelity information
and support various output formats for different cryptographic use cases.
"""

from flask import Blueprint, request, current_app
from app.utils.response_helpers import create_success_response, create_error_response
from app.utils.validators import validate_num_bytes, validate_packages
from app.services.seqrng_service import SeQRNGService
from app.services.ctm_service import CTMService
from app.config.settings import parse_connection_string

seqrng_bp = Blueprint('seqrng', __name__)


def get_seqrng_service(seqrng_config=None):
    """Get SeQRNG service instance with optional dynamic configuration"""
    if seqrng_config:
        # Normalize dynamic configuration to match expected format
        normalized_config = normalize_seqrng_config(seqrng_config)
        return SeQRNGService(normalized_config)
    else:
        # Use global configuration
        global_seqrng_config = current_app.config.get('SEQRNG_CONFIG')
        if not global_seqrng_config:
            raise Exception("SeQRNG configuration not loaded")
        return SeQRNGService(global_seqrng_config)


def validate_seqrng_config(seqrng_config):
    """Validate SeqRNG configuration"""
    if not seqrng_config:
        return None

    required_seqrng_fields = ['ip_address', 'api_token']
    for field in required_seqrng_fields:
        if not seqrng_config.get(field):
            return f"SeqRNG configuration missing required field: {field}"

    return None


def normalize_seqrng_config(seqrng_config):
    """
    Normalize SeqRNG configuration to include base_url

    Args:
        seqrng_config: SeqRNG configuration dictionary with ip_address

    Returns:
        Normalized SeqRNG configuration with base_url
    """
    normalized_config = seqrng_config.copy()

    # Convert ip_address to base_url if not already present
    if 'base_url' not in normalized_config and 'ip_address' in normalized_config:
        try:
            normalized_config['base_url'] = parse_connection_string(
                normalized_config['ip_address'],
                default_protocol="https"
            )
        except ValueError as e:
            raise ValueError(f"Invalid SeqRNG IP address format '{normalized_config['ip_address']}': {e}")

    return normalized_config


@seqrng_bp.route('/api/v1/keys/generate/bytes', methods=['POST'])
def generate_random_bytes():
    """Generate random bytes from SeQRNG"""
    try:
        # Get request parameters
        data = request.get_json() or {}
        num_bytes = data.get('num_bytes', 32)
        packages = data.get('packages', 1)

        # Validate parameters
        num_bytes_error = validate_num_bytes(num_bytes)
        if num_bytes_error:
            return create_error_response(num_bytes_error, 400, "validation_error")

        packages_error = validate_packages(packages)
        if packages_error:
            return create_error_response(packages_error, 400, "validation_error")

        # Generate random bytes
        seqrng_service = get_seqrng_service()
        result = seqrng_service.generate_random_bytes(num_bytes, packages)

        # Remove raw bytes from response (keep only base64)
        response_data = {
            "random_bytes_base64": result["random_bytes_base64"],
            "num_bytes": result["num_bytes"],
            "packages": result["packages"],
            "entropy_report": result["entropy_report"]
        }

        return create_success_response(response_data, "Random bytes generated successfully")

    except Exception as e:
        return create_error_response(f"Failed to generate random bytes: {str(e)}", 500, "generation_error")


@seqrng_bp.route('/api/v1/keys/generate/hex', methods=['POST'])
def generate_hex_key():
    """Generate random hexadecimal key from SeQRNG"""
    try:
        # Get request parameters
        data = request.get_json() or {}
        num_bytes = data.get('num_bytes', 32)

        # Validate parameters
        num_bytes_error = validate_num_bytes(num_bytes)
        if num_bytes_error:
            return create_error_response(num_bytes_error, 400, "validation_error")

        # Generate hex key
        seqrng_service = get_seqrng_service()
        result = seqrng_service.generate_hex_key(num_bytes)

        return create_success_response(result, "Hexadecimal key generated successfully")

    except Exception as e:
        return create_error_response(f"Failed to generate hex key: {str(e)}", 500, "generation_error")


@seqrng_bp.route('/api/v1/keys/generate/alphanumeric', methods=['POST'])
def generate_alphanumeric_key():
    """Generate random alphanumeric key from SeQRNG"""
    try:
        # Get request parameters
        data = request.get_json() or {}
        num_bytes = data.get('num_bytes', 32)

        # Validate parameters
        num_bytes_error = validate_num_bytes(num_bytes)
        if num_bytes_error:
            return create_error_response(num_bytes_error, 400, "validation_error")

        # Generate alphanumeric key
        seqrng_service = get_seqrng_service()
        result = seqrng_service.generate_alphanumeric_key(num_bytes)

        return create_success_response(result, "Alphanumeric key generated successfully")

    except Exception as e:
        return create_error_response(f"Failed to generate alphanumeric key: {str(e)}", 500, "generation_error")


@seqrng_bp.route('/api/v1/keys/generate/rsa', methods=['POST'])
def generate_rsa_key_pair():
    """Generate RSA key pair from SeQRNG"""
    try:
        # Get request parameters
        data = request.get_json() or {}
        key_size = data.get('key_size', 2048)

        # Validate key size
        valid_key_sizes = [1024, 2048, 3072, 4096]
        if key_size not in valid_key_sizes:
            return create_error_response(f"Invalid key size. Must be one of: {valid_key_sizes}", 400, "validation_error")

        # Generate RSA key pair
        seqrng_service = get_seqrng_service()
        result = seqrng_service.generate_rsa_key_pair(key_size)

        return create_success_response(result, "RSA key pair generated successfully")

    except Exception as e:
        return create_error_response(f"Failed to generate RSA key pair: {str(e)}", 500, "generation_error")


# Dynamic CTM and SeqRNG Configuration Endpoints

@seqrng_bp.route('/api/v1/keys/generate/bytes/dynamic', methods=['POST'])
def generate_random_bytes_dynamic():
    """Generate random bytes from SeQRNG with dynamic CTM and SeqRNG configuration"""
    try:
        # Get request parameters
        data = request.get_json() or {}
        num_bytes = data.get('num_bytes', 32)
        packages = data.get('packages', 1)
        ctm_config = data.get('ctm_config')
        seqrng_config = data.get('seqrng_config')

        # Validate parameters
        num_bytes_error = validate_num_bytes(num_bytes)
        if num_bytes_error:
            return create_error_response(num_bytes_error, 400, "validation_error")

        packages_error = validate_packages(packages)
        if packages_error:
            return create_error_response(packages_error, 400, "validation_error")

        if not ctm_config:
            return create_error_response("CTM configuration is required for dynamic endpoints", 400, "validation_error")

        # Validate CTM configuration
        required_ctm_fields = ['ip_address', 'username', 'password', 'domain']
        for field in required_ctm_fields:
            if not ctm_config.get(field):
                return create_error_response(f"CTM configuration missing required field: {field}", 400, "validation_error")

        # Validate SeqRNG configuration if provided
        seqrng_error = validate_seqrng_config(seqrng_config)
        if seqrng_error:
            return create_error_response(seqrng_error, 400, "validation_error")

        # Generate random bytes with dynamic SeqRNG config if provided
        seqrng_service = get_seqrng_service(seqrng_config)
        result = seqrng_service.generate_random_bytes(num_bytes, packages)

        # Remove raw bytes from response (keep only base64)
        response_data = {
            "random_bytes_base64": result["random_bytes_base64"],
            "num_bytes": result["num_bytes"],
            "packages": result["packages"],
            "entropy_report": result["entropy_report"]
        }

        return create_success_response(response_data, "Random bytes generated successfully")

    except Exception as e:
        return create_error_response(f"Failed to generate random bytes: {str(e)}", 500, "generation_error")


@seqrng_bp.route('/api/v1/keys/generate/hex/dynamic', methods=['POST'])
def generate_hex_key_dynamic():
    """Generate random hexadecimal key from SeQRNG with dynamic CTM and SeqRNG configuration"""
    try:
        # Get request parameters
        data = request.get_json() or {}
        num_bytes = data.get('num_bytes', 32)
        ctm_config = data.get('ctm_config')
        seqrng_config = data.get('seqrng_config')

        # Validate parameters
        num_bytes_error = validate_num_bytes(num_bytes)
        if num_bytes_error:
            return create_error_response(num_bytes_error, 400, "validation_error")

        if not ctm_config:
            return create_error_response("CTM configuration is required for dynamic endpoints", 400, "validation_error")

        # Validate CTM configuration
        required_ctm_fields = ['ip_address', 'username', 'password', 'domain']
        for field in required_ctm_fields:
            if not ctm_config.get(field):
                return create_error_response(f"CTM configuration missing required field: {field}", 400, "validation_error")

        # Validate SeqRNG configuration if provided
        seqrng_error = validate_seqrng_config(seqrng_config)
        if seqrng_error:
            return create_error_response(seqrng_error, 400, "validation_error")

        # Generate hex key with dynamic SeqRNG config if provided
        seqrng_service = get_seqrng_service(seqrng_config)
        result = seqrng_service.generate_hex_key(num_bytes)

        return create_success_response(result, "Hexadecimal key generated successfully")

    except Exception as e:
        return create_error_response(f"Failed to generate hex key: {str(e)}", 500, "generation_error")


@seqrng_bp.route('/api/v1/keys/generate/alphanumeric/dynamic', methods=['POST'])
def generate_alphanumeric_key_dynamic():
    """Generate random alphanumeric key from SeQRNG with dynamic CTM and SeqRNG configuration"""
    try:
        # Get request parameters
        data = request.get_json() or {}
        num_bytes = data.get('num_bytes', 32)
        ctm_config = data.get('ctm_config')
        seqrng_config = data.get('seqrng_config')

        # Validate parameters
        num_bytes_error = validate_num_bytes(num_bytes)
        if num_bytes_error:
            return create_error_response(num_bytes_error, 400, "validation_error")

        if not ctm_config:
            return create_error_response("CTM configuration is required for dynamic endpoints", 400, "validation_error")

        # Validate CTM configuration
        required_ctm_fields = ['ip_address', 'username', 'password', 'domain']
        for field in required_ctm_fields:
            if not ctm_config.get(field):
                return create_error_response(f"CTM configuration missing required field: {field}", 400, "validation_error")

        # Validate SeqRNG configuration if provided
        seqrng_error = validate_seqrng_config(seqrng_config)
        if seqrng_error:
            return create_error_response(seqrng_error, 400, "validation_error")

        # Generate alphanumeric key with dynamic SeqRNG config if provided
        seqrng_service = get_seqrng_service(seqrng_config)
        result = seqrng_service.generate_alphanumeric_key(num_bytes)

        return create_success_response(result, "Alphanumeric key generated successfully")

    except Exception as e:
        return create_error_response(f"Failed to generate alphanumeric key: {str(e)}", 500, "generation_error")


@seqrng_bp.route('/api/v1/keys/generate/rsa/dynamic', methods=['POST'])
def generate_rsa_key_pair_dynamic():
    """Generate RSA key pair from SeQRNG with dynamic CTM and SeqRNG configuration"""
    try:
        # Get request parameters
        data = request.get_json() or {}
        key_size = data.get('key_size', 2048)
        ctm_config = data.get('ctm_config')
        seqrng_config = data.get('seqrng_config')

        # Validate key size
        valid_key_sizes = [1024, 2048, 3072, 4096]
        if key_size not in valid_key_sizes:
            return create_error_response(f"Invalid key size. Must be one of: {valid_key_sizes}", 400, "validation_error")

        if not ctm_config:
            return create_error_response("CTM configuration is required for dynamic endpoints", 400, "validation_error")

        # Validate CTM configuration
        required_ctm_fields = ['ip_address', 'username', 'password', 'domain']
        for field in required_ctm_fields:
            if not ctm_config.get(field):
                return create_error_response(f"CTM configuration missing required field: {field}", 400, "validation_error")

        # Validate SeqRNG configuration if provided
        seqrng_error = validate_seqrng_config(seqrng_config)
        if seqrng_error:
            return create_error_response(seqrng_error, 400, "validation_error")

        # Generate RSA key pair with dynamic SeqRNG config if provided
        seqrng_service = get_seqrng_service(seqrng_config)
        result = seqrng_service.generate_rsa_key_pair(key_size)

        return create_success_response(result, "RSA key pair generated successfully")

    except Exception as e:
        return create_error_response(f"Failed to generate RSA key pair: {str(e)}", 500, "generation_error")
