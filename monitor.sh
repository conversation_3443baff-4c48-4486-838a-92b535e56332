#!/bin/bash

# SequreQuantum Monitoring Script
# Este script verifica el estado de todos los servicios

set -e

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}=================================="
    echo -e "  SequreQuantum Service Monitor"
    echo -e "==================================${NC}"
    echo ""
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[⚠]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

# Verificar si Docker Compose está corriendo
check_docker_compose() {
    if ! docker compose ps &> /dev/null; then
        print_error "Docker Compose no está disponible o no hay servicios corriendo"
        exit 1
    fi
}

# Verificar estado de servicios
check_services() {
    print_status "Verificando estado de servicios..."
    echo ""
    
    services=("postgres" "seqrng-api" "nestjs-api" "frontend")
    
    for service in "${services[@]}"; do
        status=$(docker compose ps --services --filter "status=running" | grep "^${service}$" || echo "")
        
        if [ -n "$status" ]; then
            # Verificar health check si está disponible
            health=$(docker compose ps --format "table {{.Service}}\t{{.Status}}" | grep "$service" | awk '{print $2}')
            
            if [[ "$health" == *"healthy"* ]]; then
                print_success "$service: Running (Healthy)"
            elif [[ "$health" == *"unhealthy"* ]]; then
                print_warning "$service: Running (Unhealthy)"
            else
                print_success "$service: Running"
            fi
        else
            print_error "$service: Not running"
        fi
    done
}

# Verificar conectividad de puertos
check_ports() {
    echo ""
    print_status "Verificando conectividad de puertos..."
    echo ""
    
    ports=("80:Frontend" "3000:NestJS API" "3001:Flask API" "5432:PostgreSQL")
    
    for port_info in "${ports[@]}"; do
        port=$(echo $port_info | cut -d: -f1)
        service=$(echo $port_info | cut -d: -f2)
        
        if nc -z localhost $port 2>/dev/null; then
            print_success "Puerto $port ($service): Accesible"
        else
            print_error "Puerto $port ($service): No accesible"
        fi
    done
}

# Verificar endpoints de salud
check_health_endpoints() {
    echo ""
    print_status "Verificando endpoints de salud..."
    echo ""
    
    # NestJS API
    if curl -s -f http://localhost:3000/api &> /dev/null; then
        print_success "NestJS API (/api): Respondiendo"
    else
        print_error "NestJS API (/api): No responde"
    fi
    
    # Flask API
    if curl -s -f http://localhost:3001/api/v1/health &> /dev/null; then
        print_success "Flask API (/api/v1/health): Respondiendo"
    else
        print_error "Flask API (/api/v1/health): No responde"
    fi
    
    # Frontend
    if curl -s -f http://localhost:80/health &> /dev/null; then
        print_success "Frontend (/health): Respondiendo"
    else
        print_error "Frontend (/health): No responde"
    fi
}

# Mostrar uso de recursos
show_resource_usage() {
    echo ""
    print_status "Uso de recursos por contenedor..."
    echo ""
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}\t{{.NetIO}}\t{{.BlockIO}}"
}

# Mostrar logs recientes
show_recent_logs() {
    echo ""
    print_status "Logs recientes (últimas 10 líneas por servicio)..."
    echo ""
    
    services=("postgres" "seqrng-api" "nestjs-api" "frontend")
    
    for service in "${services[@]}"; do
        echo -e "${YELLOW}--- $service ---${NC}"
        docker compose logs --tail=5 $service 2>/dev/null || echo "No logs available"
        echo ""
    done
}

# Mostrar información de acceso
show_access_info() {
    echo ""
    print_status "Información de acceso..."
    echo ""
    
    local_ip=$(hostname -I | awk '{print $1}')
    
    echo "📱 URLs de acceso:"
    echo "  Frontend:     http://$local_ip"
    echo "  NestJS API:   http://$local_ip:3000"
    echo "  API Docs:     http://$local_ip:3000/api"
    echo "  Flask API:    http://$local_ip:3001"
    echo "  Flask Docs:   http://$local_ip:3001/docs"
}

# Función principal
main() {
    print_header
    
    check_docker_compose
    check_services
    check_ports
    check_health_endpoints
    
    if [ "$1" == "--detailed" ] || [ "$1" == "-d" ]; then
        show_resource_usage
        show_recent_logs
    fi
    
    show_access_info
    
    echo ""
    print_status "Monitoreo completado. Usa --detailed para más información."
}

# Verificar si netcat está instalado
if ! command -v nc &> /dev/null; then
    print_warning "netcat no está instalado. Instalando..."
    sudo apt update && sudo apt install -y netcat-openbsd
fi

# Ejecutar función principal
main "$@"
