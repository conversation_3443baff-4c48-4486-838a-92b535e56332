# SequreQuantum

Sistema completo de gestión de claves cuánticas que integra SeQRNG (Quantum Random Number Generator) con CipherTrust Manager para la generación y gestión segura de claves criptográficas.

## 🏗️ Arquitectura

El sistema está compuesto por 4 servicios principales:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React Frontend │    │   NestJS API    │    │   Flask API     │
│   (Puerto 80)    │◄──►│   (Puerto 3000) │◄──►│   (Puerto 3001) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   PostgreSQL    │    │   SeQRNG + CTM  │
                       │   (Puerto 5432) │    │   (Externos)    │
                       └─────────────────┘    └─────────────────┘
```

### Componentes

1. **Frontend (React + Vite)**: Interfaz de usuario moderna y responsiva
2. **NestJS API**: Backend principal con autenticación JWT y gestión de usuarios
3. **Flask API**: Servicio especializado para integración con SeQRNG y CipherTrust Manager
4. **PostgreSQL**: Base de datos para persistencia de usuarios y claves generadas

## 🚀 Despliegue Rápido

### Prerrequisitos

- VPS Ubuntu 20.04+ con Docker y Docker Compose
- Acceso a servidores SeQRNG y CipherTrust Manager
- Puertos 80, 3000, 3001 disponibles

### Instalación Automática

```bash
# 1. Clonar el repositorio
git clone <URL_REPOSITORIO> SequreQuantum
cd SequreQuantum

# 2. Ejecutar script de despliegue automático
chmod +x deploy.sh
./deploy.sh
```

El script automáticamente:
- ✅ Verifica instalación de Docker
- ✅ Configura permisos necesarios
- ✅ Valida estructura del proyecto
- ✅ Construye todas las imágenes
- ✅ Levanta todos los servicios
- ✅ Verifica que todo esté funcionando

### Configuración Manual

Si prefieres configurar manualmente, consulta [DEPLOYMENT.md](DEPLOYMENT.md) para instrucciones detalladas.

## 🔧 Configuración

### Variables de Entorno

Copia `.env.example` a `.env` y configura:

```bash
cp .env.example .env
nano .env
```

**Variables críticas a configurar:**

```env
# Base de datos
DB_PASSWORD=tu_password_seguro

# JWT (mínimo 32 caracteres)
JWT_SECRET=tu_jwt_secret_muy_seguro_de_al_menos_32_caracteres
JWT_REFRESH_SECRET=tu_refresh_secret_muy_seguro

# Frontend (IMPORTANTE: Cambiar localhost por la IP de tu servidor)
FRONTEND_API_URL=http://TU_IP_SERVIDOR:3000

# SeQRNG
SEQRNG_IP_ADDRESS=http://tu-servidor-seqrng:puerto
SEQRNG_API_TOKEN=tu_token_seqrng

# CipherTrust Manager
CTM_IP_ADDRESS=tu-servidor-ctm
CTM_USERNAME=tu_usuario_ctm
CTM_PASSWORD=tu_password_ctm
CTM_DOMAIN=tu_dominio_ctm
```

> **⚠️ IMPORTANTE**: Para acceso externo, cambia `FRONTEND_API_URL=http://localhost:3000` por `FRONTEND_API_URL=http://TU_IP_SERVIDOR:3000` donde `TU_IP_SERVIDOR` es la IP pública de tu VPS.

## 📊 Monitoreo

### Verificar Estado de Servicios

```bash
# Estado básico
./monitor.sh

# Información detallada con recursos y logs
./monitor.sh --detailed
```

### Acceso a la Aplicación

Una vez desplegado, accede a:

- **Frontend**: http://tu-servidor (puerto 80)
- **API Principal**: http://tu-servidor:3000
- **Documentación API**: http://tu-servidor:3000/api
- **Flask API**: http://tu-servidor:3001
- **Docs Flask**: http://tu-servidor:3001/docs

## 🧹 Mantenimiento

### Scripts de Limpieza

```bash
# Limpieza básica
./cleanup.sh

# Backup de base de datos
./cleanup.sh backup-db

# Restaurar backup
./cleanup.sh restore-db

# Ver todas las opciones
./cleanup.sh help
```

### Comandos Docker Útiles

```bash
# Ver logs en tiempo real
docker compose logs -f

# Reiniciar un servicio específico
docker compose restart nestjs-api

# Acceder a un contenedor
docker compose exec nestjs-api bash

# Ver estado de servicios
docker compose ps
```

## 🔒 Seguridad

### Configuración de Producción

1. **Cambiar todas las contraseñas por defecto**
2. **Usar secretos JWT fuertes (32+ caracteres)**
3. **Configurar firewall apropiadamente**
4. **Usar HTTPS con certificados SSL válidos**
5. **Mantener Docker y el sistema actualizados**

### Firewall (UFW)

```bash
sudo ufw allow 80
sudo ufw allow 3000
sudo ufw allow 3001
# sudo ufw allow 5432  # Solo si necesitas acceso externo a PostgreSQL
```

### SSL/HTTPS (Opcional)

Para configurar SSL con Nginx como proxy reverso, consulta la sección correspondiente en [DEPLOYMENT.md](DEPLOYMENT.md).

## 📁 Estructura del Proyecto

```
SequreQuantum/
├── sqq-api/                 # NestJS Backend
│   ├── src/
│   ├── Dockerfile
│   └── package.json
├── sqq-frontend/            # React Frontend
│   ├── src/
│   ├── Dockerfile
│   └── package.json
├── sqq-seqrng-api/         # Flask API
│   ├── app/
│   ├── Dockerfile
│   └── requirements.txt
├── init-db/                # Scripts de inicialización de BD
├── docker-compose.yml      # Configuración de servicios
├── .env.example           # Plantilla de variables de entorno
├── deploy.sh              # Script de despliegue automático
├── monitor.sh             # Script de monitoreo
├── cleanup.sh             # Script de limpieza
├── DEPLOYMENT.md          # Guía detallada de despliegue
└── README.md              # Este archivo
```

## 🐛 Troubleshooting

### Problemas Comunes

1. **Servicios no inician**: Verificar logs con `docker compose logs`
2. **Error de conexión a BD**: Verificar variables de entorno y que PostgreSQL esté healthy
3. **Frontend no carga**: Verificar que NestJS API esté corriendo
4. **Errores de permisos Docker**: Agregar usuario al grupo docker

### Verificar Conectividad

```bash
# Entre servicios
docker compose exec nestjs-api ping postgres
docker compose exec nestjs-api ping seqrng-api

# Puertos externos
nc -z localhost 80
nc -z localhost 3000
nc -z localhost 3001
```

## 📞 Soporte

Para problemas o preguntas:

1. Revisar logs: `docker compose logs`
2. Verificar estado: `./monitor.sh --detailed`
3. Consultar [DEPLOYMENT.md](DEPLOYMENT.md) para configuración avanzada

## 📄 Licencia

[Especificar licencia del proyecto]

---

**Desarrollado por el equipo SequreQuantum**
