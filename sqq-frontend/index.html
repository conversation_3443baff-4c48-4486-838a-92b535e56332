<!DOCTYPE html>
<html lang="es">
  <head>
    <meta charset="UTF-8" />

    <!-- 🎯 FAVICON - Icono de la pestaña -->
    <link rel="icon" type="image/svg+xml" href="/src/assets/q-qrng-3.svg" />
    <link rel="alternate icon" href="/src/assets/q-qrng-3.svg" />
    <link rel="mask-icon" href="/src/assets/q-qrng-3.svg" color="#000000" />

    <title>Secure Quantum</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- 🛡️ SECURITY HEADERS -->
    <!-- Content Security Policy - Previene XSS -->
    <meta http-equiv="Content-Security-Policy"
          content="default-src 'self';
                  script-src 'self' 'unsafe-inline' 'unsafe-eval';
                  style-src 'self' 'unsafe-inline';
                  img-src 'self' data: blob:;
                  font-src 'self' data:;
                  connect-src 'self' http://localhost:3000 https://api.securequantum.com;
                  frame-ancestors 'none';
                  base-uri 'self';
                  form-action 'self';">

    <!-- Previene MIME type sniffing -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">

    <!-- Controla referrer information -->
    <meta name="referrer" content="strict-origin-when-cross-origin">

    <!-- Permissions Policy - Deshabilita APIs peligrosas -->
    <meta http-equiv="Permissions-Policy"
          content="camera=(), microphone=(), geolocation=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=()">

    <!-- Previene DNS prefetching no deseado -->
    <meta http-equiv="x-dns-prefetch-control" content="off">

    <!-- Fuerza HTTPS en producción -->
    <meta http-equiv="Strict-Transport-Security" content="max-age=31536000; includeSubDomains">
  </head>
<body>
  <div id="root"></div>
  <script type="module" src="/src/main.jsx"></script>
</body>


</html>
