/**
 * Script de prueba para verificar las correcciones de CTM
 * Este script simula las llamadas que se harían desde el frontend
 */

// Simular el servicio de llaves
const mockKeyService = {
  async getKeysByCtm(filters) {
    console.log('🔍 getKeysByCtm llamado con filtros:', filters);
    
    // Verificar que ctmId esté presente
    if (!filters.ctmId) {
      throw new Error('ctmId es requerido para obtener llaves por CTM');
    }
    
    // Simular validación UUID
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(filters.ctmId)) {
      throw new Error('ctmId must be a UUID');
    }
    
    console.log('✅ Validación de ctmId exitosa');
    return {
      keys: [],
      total: 0,
      successful: 0,
      failed: 0,
      uploadedToCtm: 0
    };
  },

  async uploadToCTM(keyData) {
    console.log('🔑 uploadToCTM llamado con datos:', keyData);
    
    // Verificar que ctmId esté presente para CTM
    if (!keyData.ctmId && !keyData.hsmId) {
      throw new Error('ctmId o hsmId es requerido');
    }
    
    if (keyData.ctmId) {
      console.log('✅ ctmId presente en payload CTM:', keyData.ctmId);
    }
    
    if (keyData.hsmId) {
      console.log('✅ hsmId presente en payload HSM:', keyData.hsmId);
    }
    
    return {
      id: 'test-key-id',
      name: keyData.key_name,
      algorithm: keyData.algorithm,
      status: 'success'
    };
  }
};

// Simular el contexto de servicios
const mockSelectedService = {
  id: '12345678-1234-1234-1234-123456789abc',
  name: 'CTM Test',
  type: 'CTM'
};

// Simular el modal de subida de llaves
function simulateKeyUpload(selectedService, formData) {
  console.log('\n🚀 Simulando subida de llave...');
  console.log('Servicio seleccionado:', selectedService);
  console.log('Datos del formulario:', formData);
  
  const isHSMService = selectedService?.type === 'HSM';
  
  let keyData;
  
  if (isHSMService) {
    // Para HSM, transformar datos al formato esperado por el backend
    const algorithm = formData.algorithm.toLowerCase();

    // Mapear algoritmos del frontend a los valores esperados por el backend
    let keyType;
    switch (algorithm) {
      case 'aes':
      case 'aria':
        keyType = 'aes';
        break;
      case 'rsa':
        keyType = 'rsa';
        break;
      case 'ec':
        keyType = 'ec';
        break;
      case 'kyber':
        keyType = 'kyberkn';
        break;
      case 'dilithium':
        keyType = 'dilithiumkn';
        break;
      default:
        keyType = algorithm; // fallback
    }

    keyData = {
      keyName: formData.key_name,
      keyType: keyType,
      hsmId: selectedService?.id
    };

    // Agregar campos específicos según el tipo de algoritmo
    if (keyType === 'aes' || keyType === 'rsa') {
      // Para AES y RSA, keySize en bits
      keyData.keySize = formData.num_bytes * 8; // Convertir bytes a bits
    } else if (keyType === 'ec') {
      // Para EC, agregar eccCurveType
      keyData.eccCurveType = 'sect233k1'; // Valor por defecto
    }
    // Para kyberkn y dilithiumkn no se requieren campos adicionales

  } else {
    // Para CTM, incluir ctmId
    keyData = {
      ...formData,
      ctmId: selectedService?.id
    };
  }
  
  console.log('Datos finales para envío:', keyData);
  return mockKeyService.uploadToCTM(keyData);
}

// Simular obtener llaves por CTM
function simulateGetKeysByCtm(selectedService, filters = {}) {
  console.log('\n🔍 Simulando obtener llaves por CTM...');
  console.log('Servicio seleccionado:', selectedService);
  
  if (!selectedService) {
    throw new Error('Service is required');
  }
  
  const serviceId = selectedService.id;
  const requestFilters = {
    page: filters.page || 1,
    limit: filters.limit || 10,
    ctmId: serviceId,
    ...filters
  };
  
  console.log('Filtros finales:', requestFilters);
  return mockKeyService.getKeysByCtm(requestFilters);
}

// Ejecutar pruebas
async function runTests() {
  console.log('🧪 Iniciando pruebas de correcciones CTM...\n');
  
  try {
    // Prueba 1: Subir llave CTM con ctmId
    console.log('=== PRUEBA 1: Subir llave CTM ===');
    const ctmKeyData = {
      key_name: 'test-ctm-key',
      algorithm: 'AES',
      num_bytes: 32,
      exportable: false
    };
    
    await simulateKeyUpload(mockSelectedService, ctmKeyData);
    console.log('✅ Prueba 1 exitosa\n');
    
    // Prueba 2: Subir llave HSM con hsmId
    console.log('=== PRUEBA 2: Subir llave HSM ===');
    const hsmService = {
      id: '87654321-4321-4321-4321-210987654321',
      name: 'HSM Test',
      type: 'HSM'
    };
    
    const hsmKeyData = {
      key_name: 'test-hsm-key',
      algorithm: 'RSA',
      num_bytes: 256,
      message: 'Test message'
    };
    
    await simulateKeyUpload(hsmService, hsmKeyData);
    console.log('✅ Prueba 2 exitosa\n');
    
    // Prueba 3: Obtener llaves por CTM con ctmId válido
    console.log('=== PRUEBA 3: Obtener llaves por CTM ===');
    await simulateGetKeysByCtm(mockSelectedService);
    console.log('✅ Prueba 3 exitosa\n');
    
    // Prueba 4: Error cuando ctmId está vacío
    console.log('=== PRUEBA 4: Error con ctmId vacío ===');
    try {
      await simulateGetKeysByCtm({ id: '', type: 'CTM' });
      console.log('❌ Debería haber fallado');
    } catch (error) {
      console.log('✅ Error esperado:', error.message);
    }
    
    // Prueba 5: Error cuando ctmId no es UUID válido
    console.log('\n=== PRUEBA 5: Error con ctmId inválido ===');
    try {
      await simulateGetKeysByCtm({ id: 'invalid-uuid', type: 'CTM' });
      console.log('❌ Debería haber fallado');
    } catch (error) {
      console.log('✅ Error esperado:', error.message);
    }
    
    console.log('\n🎉 Todas las pruebas completadas exitosamente!');
    
  } catch (error) {
    console.error('❌ Error en las pruebas:', error.message);
  }
}

// Ejecutar las pruebas si el script se ejecuta directamente
if (typeof module !== 'undefined' && require.main === module) {
  runTests();
}

// Exportar para uso en otros archivos
if (typeof module !== 'undefined') {
  module.exports = {
    runTests,
    simulateKeyUpload,
    simulateGetKeysByCtm
  };
}
