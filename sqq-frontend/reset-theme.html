<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Theme - SQQ</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        button:hover {
            background: #2563eb;
        }
        .success {
            color: #059669;
            margin-top: 20px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌞 Reset Theme to Light Mode</h1>
        <p>Este script limpiará la configuración del tema y establecerá el modo claro como predeterminado.</p>
        
        <button onclick="resetTheme()">🔄 Resetear a Modo Claro</button>
        <button onclick="checkCurrentTheme()">🔍 Ver Configuración Actual</button>
        
        <div id="result"></div>
    </div>

    <script>
        function resetTheme() {
            // Limpiar localStorage
            localStorage.removeItem('darkMode');
            
            // Establecer modo claro explícitamente
            localStorage.setItem('darkMode', 'false');
            
            // Remover clase dark del HTML
            document.documentElement.classList.remove('dark');
            
            // Mostrar resultado
            document.getElementById('result').innerHTML = `
                <div class="success">
                    ✅ ¡Tema reseteado exitosamente!<br>
                    🌞 Modo claro establecido como predeterminado<br>
                    🔄 Ahora puedes cerrar esta ventana y ejecutar tu aplicación
                </div>
            `;
        }
        
        function checkCurrentTheme() {
            const currentTheme = localStorage.getItem('darkMode');
            const hasClass = document.documentElement.classList.contains('dark');
            
            document.getElementById('result').innerHTML = `
                <div style="text-align: left; margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 6px;">
                    <strong>📊 Configuración Actual:</strong><br>
                    • localStorage 'darkMode': ${currentTheme || 'null'}<br>
                    • Clase 'dark' en HTML: ${hasClass ? 'Sí' : 'No'}<br>
                    • Estado esperado: ${currentTheme === 'false' ? '🌞 Modo Claro' : currentTheme === 'true' ? '🌙 Modo Oscuro' : '❓ No definido'}
                </div>
            `;
        }
        
        // Verificar al cargar la página
        window.onload = function() {
            checkCurrentTheme();
        };
    </script>
</body>
</html>
