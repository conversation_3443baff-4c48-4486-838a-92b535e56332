# ================================
# Multi-stage Dockerfile para React + Vite
# Optimizado para producción en VPS Ubuntu
# ================================

# ================================
# Stage 1: Build Stage
# ================================
FROM node:20-alpine AS builder

# Instalar dependencias del sistema necesarias
RUN apk add --no-cache \
    git \
    python3 \
    make \
    g++

# Crear directorio de trabajo
WORKDIR /app

# Copiar archivos de configuración de dependencias
COPY package*.json ./

# Instalar todas las dependencias (incluyendo devDependencies para el build)
RUN npm ci --silent && \
    npm cache clean --force

# Copiar archivos de configuración
COPY vite.config.js ./
COPY postcss.config.js ./
COPY tailwind.config.js ./
COPY eslint.config.js ./

# Copiar código fuente
COPY src/ ./src/
COPY public/ ./public/
COPY index.html ./

# Argumentos de build para variables de entorno
ARG VITE_API_BASE_URL=http://localhost:3000
ARG VITE_API_TIMEOUT=30000
ARG VITE_NODE_ENV=production

# Crear archivo .env para el build
RUN echo "VITE_API_BASE_URL=${VITE_API_BASE_URL}" > .env && \
    echo "VITE_API_TIMEOUT=${VITE_API_TIMEOUT}" >> .env && \
    echo "VITE_NODE_ENV=${VITE_NODE_ENV}" >> .env

# Construir la aplicación
RUN npm run build:prod

# Verificar que el build se completó correctamente
RUN ls -la dist/ && \
    test -f dist/index.html || (echo "Build failed: index.html not found" && exit 1)

# ================================
# Stage 2: Production Stage
# ================================
FROM nginx:1.25-alpine AS production

# Instalar dependencias adicionales para seguridad
RUN apk add --no-cache \
    curl \
    tzdata && \
    rm -rf /var/cache/apk/*

# Configurar timezone
ENV TZ=America/Santiago
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Crear usuario no-root para nginx
RUN addgroup -g 1001 -S nginx-app && \
    adduser -S -D -H -u 1001 -h /var/cache/nginx -s /sbin/nologin -G nginx-app -g nginx-app nginx-app

# Copiar archivos construidos desde el stage anterior
COPY --from=builder /app/dist /usr/share/nginx/html

# Crear configuración personalizada de nginx
RUN cat > /etc/nginx/nginx.conf << 'EOF'
user nginx-app;
worker_processes auto;
error_log /var/log/nginx/error.log notice;
pid /tmp/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging format
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    # Performance optimizations
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 16M;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # Security headers
    add_header X-Frame-Options "DENY" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Permissions-Policy "camera=(), microphone=(), geolocation=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=()" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Hide nginx version
    server_tokens off;

    server {
        listen 80;
        listen [::]:80;
        server_name _;
        root /usr/share/nginx/html;
        index index.html;

        # Security: Disable access to hidden files
        location ~ /\. {
            deny all;
            access_log off;
            log_not_found off;
        }

        # Handle React Router (SPA)
        location / {
            try_files $uri $uri/ /index.html;
            
            # Cache static assets
            location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
                expires 1y;
                add_header Cache-Control "public, immutable";
                access_log off;
            }
        }

        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        # Security: Block access to sensitive files
        location ~* \.(env|log|ini|conf|bak|old|tmp)$ {
            deny all;
            access_log off;
            log_not_found off;
        }
    }
}
EOF

# Ajustar permisos
RUN chown -R nginx-app:nginx-app /usr/share/nginx/html && \
    chown -R nginx-app:nginx-app /var/cache/nginx && \
    chown -R nginx-app:nginx-app /var/log/nginx && \
    chmod -R 755 /usr/share/nginx/html

# Crear script de inicio
RUN cat > /docker-entrypoint.sh << 'EOF'
#!/bin/sh
set -e

echo "🚀 Starting Secure Quantum Frontend..."
echo "📁 Files in /usr/share/nginx/html:"
ls -la /usr/share/nginx/html/

echo "🔧 Testing nginx configuration..."
nginx -t

echo "🌐 Starting nginx..."
exec nginx -g "daemon off;"
EOF

RUN chmod +x /docker-entrypoint.sh

# Cambiar a usuario no-root
USER nginx-app

# Exponer puerto
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/health || exit 1

# Comando de inicio
ENTRYPOINT ["/docker-entrypoint.sh"]

# Metadata
LABEL maintainer="Secure Quantum Team"
LABEL description="Frontend React application for Secure Quantum"
LABEL version="1.0.0"
