# 🔧 Solución: Visualización de Llaves HSM

## 🚨 Problemas Identificados y Solucionados

### **Problema 1: Las llaves HSM no se mostraban en el frontend**
**Causa**: El filtro en `KeyManagement.jsx` solo mostraba llaves con `ctmKeyId`, excluyendo las llaves HSM.

### **Problema 2: Bucle infinito de carga de versiones**
**Causa**: El componente intentaba cargar versiones para todas las llaves, incluyendo HSM que no tienen versiones.

### **Problema 3: Botones incorrectos para llaves HSM**
**Causa**: Se mostraban botones de "Actualizar" y "Ver versiones" para llaves HSM que no soportan estas funciones.

## ✅ Soluciones Implementadas

### **1. Actualización del Hook useKeys**
**Archivo**: `sqq-frontend/src/hooks/useKeys.js`

```javascript
// Procesar las llaves para asegurar compatibilidad con el frontend
const processedKeys = keysArray.map(key => ({
  ...key,
  // Asegurar compatibilidad con el formato esperado por el frontend
  algorithm: key.algorithm || 'AES', // Ya viene mapeado del backend
  type: key.type || 'hex_key',
  num_bytes: key.numBytes || 32, // Usar numBytes del backend
  // Mantener campos originales para compatibilidad
  keyType: key.algorithm,
  keySize: key.numBytes * 8, // Convertir bytes a bits para compatibilidad
  // Marcar como llave HSM para identificación
  isHsmKey: true
}));

// Usar los valores de paginación del response
const totalCount = response.total || 0;
const currentPageNum = response.page || currentPage;
const limitNum = response.limit || keysPerPage;
const totalPagesNum = response.totalPages || Math.ceil(totalCount / limitNum);

// Usar las estadísticas del response
setStatistics({
  total: totalCount,
  successful: response.successful || 0,
  failed: response.failed || 0,
  uploadedToCtm: response.uploadedToCtm || 0,
});
```

### **2. Actualización del Filtro de Llaves Madre**
**Archivo**: `sqq-frontend/src/components/dashboard/user/KeyManagement.jsx`

```javascript
// Filtrar solo las llaves madre (originales, no versiones actualizadas)
const motherKeys = keys.filter(key => {
  // Una llave es madre si:
  // 1. Tiene un ctmKeyId (está subida a CTM) O es una llave HSM
  // 2. No es una versión actualizada (no tiene isUpdatedVersion = true)
  // 3. Está activa o tiene estado válido
  const isCtmKey = key.ctmKeyId && 
                   (key.status === 'UPLOADED_TO_CTM' || key.status === 'ACTIVE' || key.uploadedToCtm === true);
  const isHsmKey = key.isHsmKey || 
                   (key.hsmId && (key.status === 'uploaded_to_ctm' || key.status === 'ACTIVE' || key.active));
  
  return (isCtmKey || isHsmKey) && !key.isUpdatedVersion;
});
```

### **3. Prevención de Carga de Versiones para HSM**
**Archivo**: `sqq-frontend/src/components/dashboard/user/KeyManagement.jsx`

```javascript
{currentKeys.map((key) => {
  // Cargar versiones de la llave si no se han cargado aún
  // Solo para llaves CTM (las llaves HSM no tienen versiones)
  if (getKeyVersions && !keyVersionsInfo[key.id] && !key.isHsmKey && key.ctmKeyId) {
    loadKeyVersions(key.id);
  }
  // ... resto del código
})}
```

### **4. Ocultación de Botones Específicos de CTM**
**Archivo**: `sqq-frontend/src/components/dashboard/user/KeyCard.jsx`

```javascript
{/* Solo mostrar botón de actualizar para llaves CTM */}
{!keyData.isHsmKey && keyData.ctmKeyId && (
  <button onClick={onUpdate} /* ... */>
    <RefreshCw size={18} />
  </button>
)}

{/* Solo mostrar botón de versiones para llaves CTM */}
{!keyData.isHsmKey && keyData.ctmKeyId && (
  <button onClick={onViewVersions} /* ... */>
    <History size={18} />
  </button>
)}
```

### **5. Actualización de Estadísticas**
**Archivo**: `sqq-frontend/src/pages/UsuarioDashboard.jsx`

```javascript
// Incluir llaves HSM en las estadísticas de llaves activas
value: statistics?.uploadedToCtm || userKeys.filter(key =>
  key.status === 'UPLOADED_TO_CTM' || 
  key.uploadedToCtm === true ||
  (key.isHsmKey && (key.status === 'uploaded_to_ctm' || key.status === 'ACTIVE' || key.active))
).length,

// Incluir llaves HSM en el filtro de llaves madre para versiones
motherKeys={userKeys.filter(key => 
  key.status === 'UPLOADED_TO_CTM' || 
  key.uploadedToCtm === true || 
  key.isHsmKey || 
  (key.hsmId && (key.status === 'uploaded_to_ctm' || key.status === 'ACTIVE' || key.active))
)}
```

## 📋 Formato de Respuesta HSM

### **Estructura de Llave HSM**:
```json
{
  "id": "37bb78e9-ff40-47bd-b478-f0a97d56709d",
  "name": "test-dilithium-01",
  "type": "hex_key",
  "version": 1,
  "active": true,
  "algorithm": "aes",
  "numBytes": 32,
  "status": "uploaded_to_ctm",
  "owner": "test_dasdasdsa",
  "exportable": false,
  "uploadedToCtm": false,
  "ctmKeyName": null,
  "ctmKeyId": null,
  "entropyReport": null,
  "errorMessage": null,
  "createdAt": "2025-08-11T09:15:19.991Z",
  "updatedAt": "2025-08-11T09:15:29.004Z",
  "isSuccessful": true,
  "displayName": "test-dilithium-01"
}
```

### **Campos Agregados en Frontend**:
```javascript
{
  // ... campos originales del backend
  isHsmKey: true,           // Identificador de llave HSM
  keyType: "aes",           // Alias del algorithm
  keySize: 256,             // numBytes * 8 (bits)
  num_bytes: 32             // numBytes del backend
}
```

## 🎯 Diferencias entre Llaves CTM y HSM

| Característica | CTM | HSM |
|----------------|-----|-----|
| **Identificador** | `ctmKeyId` | `isHsmKey: true` |
| **Versiones** | ✅ Soporta | ❌ No soporta |
| **Actualización** | ✅ Soporta | ❌ No soporta |
| **Botón "Ver versiones"** | ✅ Visible | ❌ Oculto |
| **Botón "Actualizar"** | ✅ Visible | ❌ Oculto |
| **Carga de versiones** | ✅ Se cargan | ❌ Se omite |
| **Estadísticas** | ✅ Incluidas | ✅ Incluidas |

## 🚀 Estado Final

✅ **Las llaves HSM ahora se muestran correctamente**:
- Se visualizan en la lista de llaves
- No generan bucles de carga de versiones
- Solo muestran botones relevantes (Ver detalle, Eliminar)
- Se incluyen en las estadísticas del dashboard
- Mantienen compatibilidad con el formato de llaves CTM

✅ **No más errores**:
- No más errores 400 por intentar cargar versiones de llaves HSM
- No más bucles infinitos de requests
- Interfaz limpia y funcional para ambos tipos de llaves

El sistema ahora maneja correctamente tanto llaves CTM como HSM con sus respectivas limitaciones y capacidades.
