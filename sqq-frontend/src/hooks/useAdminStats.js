/**
 * useAdminStats Hook
 * Hook personalizado para obtener estadísticas del dashboard de admin
 */

import { useState, useCallback } from 'react';
import { userService, keyService } from '../services/index.js';

export const useAdminStats = () => {
  const [stats, setStats] = useState({
    totalUsers: 0,
    activeUsers: 0,
    totalKeys: 0,
    activeKeys: 0,
    successfulKeys: 0,
    failedKeys: 0,
    uploadedToCtm: 0,
    pendingKeys: 0,
  });
  const [usersWithKeys, setUsersWithKeys] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  /**
   * Cargar estadísticas completas
   */
  const loadStats = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // 1. Obtener todos los usuarios
      const users = await userService.getAllUsers();

      // 2. Obtener llaves para cada usuario
      const usersWithKeysData = await Promise.all(
        users.map(async (user) => {
          try {
            // Obtener todas las llaves del usuario con límite alto para asegurar que se obtengan todas
            const keysResponse = await keyService.getKeysByUser(user.id, { limit: 100 });
            return {
              ...user,
              keys: keysResponse.keys || [],
              keyStats: {
                total: keysResponse.total || 0,
                successful: keysResponse.successful || 0,
                failed: keysResponse.failed || 0,
                uploadedToCtm: keysResponse.uploadedToCtm || 0,
              }
            };
          } catch (error) {
            // Si el usuario no tiene configuración CTM o no tiene llaves, devolver usuario sin llaves
            console.warn(`No se pudieron cargar llaves para usuario ${user.id}:`, error.message);
            return {
              ...user,
              keys: [],
              keyStats: {
                total: 0,
                successful: 0,
                failed: 0,
                uploadedToCtm: 0,
              }
            };
          }
        })
      );

      setUsersWithKeys(usersWithKeysData);

      // 3. Calcular estadísticas globales
      const totalUsers = users.length;
      const activeUsers = users.filter(user => user.isActive).length;
      
      const allKeyStats = usersWithKeysData.reduce((acc, user) => {
        acc.total += user.keyStats.total;
        acc.successful += user.keyStats.successful;
        acc.failed += user.keyStats.failed;
        acc.uploadedToCtm += user.keyStats.uploadedToCtm;
        return acc;
      }, { total: 0, successful: 0, failed: 0, uploadedToCtm: 0 });

      // Contar llaves activas usando las estadísticas del backend
      const activeKeys = usersWithKeysData.reduce((count, user) => {
        console.log(`[useAdminStats] Usuario ${user.id}:`, {
          keyStats: user.keyStats,
          keysCount: user.keys.length,
          keys: user.keys.map(k => ({ id: k.id, status: k.status, uploadedToCtm: k.uploadedToCtm }))
        });

        // Usar las estadísticas del backend si están disponibles
        if (user.keyStats && user.keyStats.uploadedToCtm) {
          return count + user.keyStats.uploadedToCtm;
        }
        // Fallback: contar manualmente las llaves activas
        const manualCount = user.keys.filter(key =>
          key.status === 'UPLOADED_TO_CTM' || key.uploadedToCtm === true
        ).length;
        console.log(`[useAdminStats] Usuario ${user.id} - llaves activas (manual):`, manualCount);
        return count + manualCount;
      }, 0);

      console.log('[useAdminStats] Total llaves activas calculadas:', activeKeys);

      // Calcular llaves pendientes (total - exitosas - fallidas)
      const pendingKeys = Math.max(0, allKeyStats.total - allKeyStats.successful - allKeyStats.failed);

      const newStats = {
        totalUsers,
        activeUsers,
        totalKeys: allKeyStats.total,
        activeKeys,
        successfulKeys: allKeyStats.successful,
        failedKeys: allKeyStats.failed,
        uploadedToCtm: allKeyStats.uploadedToCtm,
        pendingKeys,
      };

      setStats(newStats);
      return { stats: newStats, usersWithKeys: usersWithKeysData };

    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Obtener llaves de un usuario específico
   */
  const getUserKeys = useCallback(async (userId, filters = {}) => {
    try {
      // Obtener todas las llaves del usuario con límite alto por defecto
      const response = await keyService.getKeysByUser(userId, { 
        ...filters, 
        limit: filters.limit || 100 
      });
      return response;
    } catch (error) {
      console.warn(`Error obteniendo llaves del usuario ${userId}:`, error.message);
      return { keys: [], total: 0, successful: 0, failed: 0, uploadedToCtm: 0 };
    }
  }, []);

  /**
   * Obtener todas las llaves de todos los usuarios
   */
  const getAllKeys = useCallback(async (filters = {}) => {
    try {
      setIsLoading(true);
      setError(null);

      const users = await userService.getAllUsers();
      const allKeys = [];

      for (const user of users) {
        try {
          // Obtener todas las llaves del usuario con límite alto
          const userKeysResponse = await keyService.getKeysByUser(user.id, { 
            ...filters, 
            limit: 100 
          });
          const userKeys = userKeysResponse.keys.map(key => ({
            ...key,
            user: {
              id: user.id,
              firstName: user.firstName,
              lastName: user.lastName,
              email: user.email,
              company: user.company,
            }
          }));
          allKeys.push(...userKeys);
        } catch (error) {
          console.warn(`Error obteniendo llaves del usuario ${user.id}:`, error.message);
        }
      }

      return allKeys;
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Limpiar errores
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * Refrescar estadísticas
   */
  const refreshStats = useCallback(async () => {
    return await loadStats();
  }, [loadStats]);

  return {
    // Estado
    stats,
    usersWithKeys,
    isLoading,
    error,
    
    // Acciones
    loadStats,
    getUserKeys,
    getAllKeys,
    refreshStats,
    clearError,
  };
};

/**
 * useKeyStatistics Hook
 * Hook específico para estadísticas de llaves
 */
export const useKeyStatistics = () => {
  const [keyStats, setKeyStats] = useState({
    byAlgorithm: {},
    byStatus: {},
    byUser: {},
    recentActivity: [],
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  /**
   * Calcular estadísticas detalladas de llaves
   */
  const calculateKeyStatistics = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const users = await userService.getAllUsers();
      const allKeys = [];

      // Recopilar todas las llaves
      for (const user of users) {
        try {
          const userKeysResponse = await keyService.getKeysByUser(user.id);
          const userKeys = userKeysResponse.keys.map(key => ({
            ...key,
            userName: `${user.firstName} ${user.lastName}`,
            userEmail: user.email,
          }));
          allKeys.push(...userKeys);
        } catch (error) {
          console.warn(`Error obteniendo llaves del usuario ${user.id}:`, error.message);
        }
      }

      // Calcular estadísticas por algoritmo
      const byAlgorithm = allKeys.reduce((acc, key) => {
        acc[key.algorithm] = (acc[key.algorithm] || 0) + 1;
        return acc;
      }, {});

      // Calcular estadísticas por estado
      const byStatus = allKeys.reduce((acc, key) => {
        acc[key.status] = (acc[key.status] || 0) + 1;
        return acc;
      }, {});

      // Calcular estadísticas por usuario
      const byUser = allKeys.reduce((acc, key) => {
        const userKey = key.userName || 'Usuario desconocido';
        if (!acc[userKey]) {
          acc[userKey] = { total: 0, successful: 0, failed: 0 };
        }
        acc[userKey].total += 1;
        if (key.isSuccessful) {
          acc[userKey].successful += 1;
        } else {
          acc[userKey].failed += 1;
        }
        return acc;
      }, {});

      // Actividad reciente (últimas 10 llaves)
      const recentActivity = allKeys
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
        .slice(0, 10)
        .map(key => ({
          id: key.id,
          name: key.name,
          algorithm: key.algorithm,
          status: key.status,
          userName: key.userName,
          createdAt: key.createdAt,
        }));

      const statistics = {
        byAlgorithm,
        byStatus,
        byUser,
        recentActivity,
      };

      setKeyStats(statistics);
      return statistics;

    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Limpiar errores
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    // Estado
    keyStats,
    isLoading,
    error,
    
    // Acciones
    calculateKeyStatistics,
    clearError,
  };
};

export default useAdminStats;
