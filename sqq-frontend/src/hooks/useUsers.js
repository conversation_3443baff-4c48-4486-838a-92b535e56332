/**
 * useUsers Hook
 * Hook personalizado para manejo de usuarios
 */

import { useState, useCallback } from 'react';
import { userService } from '../services/index.js';

export const useUsers = () => {
  const [users, setUsers] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  /**
   * Obtener todos los usuarios
   */
  const getAllUsers = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await userService.getAllUsers();
      setUsers(response);
      
      return response;
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Crear nuevo usuario
   */
  const createUser = useCallback(async (userData) => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await userService.createUser(userData);
      
      // Actualizar lista de usuarios
      setUsers(prevUsers => [...prevUsers, response]);
      
      return response;
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Actualizar usuario
   */
  const updateUser = useCallback(async (userId, updateData) => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await userService.updateUser(userId, updateData);
      
      // Actualizar usuario en la lista
      setUsers(prevUsers => 
        prevUsers.map(user => 
          user.id === userId ? response : user
        )
      );
      
      return response;
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Eliminar usuario
   */
  const deleteUser = useCallback(async (userId) => {
    try {
      setIsLoading(true);
      setError(null);

      // TODO: Nicolás - Implementar userService.deleteUser cuando el endpoint esté listo
      await userService.deleteUser(userId);

      // Actualizar lista de usuarios removiendo el usuario eliminado
      setUsers(prevUsers => prevUsers.filter(user => user.id !== userId));

      return true;
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Obtener usuario por ID
   */
  const getUserById = useCallback(async (userId) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await userService.getUserById(userId);

      return response;
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Limpiar errores
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * Limpiar usuarios
   */
  const clearUsers = useCallback(() => {
    setUsers([]);
  }, []);

  return {
    // Estado
    users,
    isLoading,
    error,

    // Acciones
    getAllUsers,
    createUser,
    updateUser,
    deleteUser,
    getUserById,
    clearError,
    clearUsers,
  };
};

/**
 * useUser Hook
 * Hook para manejo de un usuario individual
 */
export const useUser = (userId) => {
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  /**
   * Cargar usuario
   */
  const loadUser = useCallback(async () => {
    if (!userId) return;
    
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await userService.getUserById(userId);
      setUser(response);
      
      return response;
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [userId]);

  /**
   * Actualizar usuario
   */
  const updateUser = useCallback(async (updateData) => {
    if (!userId) return;
    
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await userService.updateUser(userId, updateData);
      setUser(response);
      
      return response;
    } catch (error) {
      setError(error.message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [userId]);

  /**
   * Limpiar errores
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    // Estado
    user,
    isLoading,
    error,
    
    // Acciones
    loadUser,
    updateUser,
    clearError,
  };
};
