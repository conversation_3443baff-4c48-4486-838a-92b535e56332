import { useState, useEffect } from 'react';

/**
 * Hook personalizado para manejar el modo oscuro
 * Centraliza la lógica de dark mode para reutilización con persistencia
 */
const useDarkMode = () => {
  const [darkMode, setDarkMode] = useState(() => {
    // Leer desde localStorage al inicializar
    const saved = localStorage.getItem('darkMode');
    // Si no hay nada guardado, usar modo claro por defecto (false)
    return saved !== null ? JSON.parse(saved) : false;
  });

  // Aplicar clase dark al HTML cuando cambie el estado
  useEffect(() => {
    if (darkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [darkMode]);

  const toggleDarkMode = () => {
    const newDarkMode = !darkMode;
    setDarkMode(newDarkMode);

    // Guardar en localStorage para persistencia
    localStorage.setItem('darkMode', JSON.stringify(newDarkMode));
  };

  const setDarkModeValue = (value) => {
    setDarkMode(value);
    localStorage.setItem('darkMode', JSON.stringify(value));
  };

  // Función para resetear a modo claro
  const resetToLightMode = () => {
    setDarkMode(false);
    localStorage.setItem('darkMode', JSON.stringify(false));
    document.documentElement.classList.remove('dark');
  };

  return {
    darkMode,
    toggleDarkMode,
    setDarkMode: setDarkModeValue,
    resetToLightMode
  };
};

export default useDarkMode;
