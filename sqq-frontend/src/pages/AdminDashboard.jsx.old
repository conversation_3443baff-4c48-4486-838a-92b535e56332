import { useState } from "react";
import {
  Users,
  KeyRound,
  Settings,
  BarChart3,
  Activity,
} from "lucide-react";
import { useAuth, useUsers } from '../hooks/index';
import { useAdminStats } from '../hooks/useAdminStats';
import { DashboardLayout, DashboardStats } from '../components/dashboard';
import { UserManagement } from '../components/dashboard/admin';

const AdminDashboard = () => {
  const [activeSection, setActiveSection] = useState('dashboard');

  // Hooks para autenticación y usuarios
  const { user: currentUser, logout } = useAuth();
  const {
    users,
    isLoading: usersLoading,
    error: usersError,
    getAllUsers,
    createUser,
    updateUser,
    clearError
  } = useUsers();

  // Hook para estadísticas del admin
  const {
    stats,
    usersWithKeys,
    isLoading: statsLoading,
    error: statsError,
    loadStats,
    clearError: clearStatsError
  } = useAdminStats();

  // Cargar usuarios y estadísticas al montar el componente
  useEffect(() => {
    const loadData = async () => {
      try {
        // Cargar usuarios y estadísticas en paralelo
        await Promise.all([
          getAllUsers(),
          loadStats()
        ]);
      } catch (error) {
        console.error('Error loading admin data:', error);
      }
    };

    if (currentUser && (currentUser.role === 'ADMIN' || currentUser.role === 'admin')) {
      loadData();
    }
  }, [currentUser, getAllUsers, loadStats]);

  // Manejar logout
  const handleLogout = () => {
    logout();
  };

  // Funciones para gestión de usuarios
  const handleUpdateUser = async (userId, userData) => {
    await updateUser(userId, userData);
    // Refrescar datos después de actualizar
    await Promise.all([getAllUsers(), loadStats()]);
  };

  const handleCreateUser = async (userData) => {
    await createUser(userData);
    // Refrescar datos después de crear
    await Promise.all([getAllUsers(), loadStats()]);
  };

  const handleDeleteUser = async (userId) => {
    // TODO: Implementar endpoint de eliminación en el backend
    console.log('Eliminar usuario:', userId);
    // await deleteUser(userId);
  };

  // Configuración de navegación
  const navigationItems = [
    { key: 'dashboard', label: 'Dashboard', icon: BarChart3 },
    { key: 'users', label: 'Usuarios', icon: Users },
    { key: 'keys', label: 'Llaves', icon: KeyRound },
    { key: 'settings', label: 'Configuración', icon: Settings }
  ];

  // Configuración de estadísticas principales
  const mainStats = [
    {
      icon: Users,
      title: "Total Usuarios",
      value: stats.totalUsers,
      description: "Usuarios registrados en el sistema.",
      iconColor: "text-blue-500",
      valueColor: "text-blue-600 dark:text-blue-400"
    },
    {
      icon: KeyRound,
      title: "Llaves Activas",
      value: stats.activeKeys,
      description: "Llaves subidas a CTM exitosamente.",
      iconColor: "text-green-500",
      valueColor: "text-green-600 dark:text-green-400"
    },
    {
      icon: Activity,
      title: "Usuarios Activos",
      value: stats.activeUsers,
      description: "Usuarios con estado activo.",
      iconColor: "text-purple-500",
      valueColor: "text-purple-600 dark:text-purple-400"
    }
  ];

  // Configuración de estadísticas adicionales
  const miniStats = [
    {
      value: stats.totalKeys,
      label: "Total Llaves",
      valueColor: "text-gray-900 dark:text-white"
    },
    {
      value: stats.successfulKeys,
      label: "Exitosas",
      valueColor: "text-green-600 dark:text-green-400"
    },
    {
      value: stats.failedKeys,
      label: "Fallidas",
      valueColor: "text-red-600 dark:text-red-400"
    },
    {
      value: stats.uploadedToCtm,
      label: "En CTM",
      valueColor: "text-blue-600 dark:text-blue-400"
    }
  ];

  return (
    <DashboardLayout
      title="Quantum Admin"
      currentUser={currentUser}
      navigationItems={navigationItems}
      activeSection={activeSection}
      onSectionChange={setActiveSection}
      onLogout={handleLogout}
      expectedRole={['admin', 'ADMIN']}
    >
      {/* 🌙 Sidebar siempre visible (responsive como Gmail) */}
      <aside
        ref={sidebarRef}
        className={`w-64 sm:w-72 h-full shadow-xl z-10 p-6 flex flex-col justify-between transition-colors duration-500
        ${darkMode ? "bg-gray-800 text-white" : "bg-white text-gray-800"}`}
      >
        {/* 🔹 Información del Admin */}
        <div>
          <h2 className="text-2xl font-bold mb-4 text-center">Quantum Admin</h2>
          
          {/* Información del administrador */}
          {currentUser && (
            <div className={`mb-8 p-4 rounded-lg border ${darkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'}`}>
              <div className="flex items-center gap-3 mb-2">
                <User size={16} className="text-blue-500" />
                <span className="font-medium text-sm">
                  {currentUser.firstName} {currentUser.lastName}
                </span>
              </div>
              <div className="flex items-center gap-3">
                <Mail size={16} className="text-green-500" />
                <span className="text-sm text-gray-600 dark:text-white">{currentUser.email}</span>
              </div>
            </div>
          )}

          <nav className="space-y-4">
            <button
              onClick={() => setActiveSection('dashboard')}
              className={`flex items-center gap-3 px-4 py-2 rounded-lg transition-colors duration-300 w-full text-left ${
                activeSection === 'dashboard'
                  ? (darkMode ? 'bg-blue-900 text-blue-300' : 'bg-blue-100 text-blue-700')
                  : (darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100')
              }`}
            >
              <BarChart3 size={18} /> <span>Dashboard</span>
            </button>
            <button
              onClick={() => setActiveSection('users')}
              className={`flex items-center gap-3 px-4 py-2 rounded-lg transition-colors duration-300 w-full text-left ${
                activeSection === 'users'
                  ? (darkMode ? 'bg-blue-900 text-blue-300' : 'bg-blue-100 text-blue-700')
                  : (darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100')
              }`}
            >
              <Users size={18} /> <span>Usuarios</span>
            </button>
            <button
              onClick={() => setActiveSection('keys')}
              className={`flex items-center gap-3 px-4 py-2 rounded-lg transition-colors duration-300 w-full text-left ${
                activeSection === 'keys'
                  ? (darkMode ? 'bg-blue-900 text-blue-300' : 'bg-blue-100 text-blue-700')
                  : (darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100')
              }`}
            >
              <KeyRound size={18} /> <span>Llaves</span>
            </button>
            <button
              onClick={() => setActiveSection('settings')}
              className={`flex items-center gap-3 px-4 py-2 rounded-lg transition-colors duration-300 w-full text-left ${
                activeSection === 'settings'
                  ? (darkMode ? 'bg-blue-900 text-blue-300' : 'bg-blue-100 text-blue-700')
                  : (darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100')
              }`}
            >
              <Settings size={18} /> <span>Configuración</span>
            </button>
          </nav>
        </div>

        {/* 🔘 Botones: modo claro/oscuro + logout */}
        <div className="flex flex-col gap-4">
          <button
            onClick={toggleDarkMode}
            className={`flex items-center justify-center gap-2 w-full px-4 py-2 rounded-lg text-sm font-medium border transition duration-300
              ${darkMode
                ? "border-gray-500 text-white hover:bg-gray-700/40"
                : "border-gray-400 text-gray-800 hover:bg-gray-100"}`}
          >
            {darkMode ? (
              <>
                <Sun size={18} className="text-yellow-400 animate-pulse" />
                <span>Modo Claro</span>
              </>
            ) : (
              <>
                <Moon size={18} className="text-indigo-300 animate-pulse" />
                <span>Modo Oscuro</span>
              </>
            )}
          </button>

          <button
            onClick={handleLogout}
            className="bg-red-600 hover:bg-red-700 transition text-white font-semibold px-4 py-2 rounded-lg shadow text-center"
          >
            Cerrar sesión
          </button>
        </div>
      </aside>

      {/* 📄 Contenido Principal */}
      <main
        ref={mainContentRef}
        className={`flex-1 p-6 sm:p-10 overflow-y-auto relative transition-colors duration-500
          ${darkMode ? "bg-gray-700" : "bg-gray-50"}`}
      >


        {/* 🧩 Contenedor principal */}
        <div
          className={`relative z-10 transition-all duration-500 rounded-xl p-6 sm:p-8 shadow-md border
            ${darkMode
              ? "bg-gray-800 border-gray-700 text-white"
              : "bg-white border-gray-200 text-gray-900"}`}
        >
          {/* Renderizado condicional según la sección activa */}
          {activeSection === 'dashboard' && (
            <>
              <h1 className="text-3xl font-bold mb-4">
                Bienvenido, {currentUser ? `${currentUser.firstName} ${currentUser.lastName}` : 'Admin'}
              </h1>
              <p className="text-gray-600 dark:text-white mb-6">
                Aquí puedes gestionar usuarios, llaves y configuraciones del sistema.
              </p>

              {/* 📊 Tarjetas de información */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                <div className="p-6 rounded-lg border bg-white dark:bg-gray-700 dark:border-gray-600 shadow">
                  <div className="flex items-center gap-3 mb-2">
                    <Users size={20} className="text-blue-500" />
                    <span className="font-semibold text-gray-900 dark:text-white">Total Usuarios</span>
                  </div>
                  <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                    {statsLoading ? '...' : stats.totalUsers}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-white">
                    Usuarios registrados en el sistema.
                  </p>
                </div>

                <div className="p-6 rounded-lg border bg-white dark:bg-gray-700 dark:border-gray-600 shadow">
                  <div className="flex items-center gap-3 mb-2">
                    <KeyRound size={20} className="text-green-500" />
                    <span className="font-semibold text-gray-900 dark:text-white">Llaves Activas</span>
                  </div>
                  <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                    {statsLoading ? '...' : stats.activeKeys}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-white">
                    Llaves subidas a CTM exitosamente.
                  </p>
                </div>

                <div className="p-6 rounded-lg border bg-white dark:bg-gray-700 dark:border-gray-600 shadow">
                  <div className="flex items-center gap-3 mb-2">
                    <Activity size={20} className="text-purple-500" />
                    <span className="font-semibold text-gray-900 dark:text-white">Usuarios Activos</span>
                  </div>
                  <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                    {statsLoading ? '...' : stats.activeUsers}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-white">
                    Usuarios con estado activo.
                  </p>
                </div>
              </div>

              {/* Mostrar errores de estadísticas */}
              {statsError && (
                <div className="mt-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
                  Error cargando estadísticas: {statsError}
                  <button
                    onClick={clearStatsError}
                    className="ml-2 text-red-500 hover:text-red-700"
                  >
                    ✕
                  </button>
                </div>
              )}

              {/* Tarjetas adicionales de estadísticas */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mt-6">
                <div className="p-4 rounded-lg border bg-white dark:bg-gray-700 dark:border-gray-600 shadow">
                  <div className="text-center">
                    <p className="text-lg font-bold text-gray-900 dark:text-white">
                      {statsLoading ? '...' : stats.totalKeys}
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-300">Total Llaves</p>
                  </div>
                </div>

                <div className="p-4 rounded-lg border bg-white dark:bg-gray-700 dark:border-gray-600 shadow">
                  <div className="text-center">
                    <p className="text-lg font-bold text-green-600 dark:text-green-400">
                      {statsLoading ? '...' : stats.successfulKeys}
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-300">Exitosas</p>
                  </div>
                </div>

                <div className="p-4 rounded-lg border bg-white dark:bg-gray-700 dark:border-gray-600 shadow">
                  <div className="text-center">
                    <p className="text-lg font-bold text-red-600 dark:text-red-400">
                      {statsLoading ? '...' : stats.failedKeys}
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-300">Fallidas</p>
                  </div>
                </div>

                <div className="p-4 rounded-lg border bg-white dark:bg-gray-700 dark:border-gray-600 shadow">
                  <div className="text-center">
                    <p className="text-lg font-bold text-blue-600 dark:text-blue-400">
                      {statsLoading ? '...' : stats.uploadedToCtm}
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-300">En CTM</p>
                  </div>
                </div>
              </div>
            </>
          )}

          {/* Sección de Usuarios */}
          {activeSection === 'users' && (
            <>
              <div className="flex justify-between items-center mb-6">
                <h1 className="text-3xl font-bold">Gestión de Usuarios</h1>
                <button
                  onClick={() => setShowCreateModal(true)}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition flex items-center gap-2"
                >
                  <Plus size={16} />
                  Crear Usuario
                </button>
              </div>

              {/* Mostrar errores */}
              {usersError && (
                <div className="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
                  Error: {usersError}
                  <button
                    onClick={clearError}
                    className="ml-2 text-red-500 hover:text-red-700"
                  >
                    ✕
                  </button>
                </div>
              )}

              {/* Mostrar loading */}
              {(usersLoading || statsLoading) && (
                <div className="flex items-center justify-center py-8">
                  <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                  <span className="ml-3">
                    {usersLoading && statsLoading ? 'Cargando usuarios y estadísticas...' :
                     usersLoading ? 'Cargando usuarios...' : 'Cargando estadísticas de llaves...'}
                  </span>
                </div>
              )}

              {/* Lista de usuarios */}
              {!usersLoading && !statsLoading && (
                <div className="space-y-4">
                  {(usersWithKeys.length > 0 ? usersWithKeys : users).length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      No hay usuarios registrados
                    </div>
                  ) : (
                    (usersWithKeys.length > 0 ? usersWithKeys : users).map((user) => (
                      <div
                        key={user.id}
                        className={`p-6 rounded-lg border shadow-sm ${
                          darkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-200'
                        }`}
                      >
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <div className="flex items-center gap-4 mb-3">
                              <h3 className="text-xl font-semibold">
                                {user.firstName} {user.lastName}
                              </h3>
                              <span
                                className={`px-3 py-1 rounded-full text-xs font-medium ${
                                  user.isActive
                                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                                    : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                                }`}
                              >
                                {user.isActive ? 'Activo' : 'Inactivo'}
                              </span>
                              <span
                                className={`px-3 py-1 rounded-full text-xs font-medium ${
                                  user.role === 'ADMIN'
                                    ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
                                    : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                                }`}
                              >
                                {user.role}
                              </span>
                            </div>
                            <p className="text-gray-600 dark:text-white mb-2">{user.email}</p>
                            {user.company && (
                              <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                                Empresa: {user.company}
                              </p>
                            )}

                            {/* Información de configuración */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                              {user.ctmIpAddress && (
                                <div className={`p-3 rounded border ${
                                  darkMode ? 'bg-gray-600 border-gray-500' : 'bg-gray-50 border-gray-200'
                                }`}>
                                  <h5 className="font-medium text-sm mb-1">CTM Configuration</h5>
                                  <p className="text-xs text-gray-600 dark:text-gray-300">
                                    IP: {user.ctmIpAddress}
                                  </p>
                                  <p className="text-xs text-gray-600 dark:text-gray-300">
                                    Usuario: {user.ctmUsername}
                                  </p>
                                  <p className="text-xs text-gray-600 dark:text-gray-300">
                                    Dominio: {user.ctmDomain}
                                  </p>
                                </div>
                              )}

                              {user.seqrngIpAddress && (
                                <div className={`p-3 rounded border ${
                                  darkMode ? 'bg-gray-600 border-gray-500' : 'bg-gray-50 border-gray-200'
                                }`}>
                                  <h5 className="font-medium text-sm mb-1">SEQRNG Configuration</h5>
                                  <p className="text-xs text-gray-600 dark:text-gray-300">
                                    IP: {user.seqrngIpAddress}
                                  </p>
                                  <p className="text-xs text-gray-600 dark:text-gray-300">
                                    Token: {user.seqrngApiToken ? '***' + user.seqrngApiToken.slice(-4) : 'No configurado'}
                                  </p>
                                </div>
                              )}
                            </div>

                            {/* Información de llaves del usuario */}
                            {user.keyStats && (
                              <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
                                <h5 className="font-medium text-sm mb-2 text-gray-900 dark:text-white">
                                  Estadísticas de Llaves
                                </h5>
                                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                                  <div className="text-center">
                                    <p className="text-lg font-bold text-blue-600 dark:text-blue-400">
                                      {user.keyStats.total}
                                    </p>
                                    <p className="text-xs text-gray-600 dark:text-gray-300">Total</p>
                                  </div>
                                  <div className="text-center">
                                    <p className="text-lg font-bold text-green-600 dark:text-green-400">
                                      {user.keyStats.successful}
                                    </p>
                                    <p className="text-xs text-gray-600 dark:text-gray-300">Exitosas</p>
                                  </div>
                                  <div className="text-center">
                                    <p className="text-lg font-bold text-red-600 dark:text-red-400">
                                      {user.keyStats.failed}
                                    </p>
                                    <p className="text-xs text-gray-600 dark:text-gray-300">Fallidas</p>
                                  </div>
                                  <div className="text-center">
                                    <p className="text-lg font-bold text-purple-600 dark:text-purple-400">
                                      {user.keyStats.uploadedToCtm}
                                    </p>
                                    <p className="text-xs text-gray-600 dark:text-gray-300">En CTM</p>
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>

                          {/* Botones de acción */}
                          <div className="flex gap-2 ml-4">
                            <button
                              onClick={() => handleEditUser(user)}
                              className="p-2 rounded-lg bg-blue-100 hover:bg-blue-200 text-blue-700 dark:bg-blue-900 dark:hover:bg-blue-800 dark:text-blue-300 transition"
                              title="Editar usuario"
                            >
                              <Edit size={16} />
                            </button>
                            <button
                              onClick={() => handleDeleteUser(user)}
                              className="p-2 rounded-lg bg-red-100 hover:bg-red-200 text-red-700 dark:bg-red-900 dark:hover:bg-red-800 dark:text-red-300 transition"
                              title="Eliminar usuario"
                            >
                              <Trash2 size={16} />
                            </button>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              )}
            </>
          )}

          {/* Otras secciones placeholder */}
          {activeSection === 'keys' && (
            <div>
              <h1 className="text-3xl font-bold mb-4">Gestión de Llaves</h1>
              <p className="text-gray-600 dark:text-white">
                Sección en desarrollo para gestionar todas las llaves API del sistema.
              </p>
            </div>
          )}

          {activeSection === 'settings' && (
            <div>
              <h1 className="text-3xl font-bold mb-4">Configuración</h1>
              <p className="text-gray-600 dark:text-white">
                Sección en desarrollo para configuraciones del sistema.
              </p>
            </div>
          )}
        </div>
      </main>

      {/* Modal de Edición */}
      {showEditModal && editingUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 overflow-y-auto">
          <div className={`p-6 rounded-lg shadow-xl max-w-4xl w-full mx-4 my-8 ${
            darkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-900'
          }`}>
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-bold">Editar Usuario</h3>
              <button
                onClick={() => setShowEditModal(false)}
                className="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
              >
                <X size={20} />
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Información Personal */}
              <div className="space-y-4">
                <h4 className="text-lg font-semibold border-b pb-2">Información Personal</h4>

                <div>
                  <label className="block text-sm font-medium mb-1">Nombre *</label>
                  <input
                    type="text"
                    value={editingUser.firstName || ''}
                    onChange={(e) => handleInputChange('firstName', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg ${
                      darkMode
                        ? 'bg-gray-700 border-gray-600 text-white'
                        : 'bg-white border-gray-300 text-gray-900'
                    }`}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Apellido *</label>
                  <input
                    type="text"
                    value={editingUser.lastName || ''}
                    onChange={(e) => handleInputChange('lastName', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg ${
                      darkMode
                        ? 'bg-gray-700 border-gray-600 text-white'
                        : 'bg-white border-gray-300 text-gray-900'
                    }`}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Email *</label>
                  <input
                    type="email"
                    value={editingUser.email || ''}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg ${
                      darkMode
                        ? 'bg-gray-700 border-gray-600 text-white'
                        : 'bg-white border-gray-300 text-gray-900'
                    }`}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Empresa</label>
                  <input
                    type="text"
                    value={editingUser.company || ''}
                    onChange={(e) => handleInputChange('company', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg ${
                      darkMode
                        ? 'bg-gray-700 border-gray-600 text-white'
                        : 'bg-white border-gray-300 text-gray-900'
                    }`}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Rol</label>
                  <select
                    value={editingUser.role || 'USER'}
                    onChange={(e) => handleInputChange('role', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg ${
                      darkMode
                        ? 'bg-gray-700 border-gray-600 text-white'
                        : 'bg-white border-gray-300 text-gray-900'
                    }`}
                  >
                    <option value="USER">Usuario</option>
                    <option value="ADMIN">Administrador</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Estado</label>
                  <select
                    value={editingUser.isActive ? 'true' : 'false'}
                    onChange={(e) => handleInputChange('isActive', e.target.value === 'true')}
                    className={`w-full px-3 py-2 border rounded-lg ${
                      darkMode
                        ? 'bg-gray-700 border-gray-600 text-white'
                        : 'bg-white border-gray-300 text-gray-900'
                    }`}
                  >
                    <option value="true">Activo</option>
                    <option value="false">Inactivo</option>
                  </select>
                </div>
              </div>

              {/* Configuración de Servicios */}
              <div className="space-y-4">
                <h4 className="text-lg font-semibold border-b pb-2">Configuración de Servicios</h4>

                {/* CTM Configuration */}
                <div className="space-y-3">
                  <h5 className="font-medium text-blue-600 dark:text-blue-400">CipherTrust Manager</h5>

                  <div>
                    <label className="block text-sm font-medium mb-1">IP Address</label>
                    <input
                      type="text"
                      value={editingUser.ctmIpAddress || ''}
                      onChange={(e) => handleInputChange('ctmIpAddress', e.target.value)}
                      placeholder="https://ctm.example.com:443"
                      className={`w-full px-3 py-2 border rounded-lg ${
                        darkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-1">Username</label>
                    <input
                      type="text"
                      value={editingUser.ctmUsername || ''}
                      onChange={(e) => handleInputChange('ctmUsername', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-lg ${
                        darkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-1">Password</label>
                    <input
                      type="password"
                      value={editingUser.ctmPassword || ''}
                      onChange={(e) => handleInputChange('ctmPassword', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-lg ${
                        darkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-1">Domain</label>
                    <input
                      type="text"
                      value={editingUser.ctmDomain || ''}
                      onChange={(e) => handleInputChange('ctmDomain', e.target.value)}
                      placeholder="root"
                      className={`w-full px-3 py-2 border rounded-lg ${
                        darkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    />
                  </div>
                </div>

                {/* SEQRNG Configuration */}
                <div className="space-y-3">
                  <h5 className="font-medium text-green-600 dark:text-green-400">SEQRNG</h5>

                  <div>
                    <label className="block text-sm font-medium mb-1">IP Address</label>
                    <input
                      type="text"
                      value={editingUser.seqrngIpAddress || ''}
                      onChange={(e) => handleInputChange('seqrngIpAddress', e.target.value)}
                      placeholder="https://seqrng.example.com:1982"
                      className={`w-full px-3 py-2 border rounded-lg ${
                        darkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-1">API Token</label>
                    <input
                      type="text"
                      value={editingUser.seqrngApiToken || ''}
                      onChange={(e) => handleInputChange('seqrngApiToken', e.target.value)}
                      placeholder="1|cifovFzUWTeAPUeAYlZYefsY3tXFf9Z1TV5WV0YL"
                      className={`w-full px-3 py-2 border rounded-lg ${
                        darkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className="flex gap-3 mt-6">
              <button
                onClick={saveUserChanges}
                disabled={isSaving}
                className={`flex-1 px-4 py-2 rounded-lg transition flex items-center justify-center gap-2 ${
                  isSaving
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700'
                } text-white`}
              >
                {isSaving ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    Guardando...
                  </>
                ) : (
                  <>
                    <Save size={16} />
                    Guardar Cambios
                  </>
                )}
              </button>
              <button
                onClick={() => setShowEditModal(false)}
                disabled={isSaving}
                className={`flex-1 px-4 py-2 rounded-lg transition ${
                  isSaving
                    ? 'opacity-50 cursor-not-allowed'
                    : darkMode
                      ? 'bg-gray-700 hover:bg-gray-600 text-white'
                      : 'bg-gray-200 hover:bg-gray-300 text-gray-900'
                }`}
              >
                Cancelar
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal de Confirmación de Eliminación */}
      {showDeleteModal && userToDelete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`p-6 rounded-lg shadow-xl max-w-md w-full mx-4 ${
            darkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-900'
          }`}>
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-bold text-red-600 dark:text-red-400">Eliminar Usuario</h3>
              <button
                onClick={() => setShowDeleteModal(false)}
                className="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
              >
                <X size={20} />
              </button>
            </div>

            <p className="mb-6">
              ¿Estás seguro de que deseas eliminar al usuario <strong>{userToDelete.firstName} {userToDelete.lastName}</strong>?
              Esta acción no se puede deshacer.
            </p>

            <div className="flex gap-3">
              <button
                onClick={confirmDeleteUser}
                className="flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition"
              >
                Eliminar
              </button>
              <button
                onClick={() => setShowDeleteModal(false)}
                className={`flex-1 px-4 py-2 rounded-lg transition ${
                  darkMode
                    ? 'bg-gray-700 hover:bg-gray-600 text-white'
                    : 'bg-gray-200 hover:bg-gray-300 text-gray-900'
                }`}
              >
                Cancelar
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal de Creación de Usuario */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 overflow-y-auto">
          <div className={`p-6 rounded-lg shadow-xl max-w-4xl w-full mx-4 my-8 ${
            darkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-900'
          }`}>
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-bold">Crear Nuevo Usuario</h3>
              <button
                onClick={() => setShowCreateModal(false)}
                className="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
              >
                <X size={20} />
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Información Personal */}
              <div className="space-y-4">
                <h4 className="text-lg font-semibold border-b pb-2">Información Personal</h4>

                <div>
                  <label className="block text-sm font-medium mb-1">Nombre *</label>
                  <input
                    type="text"
                    value={newUser.firstName}
                    onChange={(e) => handleNewUserChange('firstName', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg ${
                      darkMode
                        ? 'bg-gray-700 border-gray-600 text-white'
                        : 'bg-white border-gray-300 text-gray-900'
                    }`}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Apellido *</label>
                  <input
                    type="text"
                    value={newUser.lastName}
                    onChange={(e) => handleNewUserChange('lastName', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg ${
                      darkMode
                        ? 'bg-gray-700 border-gray-600 text-white'
                        : 'bg-white border-gray-300 text-gray-900'
                    }`}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Email *</label>
                  <input
                    type="email"
                    value={newUser.email}
                    onChange={(e) => handleNewUserChange('email', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg ${
                      darkMode
                        ? 'bg-gray-700 border-gray-600 text-white'
                        : 'bg-white border-gray-300 text-gray-900'
                    }`}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Contraseña *</label>
                  <input
                    type="password"
                    value={newUser.password}
                    onChange={(e) => handleNewUserChange('password', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg ${
                      darkMode
                        ? 'bg-gray-700 border-gray-600 text-white'
                        : 'bg-white border-gray-300 text-gray-900'
                    }`}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Empresa</label>
                  <input
                    type="text"
                    value={newUser.company}
                    onChange={(e) => handleNewUserChange('company', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg ${
                      darkMode
                        ? 'bg-gray-700 border-gray-600 text-white'
                        : 'bg-white border-gray-300 text-gray-900'
                    }`}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">Rol</label>
                  <select
                    value={newUser.role}
                    onChange={(e) => handleNewUserChange('role', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg ${
                      darkMode
                        ? 'bg-gray-700 border-gray-600 text-white'
                        : 'bg-white border-gray-300 text-gray-900'
                    }`}
                  >
                    <option value="USER">Usuario</option>
                    <option value="ADMIN">Administrador</option>
                  </select>
                </div>
              </div>

              {/* Configuración de Servicios */}
              <div className="space-y-4">
                <h4 className="text-lg font-semibold border-b pb-2">Configuración de Servicios</h4>

                {/* CTM Configuration */}
                <div className="space-y-3">
                  <h5 className="font-medium text-blue-600 dark:text-blue-400">CipherTrust Manager</h5>

                  <div>
                    <label className="block text-sm font-medium mb-1">IP Address</label>
                    <input
                      type="text"
                      value={newUser.ctmIpAddress}
                      onChange={(e) => handleNewUserChange('ctmIpAddress', e.target.value)}
                      placeholder="https://ctm.example.com:443"
                      className={`w-full px-3 py-2 border rounded-lg ${
                        darkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-1">Username</label>
                    <input
                      type="text"
                      value={newUser.ctmUsername}
                      onChange={(e) => handleNewUserChange('ctmUsername', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-lg ${
                        darkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-1">Password</label>
                    <input
                      type="password"
                      value={newUser.ctmPassword}
                      onChange={(e) => handleNewUserChange('ctmPassword', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-lg ${
                        darkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-1">Domain</label>
                    <input
                      type="text"
                      value={newUser.ctmDomain}
                      onChange={(e) => handleNewUserChange('ctmDomain', e.target.value)}
                      placeholder="root"
                      className={`w-full px-3 py-2 border rounded-lg ${
                        darkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    />
                  </div>
                </div>

                {/* SEQRNG Configuration */}
                <div className="space-y-3">
                  <h5 className="font-medium text-green-600 dark:text-green-400">SEQRNG</h5>

                  <div>
                    <label className="block text-sm font-medium mb-1">IP Address</label>
                    <input
                      type="text"
                      value={newUser.seqrngIpAddress}
                      onChange={(e) => handleNewUserChange('seqrngIpAddress', e.target.value)}
                      placeholder="https://seqrng.example.com:1982"
                      className={`w-full px-3 py-2 border rounded-lg ${
                        darkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-1">API Token</label>
                    <input
                      type="text"
                      value={newUser.seqrngApiToken}
                      onChange={(e) => handleNewUserChange('seqrngApiToken', e.target.value)}
                      placeholder="1|cifovFzUWTeAPUeAYlZYefsY3tXFf9Z1TV5WV0YL"
                      className={`w-full px-3 py-2 border rounded-lg ${
                        darkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className="flex gap-3 mt-6">
              <button
                onClick={handleCreateUser}
                disabled={isSaving}
                className={`flex-1 px-4 py-2 rounded-lg transition flex items-center justify-center gap-2 ${
                  isSaving
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-green-600 hover:bg-green-700'
                } text-white`}
              >
                {isSaving ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    Creando...
                  </>
                ) : (
                  <>
                    <Plus size={16} />
                    Crear Usuario
                  </>
                )}
              </button>
              <button
                onClick={() => setShowCreateModal(false)}
                disabled={isSaving}
                className={`flex-1 px-4 py-2 rounded-lg transition ${
                  isSaving
                    ? 'opacity-50 cursor-not-allowed'
                    : darkMode
                      ? 'bg-gray-700 hover:bg-gray-600 text-white'
                      : 'bg-gray-200 hover:bg-gray-300 text-gray-900'
                }`}
              >
                Cancelar
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminDashboard;
