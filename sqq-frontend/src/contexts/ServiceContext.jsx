/**
 * Service Context
 * Contexto global para manejar el servicio seleccionado por el usuario
 */

import { createContext, useContext, useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { useServices } from '../hooks/useServices';
import { useAuth } from '../hooks/useAuth';
import authService from '../services/auth/authService';

const ServiceContext = createContext();

export const useServiceContext = () => {
  const context = useContext(ServiceContext);
  if (!context) {
    throw new Error('useServiceContext must be used within a ServiceProvider');
  }
  return context;
};

export const ServiceProvider = ({ children }) => {
  const [selectedService, setSelectedService] = useState(null);
  const [availableServices, setAvailableServices] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hasInitialized, setHasInitialized] = useState(false);

  const { getUserServices } = useServices(); // Solo mantenemos getUserServices como fallback
  const { user: currentUser, isLoading: authLoading, isAuthenticated } = useAuth();

  // Debug log para verificar el usuario
  console.log('🔍 ServiceContext: Estado de autenticación:', {
    currentUser,
    authLoading,
    isAuthenticated
  });

  // Cargar servicios del usuario (solo cuando cambia el usuario)
  useEffect(() => {
    const loadUserServices = async () => {
      console.log('🔍 ServiceContext: Evaluando usuario:', {
        hasUser: !!currentUser,
        userId: currentUser?.id,
        userRole: currentUser?.role,
        authLoading,
        isAuthenticated,
        isValidRole: currentUser?.role === 'USER' || currentUser?.role === 'user'
      });

      // No hacer nada si la autenticación aún está cargando
      if (authLoading) {
        console.log('⏳ ServiceContext: Esperando autenticación...');
        return;
      }

      if (isAuthenticated && currentUser && currentUser.id && (currentUser.role === 'USER' || currentUser.role === 'user')) {
        console.log('🔄 ServiceContext: Cargando servicios para usuario:', currentUser.id);

        // Primero intentar obtener servicios almacenados del login
        const storedServices = authService.getStoredServices();
        console.log('📦 ServiceContext: Servicios almacenados:', storedServices);

        if (storedServices && (storedServices.ctms || storedServices.hsms)) {
          // Usar servicios almacenados
          console.log('✅ ServiceContext: Usando servicios almacenados');
          const formattedServices = [
            ...(storedServices.ctms || []).map(ctm => ({
              id: ctm.id,
              name: ctm.name,
              type: 'CTM',
              description: ctm.description || 'CipherTrust Manager',
              isActive: ctm.isActive,
              hasCompleteConfiguration: ctm.hasCompleteConfiguration
            })),
            ...(storedServices.hsms || []).map(hsm => ({
              id: hsm.id,
              name: hsm.name,
              type: 'HSM',
              description: hsm.description || 'Hardware Security Module',
              isActive: hsm.isActive,
              hasCompleteConfiguration: hsm.hasCompleteConfiguration
            }))
          ];

          setAvailableServices(formattedServices);
          setHasInitialized(true);

          // Seleccionar el primer servicio si no hay uno seleccionado
          if (!selectedService && formattedServices.length > 0) {
            console.log('🎯 ServiceContext: Seleccionando primer servicio:', formattedServices[0].name);
            setSelectedService(formattedServices[0]);
          }
        } else {
          // Fallback: cargar servicios desde la API
          console.log('🔄 ServiceContext: Cargando servicios desde API como fallback');
          try {
            setIsLoading(true);
            await getUserServices();
            console.log('✅ ServiceContext: Servicios cargados desde API');
          } catch (error) {
            console.error('❌ ServiceContext: Error loading user services:', error);
            setAvailableServices([]);
            setHasInitialized(true);
          } finally {
            setIsLoading(false);
          }
        }
      } else {
        console.log('🚫 ServiceContext: Usuario no válido o no es USER, limpiando servicios', {
          hasUser: !!currentUser,
          userId: currentUser?.id,
          userRole: currentUser?.role,
          isAuthenticated
        });
        // Si no es usuario, limpiar servicios
        setAvailableServices([]);
        setSelectedService(null);
        setHasInitialized(true);
      }
    };

    loadUserServices();
  }, [currentUser?.id, currentUser?.role, authLoading, isAuthenticated]);

  // Este useEffect ahora solo se usa como fallback cuando se cargan servicios desde la API
  // La mayoría de las veces los servicios vendrán del login y se procesarán en el useEffect anterior

  const handleServiceChange = (service) => {
    setSelectedService(service);
    // Guardar en localStorage para persistencia
    if (service) {
      localStorage.setItem('selectedService', JSON.stringify(service));
    } else {
      localStorage.removeItem('selectedService');
    }
  };

  // Restaurar servicio seleccionado desde localStorage
  useEffect(() => {
    const savedService = localStorage.getItem('selectedService');
    if (savedService && !selectedService && availableServices.length > 0 && hasInitialized) {
      try {
        const parsedService = JSON.parse(savedService);
        // Verificar que el servicio guardado aún esté disponible
        const isServiceAvailable = availableServices.some(
          service => service.id === parsedService.id && service.type === parsedService.type
        );
        if (isServiceAvailable) {
          setSelectedService(parsedService);
        } else {
          // Si el servicio guardado no está disponible, limpiar localStorage
          localStorage.removeItem('selectedService');
        }
      } catch (error) {
        console.error('Error parsing saved service:', error);
        localStorage.removeItem('selectedService');
      }
    }
  }, [availableServices, selectedService, hasInitialized]);

  const value = {
    selectedService,
    availableServices,
    isLoading,
    hasInitialized,
    handleServiceChange,
    // Funciones de utilidad
    getSelectedServiceType: () => selectedService?.type || null,
    getSelectedServiceId: () => selectedService?.id || null,
    getSelectedServiceName: () => selectedService?.name || null,
    hasServices: () => availableServices.length > 0,
    isServiceSelected: () => selectedService !== null,
  };

  return (
    <ServiceContext.Provider value={value}>
      {children}
    </ServiceContext.Provider>
  );
};

ServiceProvider.propTypes = {
  children: PropTypes.node.isRequired,
};

export default ServiceContext;
