@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  overflow-y: hidden;
}

/* Efecto RGB profesional verde oscuro para el Reporte de Auto-certificación */
@keyframes rgb-flow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes rgb-border-glow {
  0% {
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.4), 0 0 40px rgba(34, 197, 94, 0.2);
  }
  25% {
    box-shadow: 0 0 20px rgba(21, 128, 61, 0.4), 0 0 40px rgba(21, 128, 61, 0.2);
  }
  50% {
    box-shadow: 0 0 20px rgba(15, 78, 46, 0.4), 0 0 40px rgba(15, 78, 46, 0.2);
  }
  75% {
    box-shadow: 0 0 20px rgba(5, 46, 22, 0.4), 0 0 40px rgba(5, 46, 22, 0.2);
  }
  100% {
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.4), 0 0 40px rgba(34, 197, 94, 0.2);
  }
}

.rgb-card-professional {
  position: relative;
  background: linear-gradient(
    -45deg,
    rgba(34, 197, 94, 0.15),
    rgba(21, 128, 61, 0.15),
    rgba(15, 78, 46, 0.15),
    rgba(5, 46, 22, 0.15),
    rgba(0, 0, 0, 0.25),
    rgba(22, 101, 52, 0.15),
    rgba(20, 83, 45, 0.15),
    rgba(34, 197, 94, 0.15)
  );
  background-size: 400% 400%;
  animation: rgb-flow 10s ease-in-out infinite;
  border-radius: 12px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(34, 197, 94, 0.1);
  overflow: hidden;
}

.rgb-card-professional::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: linear-gradient(
    -45deg,
    #22c55e,
    #15803d,
    #0f4e2e,
    #052e16,
    #000000,
    #166534,
    #14532d,
    #22c55e
  );
  background-size: 400% 400%;
  animation: rgb-flow 6s ease-in-out infinite;
  border-radius: 15px;
  z-index: -1;
  opacity: 0.8;
  filter: blur(1px);
}

.rgb-card-professional::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 12px;
  z-index: 0;
}
