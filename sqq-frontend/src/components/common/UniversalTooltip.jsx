import { useState, useRef, useEffect } from 'react';
import { Info } from 'lucide-react';
import PropTypes from 'prop-types';

/**
 * Componente Tooltip universal con estilo consistente
 */
const UniversalTooltip = ({ 
  darkMode, 
  title, 
  content, 
  iconColor = "blue",
  className = "",
  size = "default", // "small", "default", "large"
  position = "right" // "right", "top"
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const tooltipRef = useRef(null);

  // Cerrar tooltip al hacer clic fuera
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (tooltipRef.current && !tooltipRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen]);

  // Configurar colores según el iconColor
  const getIconColors = () => {
    switch (iconColor) {
      case "green":
        return {
          bg: darkMode ? 'bg-green-600' : 'bg-green-500',
          text: darkMode ? 'text-green-400' : 'text-green-500',
          hover: darkMode ? 'hover:text-green-300' : 'hover:text-green-700'
        };
      case "blue":
        return {
          bg: darkMode ? 'bg-blue-600' : 'bg-blue-500',
          text: darkMode ? 'text-blue-400' : 'text-blue-500',
          hover: darkMode ? 'hover:text-blue-300' : 'hover:text-blue-700'
        };
      case "gray":
        return {
          bg: darkMode ? 'bg-gray-600' : 'bg-gray-500',
          text: darkMode ? 'text-gray-400' : 'text-gray-500',
          hover: darkMode ? 'hover:text-gray-300' : 'hover:text-gray-700'
        };
      default:
        return {
          bg: darkMode ? 'bg-blue-600' : 'bg-blue-500',
          text: darkMode ? 'text-blue-400' : 'text-blue-500',
          hover: darkMode ? 'hover:text-blue-300' : 'hover:text-blue-700'
        };
    }
  };

  const iconColors = getIconColors();
  const iconSize = size === "small" ? 12 : size === "large" ? 18 : 14;
  const buttonSize = size === "small" ? "p-1" : size === "large" ? "p-2" : "p-1.5";

  // Configurar posicionamiento según la prop position
  const getTooltipPosition = () => {
    if (position === "top") {
      return {
        container: "absolute bottom-full left-0 mb-2",
        arrow: "absolute top-full left-1/2 transform -translate-x-1/2",
        arrowBorders: darkMode ? "border-r border-b border-gray-600" : "border-r border-b border-gray-200"
      };
    } else {
      return {
        container: "absolute top-0 left-full ml-2",
        arrow: "absolute top-1/2 -left-1 transform -translate-y-1/2",
        arrowBorders: darkMode ? "border-l border-t border-gray-600" : "border-l border-t border-gray-200"
      };
    }
  };

  const tooltipPosition = getTooltipPosition();

  return (
    <div className={`relative inline-block ${className}`} ref={tooltipRef}>
      <button
        onMouseEnter={() => setIsOpen(true)}
        onMouseLeave={() => setIsOpen(false)}
        onClick={() => setIsOpen(!isOpen)}
        className={`${buttonSize} rounded-full transition-all duration-200 hover:scale-110 ${
          darkMode
            ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-600'
            : 'text-gray-500 hover:text-gray-700 hover:bg-gray-200'
        }`}
        // Removido el title attribute para evitar tooltip duplicado
      >
        <Info size={iconSize} />
      </button>

      {/* Tooltip - Posicionamiento dinámico */}
      {isOpen && (
        <div className={`${tooltipPosition.container} w-64 p-3 rounded-lg border shadow-lg z-50 transition-all duration-200 ${
          darkMode
            ? 'bg-gray-800 border-gray-600 text-gray-200'
            : 'bg-white border-gray-200 text-gray-700'
        }`}
        style={{
          minWidth: '200px'
        }}>
          <div className="flex items-start gap-2">
            <div className={`p-1 rounded-full ${iconColors.bg}`}>
              <Info size={12} className="text-white" />
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium mb-1">
                {title}
              </p>
              <p className="text-xs leading-relaxed opacity-90">
                {content}
              </p>
            </div>
          </div>
          
          {/* Flecha del tooltip - Posicionamiento dinámico */}
          <div className={`${tooltipPosition.arrow} w-2 h-2 rotate-45 ${
            darkMode ? `bg-gray-800 ${tooltipPosition.arrowBorders}` : `bg-white ${tooltipPosition.arrowBorders}`
          }`}></div>
        </div>
      )}
    </div>
  );
};

UniversalTooltip.propTypes = {
  darkMode: PropTypes.bool.isRequired,
  title: PropTypes.string.isRequired,
  content: PropTypes.string.isRequired,
  iconColor: PropTypes.oneOf(['blue', 'green', 'gray']),
  className: PropTypes.string,
  size: PropTypes.oneOf(['small', 'default', 'large']),
  position: PropTypes.oneOf(['right', 'top'])
};

export default UniversalTooltip;
