/**
 * Global Service Selector
 * Selector global de servicios que aparece en la parte superior del dashboard de usuario
 */

import { useState, useRef, useEffect } from 'react';
import { ChevronDown, Server, AlertCircle } from 'lucide-react';
import PropTypes from 'prop-types';
import { useLanguage } from '../../hooks';
import { useServiceContext } from '../../contexts/ServiceContext';

const GlobalServiceSelector = ({ darkMode, className = "" }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);
  const { t } = useLanguage();
  
  const {
    selectedService,
    availableServices,
    isLoading,
    hasInitialized,
    handleServiceChange,
    hasServices,
    isServiceSelected
  } = useServiceContext();

  // Cerrar dropdown al hacer clic fuera
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Determinar si el servicio seleccionado es HSM para cambiar el color del icono
  const selectedServiceType = selectedService?.type;
  const isHSMSelected = selectedServiceType === 'HSM';
  
  const iconColor = isHSMSelected 
    ? (darkMode ? 'bg-emerald-600 group-hover:bg-emerald-500' : 'bg-emerald-500 group-hover:bg-emerald-600')
    : (darkMode ? 'bg-blue-600 group-hover:bg-blue-500' : 'bg-blue-500 group-hover:bg-blue-600');

  // Agrupar servicios por tipo
  const ctmServices = availableServices.filter(service => service.type === 'CTM');
  const hsmServices = availableServices.filter(service => service.type === 'HSM');

  // Solo ocultar el selector si ya se inicializó y no hay servicios
  if (!hasServices() && !isLoading && hasInitialized) {
    return null; // No mostrar si no hay servicios después de la inicialización
  }

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Botón principal */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`group flex items-center gap-3 px-4 py-3 rounded-xl transition-all duration-300 border-2 min-w-[280px] ${
          darkMode
            ? 'bg-gray-800 border-gray-600 hover:border-gray-500 text-gray-200'
            : 'bg-white border-gray-200 hover:border-gray-300 text-gray-800'
        } ${isOpen ? 'ring-2 ring-blue-500/20' : ''}`}
        disabled={isLoading || !hasInitialized}
      >
        {/* Icono del servicio */}
        <div className={`p-2 rounded-lg transition-all duration-300 ${iconColor}`}>
          <Server size={20} className="text-white" />
        </div>

        {/* Información del servicio */}
        <div className="flex-1 text-left">
          <div className="text-sm font-medium">
            {(isLoading || !hasInitialized) ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                <span>{t('common.serviceSelector.loading', 'Cargando...')}</span>
              </div>
            ) : isServiceSelected() ? (
              <>
                <span>{selectedService.name}</span>
                <div className="text-xs opacity-75 mt-0.5">
                  {selectedService.type} • {selectedService.description || 'Servicio activo'}
                </div>
              </>
            ) : (
              <span className="text-gray-500">
                {t('common.serviceSelector.selectService', 'Seleccionar servicio')}
              </span>
            )}
          </div>
        </div>

        {/* Icono de dropdown */}
        <ChevronDown 
          size={20} 
          className={`transition-transform duration-300 ${
            isOpen ? 'rotate-180' : 'rotate-0'
          } ${(isLoading || !hasInitialized) ? 'opacity-50' : ''}`}
        />
      </button>

      {/* Dropdown */}
      {isOpen && !isLoading && hasInitialized && (
        <div className={`absolute top-full left-0 right-0 mt-2 rounded-xl shadow-2xl border-2 z-50 ${
          darkMode
            ? 'bg-gray-800 border-gray-600'
            : 'bg-white border-gray-200'
        }`}>
          {/* Lista de servicios */}
          <div className="py-2 max-h-80 overflow-y-auto">
            {availableServices.length === 0 ? (
              <div className="px-4 py-8 text-center">
                <Server size={24} className="text-gray-400 mx-auto mb-2" />
                <span className="text-sm text-gray-500">
                  {t('common.serviceSelector.noServices', 'No hay servicios disponibles')}
                </span>
              </div>
            ) : (
              <>
                {/* Sección CTM */}
                {ctmServices.length > 0 && (
                  <>
                    <div className={`px-4 py-2 text-xs font-medium ${
                      darkMode ? 'text-blue-400 bg-blue-900/20' : 'text-blue-600 bg-blue-50'
                    }`}>
                      CTM (Centralized Trust Management)
                    </div>
                    {ctmServices.map((service) => {
                      const isSelected = selectedService?.id === service.id && selectedService?.type === service.type;
                      
                      return (
                        <button
                          key={`ctm-${service.id}`}
                          onClick={() => {
                            handleServiceChange(service);
                            setIsOpen(false);
                          }}
                          className={`w-full flex items-center gap-3 px-4 py-3 transition-all duration-200 hover:bg-opacity-80 ${
                            isSelected
                              ? darkMode
                                ? 'bg-blue-600/20 text-blue-400'
                                : 'bg-blue-50 text-blue-600'
                              : darkMode
                                ? 'text-gray-300 hover:bg-gray-700'
                                : 'text-gray-700 hover:bg-gray-50'
                          }`}
                        >
                          <div className="flex-1 text-left">
                            <span className="font-medium">{service.name}</span>
                            {service.description && (
                              <div className="text-xs opacity-75 mt-0.5">
                                {service.description}
                              </div>
                            )}
                            {isSelected && (
                              <div className="flex items-center gap-1 mt-1">
                                <div className={`w-2 h-2 rounded-full ${
                                  darkMode ? 'bg-blue-400' : 'bg-blue-500'
                                }`}></div>
                                <span className="text-xs opacity-75">
                                  {t('common.serviceSelector.current', 'Actual')}
                                </span>
                              </div>
                            )}
                          </div>
                          {isSelected && (
                            <div className={`w-2 h-2 rounded-full ${
                              darkMode ? 'bg-blue-400' : 'bg-blue-500'
                            } animate-pulse`}></div>
                          )}
                        </button>
                      );
                    })}
                  </>
                )}

                {/* Separador si hay ambos tipos */}
                {ctmServices.length > 0 && hsmServices.length > 0 && (
                  <div className={`mx-4 my-2 border-t ${
                    darkMode ? 'border-gray-600' : 'border-gray-200'
                  }`}></div>
                )}

                {/* Sección HSM */}
                {hsmServices.length > 0 && (
                  <>
                    <div className={`px-4 py-2 text-xs font-medium ${
                      darkMode ? 'text-emerald-400 bg-emerald-900/20' : 'text-emerald-600 bg-emerald-50'
                    }`}>
                      HSM (Hardware Security Module)
                    </div>
                    {hsmServices.map((service) => {
                      const isSelected = selectedService?.id === service.id && selectedService?.type === service.type;
                      
                      return (
                        <button
                          key={`hsm-${service.id}`}
                          onClick={() => {
                            handleServiceChange(service);
                            setIsOpen(false);
                          }}
                          className={`w-full flex items-center gap-3 px-4 py-3 transition-all duration-200 hover:bg-opacity-80 ${
                            isSelected
                              ? darkMode
                                ? 'bg-emerald-600/20 text-emerald-400'
                                : 'bg-emerald-50 text-emerald-600'
                              : darkMode
                                ? 'text-gray-300 hover:bg-gray-700'
                                : 'text-gray-700 hover:bg-gray-50'
                          }`}
                        >
                          <div className="flex-1 text-left">
                            <span className="font-medium">{service.name}</span>
                            {service.description && (
                              <div className="text-xs opacity-75 mt-0.5">
                                {service.description}
                              </div>
                            )}
                            {isSelected && (
                              <div className="flex items-center gap-1 mt-1">
                                <div className={`w-2 h-2 rounded-full ${
                                  darkMode ? 'bg-emerald-400' : 'bg-emerald-500'
                                }`}></div>
                                <span className="text-xs opacity-75">
                                  {t('common.serviceSelector.current', 'Actual')}
                                </span>
                              </div>
                            )}
                          </div>
                          {isSelected && (
                            <div className={`w-2 h-2 rounded-full ${
                              darkMode ? 'bg-emerald-400' : 'bg-emerald-500'
                            } animate-pulse`}></div>
                          )}
                        </button>
                      );
                    })}
                  </>
                )}
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

GlobalServiceSelector.propTypes = {
  darkMode: PropTypes.bool.isRequired,
  className: PropTypes.string,
};

export default GlobalServiceSelector;
