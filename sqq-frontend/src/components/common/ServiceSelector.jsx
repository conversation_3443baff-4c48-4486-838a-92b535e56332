import { useState, useRef, useEffect } from 'react';
import { ChevronDown, Server, AlertCircle } from 'lucide-react';
import PropTypes from 'prop-types';
import { useLanguage, useServices } from '../../hooks';

/**
 * Componente selector de servicios unificado (CTM + HSM)
 */
const ServiceSelector = ({
  darkMode,
  className = "",
  selectedService,
  onServiceChange,
  availableServices = null, // Si es null, carga dinámicamente
  showLoadingState = true
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);
  const { t } = useLanguage();
  const {
    getFormattedServices,
    getUserServices,
    isLoading: servicesLoading,
    error: servicesError
  } = useServices();

  // Estado para servicios dinámicos
  const [dynamicServices, setDynamicServices] = useState([]);
  const [isLoadingServices, setIsLoadingServices] = useState(false);

  // Cargar servicios dinámicamente si no se proporcionan
  useEffect(() => {
    if (!availableServices && showLoadingState) {
      const loadServices = async () => {
        try {
          setIsLoadingServices(true);
          await getUserServices();
          const formatted = getFormattedServices();
          setDynamicServices(formatted);
        } catch (error) {
          console.error('Error loading services:', error);
          setDynamicServices([]);
        } finally {
          setIsLoadingServices(false);
        }
      };

      loadServices();
    }
  }, [availableServices, showLoadingState, getUserServices, getFormattedServices]);

  // Cerrar dropdown al hacer clic fuera
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleServiceChange = (service) => {
    // Pasar el objeto completo del servicio en lugar de solo el nombre
    onServiceChange(service);
    setIsOpen(false);
  };

  // Determinar qué servicios usar
  const servicesToUse = availableServices || dynamicServices;
  const isLoading = isLoadingServices || servicesLoading;

  // Agrupar servicios por tipo
  const ctmServices = servicesToUse.filter(service =>
    service.type === 'CTM' || service.name?.startsWith('CTM')
  );
  const hsmServices = servicesToUse.filter(service =>
    service.type === 'HSM' || service.name?.startsWith('HSM')
  );

  // Determinar si el servicio seleccionado es HSM para cambiar el color del icono
  const selectedServiceName = typeof selectedService === 'string' ? selectedService : selectedService?.name;
  const selectedServiceType = typeof selectedService === 'object' ? selectedService?.type :
    (selectedServiceName?.startsWith('HSM') ? 'HSM' : 'CTM');

  const isHSMSelected = selectedServiceType === 'HSM';
  const iconColor = isHSMSelected
    ? (darkMode ? 'bg-emerald-600 group-hover:bg-emerald-500' : 'bg-emerald-500 group-hover:bg-emerald-600')
    : (darkMode ? 'bg-blue-600 group-hover:bg-blue-500' : 'bg-blue-500 group-hover:bg-blue-600');

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Botón principal */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`group flex items-center gap-3 px-4 py-3 rounded-xl border shadow-sm transition-all duration-300 transform hover:scale-105 hover:shadow-lg ${
          darkMode
            ? 'bg-gray-800 border-gray-600 hover:border-gray-500 text-white'
            : 'bg-white border-gray-200 hover:border-gray-300 text-gray-900'
        }`}
        title="Seleccionar Servicio"
      >
        {/* Icono de servidor */}
        <div className={`p-1.5 rounded-lg transition-all duration-300 ${iconColor}`}>
          <Server size={16} className="text-white" />
        </div>

        {/* Servicio actual */}
        <div className="flex items-center gap-2">
          {isLoading ? (
            <div className="flex items-center gap-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
              <span className="font-light tracking-wide text-sm hidden sm:block">
                {t('common.serviceSelector.loading', 'Cargando...')}
              </span>
            </div>
          ) : servicesError ? (
            <div className="flex items-center gap-2">
              <AlertCircle size={16} className="text-red-500" />
              <span className="font-light tracking-wide text-sm hidden sm:block text-red-500">
                {t('common.serviceSelector.error', 'Error')}
              </span>
            </div>
          ) : (
            <span className="font-light tracking-wide text-sm hidden sm:block">
              {selectedServiceName || t('common.serviceSelector.selectService', 'Seleccionar servicio')}
            </span>
          )}
        </div>

        {/* Icono de flecha */}
        <ChevronDown 
          size={16} 
          className={`transition-transform duration-300 ${isOpen ? 'rotate-180' : ''}`}
        />
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className={`absolute top-full left-0 mt-2 w-48 rounded-xl border shadow-xl z-50 overflow-hidden transition-all duration-300 ${
          darkMode
            ? 'bg-gray-800 border-gray-600'
            : 'bg-white border-gray-200'
        }`}>
          {/* Header del dropdown */}
          <div className={`px-4 py-3 border-b ${
            darkMode ? 'border-gray-600 bg-gray-700' : 'border-gray-200 bg-gray-50'
          }`}>
            <div className="flex items-center gap-2">
              <Server size={16} className={darkMode ? 'text-blue-400' : 'text-blue-500'} />
              <span className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-300">
                {t('common.serviceSelector.title')}
              </span>
            </div>
          </div>

          {/* Lista de servicios */}
          <div className="py-2">
            {isLoading ? (
              <div className="px-4 py-8 text-center">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-current mx-auto mb-2"></div>
                <span className="text-sm text-gray-500">
                  {t('common.serviceSelector.loadingServices', 'Cargando servicios...')}
                </span>
              </div>
            ) : servicesError ? (
              <div className="px-4 py-8 text-center">
                <AlertCircle size={24} className="text-red-500 mx-auto mb-2" />
                <span className="text-sm text-red-500">
                  {t('common.serviceSelector.errorLoading', 'Error al cargar servicios')}
                </span>
              </div>
            ) : servicesToUse.length === 0 ? (
              <div className="px-4 py-8 text-center">
                <Server size={24} className="text-gray-400 mx-auto mb-2" />
                <span className="text-sm text-gray-500">
                  {t('common.serviceSelector.noServices', 'No hay servicios disponibles')}
                </span>
              </div>
            ) : (
              <>
                {/* Sección CTM */}
                {ctmServices.length > 0 && (
                  <>
                    <div className={`px-4 py-2 text-xs font-medium ${
                      darkMode ? 'text-blue-400 bg-blue-900/20' : 'text-blue-600 bg-blue-50'
                    }`}>
                      CTM (Centralized Trust Management)
                    </div>
                    {ctmServices.map((service) => {
                      const isSelected = selectedServiceName === service.name ||
                        (typeof selectedService === 'object' && selectedService?.id === service.id);

                      return (
                        <button
                          key={service.id}
                          onClick={() => handleServiceChange(service)}
                          className={`w-full flex items-center gap-3 px-4 py-3 transition-all duration-200 hover:bg-opacity-80 ${
                            isSelected
                              ? darkMode
                                ? 'bg-blue-600/20 text-blue-400'
                                : 'bg-blue-50 text-blue-600'
                              : darkMode
                                ? 'text-gray-300 hover:bg-gray-700'
                                : 'text-gray-700 hover:bg-gray-50'
                          }`}
                        >
                          {/* Nombre del servicio */}
                          <div className="flex-1 text-left">
                            <span className="font-light tracking-wide">
                              {service.name}
                            </span>
                            {service.description && (
                              <div className="text-xs opacity-75 mt-0.5">
                                {service.description}
                              </div>
                            )}
                            {isSelected && (
                              <div className="flex items-center gap-1 mt-1">
                                <div className={`w-2 h-2 rounded-full ${
                                  darkMode ? 'bg-blue-400' : 'bg-blue-500'
                                }`}></div>
                                <span className="text-xs font-light tracking-wide opacity-75">
                                  {t('common.serviceSelector.current')}
                                </span>
                              </div>
                            )}
                          </div>

                          {/* Indicador de selección */}
                          {isSelected && (
                            <div className={`w-2 h-2 rounded-full ${
                              darkMode ? 'bg-blue-400' : 'bg-blue-500'
                            } animate-pulse`}></div>
                          )}
                        </button>
                      );
                    })}
                  </>
                )}

                {/* Separador si hay ambos tipos */}
                {ctmServices.length > 0 && hsmServices.length > 0 && (
                  <div className={`mx-4 my-2 border-t ${
                    darkMode ? 'border-gray-600' : 'border-gray-200'
                  }`}></div>
                )}

                {/* Sección HSM */}
                {hsmServices.length > 0 && (
                  <>
                    <div className={`px-4 py-2 text-xs font-medium ${
                      darkMode ? 'text-emerald-400 bg-emerald-900/20' : 'text-emerald-600 bg-emerald-50'
                    }`}>
                      HSM (Hardware Security Module)
                    </div>
                    {hsmServices.map((service) => {
                      const isSelected = selectedServiceName === service.name ||
                        (typeof selectedService === 'object' && selectedService?.id === service.id);

                      return (
                        <button
                          key={service.id}
                          onClick={() => handleServiceChange(service)}
                          className={`w-full flex items-center gap-3 px-4 py-3 transition-all duration-200 hover:bg-opacity-80 ${
                            isSelected
                              ? darkMode
                                ? 'bg-emerald-600/20 text-emerald-400'
                                : 'bg-emerald-50 text-emerald-600'
                              : darkMode
                                ? 'text-gray-300 hover:bg-gray-700'
                                : 'text-gray-700 hover:bg-gray-50'
                          }`}
                        >
                          {/* Nombre del servicio */}
                          <div className="flex-1 text-left">
                            <span className="font-light tracking-wide">
                              {service.name}
                            </span>
                            {service.description && (
                              <div className="text-xs opacity-75 mt-0.5">
                                {service.description}
                              </div>
                            )}
                            {isSelected && (
                              <div className="flex items-center gap-1 mt-1">
                                <div className={`w-2 h-2 rounded-full ${
                                  darkMode ? 'bg-emerald-400' : 'bg-emerald-500'
                                }`}></div>
                                <span className="text-xs font-light tracking-wide opacity-75">
                                  {t('common.serviceSelector.current')}
                                </span>
                              </div>
                            )}
                          </div>

                          {/* Indicador de selección */}
                          {isSelected && (
                            <div className={`w-2 h-2 rounded-full ${
                              darkMode ? 'bg-emerald-400' : 'bg-emerald-500'
                            } animate-pulse`}></div>
                          )}
                        </button>
                      );
                    })}
                  </>
                )}
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

ServiceSelector.propTypes = {
  darkMode: PropTypes.bool.isRequired,
  className: PropTypes.string,
  selectedService: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.object
  ]),
  onServiceChange: PropTypes.func.isRequired,
  availableServices: PropTypes.array, // Ahora es opcional
  showLoadingState: PropTypes.bool
};

export default ServiceSelector;
