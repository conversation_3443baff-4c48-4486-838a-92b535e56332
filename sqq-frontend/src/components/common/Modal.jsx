import { useEffect, useRef, useState, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { X } from 'lucide-react';
import PropTypes from 'prop-types';
import gsap from 'gsap';
import '../../styles/scrollbar.css';

/**
 * @fileoverview Componente Modal reutilizable con animaciones GSAP y portal rendering
 * @description Modal moderno con soporte para diferentes tamaños, animaciones suaves,
 * manejo de eventos de teclado y overlay, y renderizado mediante portal para mejor UX.
 * <AUTHOR>
 * @version 1.0.0
 */

/**
 * Componente Modal reutilizable con animaciones y portal rendering
 * @component
 * @param {Object} props - Propiedades del componente
 * @param {boolean} props.isOpen - Controla la visibilidad del modal
 * @param {Function} props.onClose - Función callback para cerrar el modal
 * @param {string} props.title - Título del modal
 * @param {React.ReactNode} props.children - Contenido del modal
 * @param {string} [props.maxWidth="max-w-md"] - Clase CSS para ancho máximo
 * @param {boolean} [props.darkMode=false] - Modo oscuro activado
 * @param {boolean} [props.showCloseButton=true] - Mostrar botón de cerrar
 * @param {string} [props.className=""] - Clases CSS adicionales
 * @returns {JSX.Element|null} Modal renderizado mediante portal o null si está cerrado
 *
 * @example
 * <Modal
 *   isOpen={showModal}
 *   onClose={() => setShowModal(false)}
 *   title="Confirmar acción"
 *   maxWidth="max-w-lg"
 *   darkMode={true}
 * >
 *   <p>¿Estás seguro de realizar esta acción?</p>
 * </Modal>
 */
const Modal = ({
  isOpen,
  onClose,
  title,
  children,
  maxWidth = "max-w-md",
  darkMode = false,
  showCloseButton = true,
  className = ""
}) => {
  const modalRef = useRef(null);
  const overlayRef = useRef(null);
  const [isClosing, setIsClosing] = useState(false);
  // Animaciones GSAP y bloqueo de scroll
  useEffect(() => {
    if (isOpen) {
      // Guardar el scroll actual
      const scrollY = window.scrollY;

      // Bloquear scroll
      document.body.style.overflow = 'hidden';
      document.body.style.position = 'fixed';
      document.body.style.top = `-${scrollY}px`;
      document.body.style.width = '100%';

      // Animación de entrada simple como el proyecto anterior
      if (overlayRef.current && modalRef.current) {
        gsap.fromTo(overlayRef.current,
          { opacity: 0 },
          { opacity: 1, duration: 0.3, ease: "power2.out" }
        );

        gsap.fromTo(modalRef.current,
          {
            opacity: 0,
            scale: 0.9,
            y: 20
          },
          {
            opacity: 1,
            scale: 1,
            y: 0,
            duration: 0.4,
            ease: "power2.out",
            delay: 0.1
          }
        );
      }

      return () => {
        // Restaurar scroll al cerrar
        document.body.style.overflow = '';
        document.body.style.position = '';
        document.body.style.top = '';
        document.body.style.width = '';
        window.scrollTo(0, scrollY);
      };
    }
  }, [isOpen]);

  // Función para cerrar con animación
  const handleClose = useCallback(() => {
    if (isClosing) return; // Prevenir múltiples clicks

    setIsClosing(true);

    // Animación de salida
    if (overlayRef.current && modalRef.current) {
      gsap.to(modalRef.current, {
        opacity: 0,
        scale: 0.9,
        y: 20,
        duration: 0.3,
        ease: "power2.in"
      });

      gsap.to(overlayRef.current, {
        opacity: 0,
        duration: 0.3,
        ease: "power2.in",
        onComplete: () => {
          setIsClosing(false);
          onClose();
        }
      });
    } else {
      // Fallback si no hay refs
      setTimeout(() => {
        setIsClosing(false);
        onClose();
      }, 300);
    }
  }, [isClosing, onClose]);

  // Cerrar modal al presionar Escape con animación
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape' && isOpen && !isClosing) {
        handleClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      return () => document.removeEventListener('keydown', handleEscape);
    }
  }, [isOpen, isClosing, handleClose]);

  if (!isOpen && !isClosing) return null;

  // Manejar click en el overlay (fuera del modal)
  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  return createPortal(
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        width: '100vw',
        height: '100vh',
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '12px',
        zIndex: 999999999
      }}
      ref={overlayRef}
      onClick={handleOverlayClick}
    >
        <div
          ref={modalRef}
          className={`rounded-2xl shadow-2xl border-2 ${maxWidth} w-full max-h-[90vh] sm:max-h-[85vh] flex flex-col ${
            darkMode
              ? 'bg-gray-800 text-white border-gray-600/50'
              : 'bg-white text-gray-900 border-gray-200/50'
          } ${className}`}
          onClick={(e) => e.stopPropagation()}
          style={{
            backdropFilter: 'blur(10px)',
            background: darkMode
              ? 'rgba(31, 41, 55, 0.95)'
              : 'rgba(255, 255, 255, 0.95)'
          }}
        >
          {/* Header fijo */}
          {(title || showCloseButton) && (
            <div className="flex justify-between items-center p-4 sm:p-6 lg:p-8 pb-3 sm:pb-4 border-b border-gray-200 dark:border-gray-600 flex-shrink-0">
              {title && (
                <div className="flex-1 pr-4">
                  {typeof title === 'string' ? (
                    <h3 className="text-lg sm:text-xl font-light tracking-wide text-gray-900 dark:text-white">
                      {title}
                    </h3>
                  ) : (
                    title
                  )}
                </div>
              )}
              {showCloseButton && (
                <button
                  onClick={handleClose}
                  className="p-1.5 sm:p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors flex-shrink-0"
                  aria-label="Cerrar modal"
                >
                  <X size={18} className="text-gray-500 dark:text-gray-400 sm:w-5 sm:h-5" />
                </button>
              )}
            </div>
          )}

          {/* Contenido con scroll elegante */}
          <div className="flex-1 overflow-y-auto px-4 sm:px-6 lg:px-8 py-3 sm:py-4 custom-scrollbar">
            {children}
          </div>
        </div>
    </div>,
    document.body
  );
};

Modal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  title: PropTypes.string,
  children: PropTypes.node.isRequired,
  maxWidth: PropTypes.string,
  darkMode: PropTypes.bool,
  showCloseButton: PropTypes.bool,
  className: PropTypes.string
};

export default Modal;
