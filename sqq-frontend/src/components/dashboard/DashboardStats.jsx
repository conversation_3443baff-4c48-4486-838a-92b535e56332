import PropTypes from 'prop-types';
import { StatCard, MiniStatCard, ErrorAlert } from '../common';

/**
 * Componente para mostrar estadísticas del dashboard
 * Reutilizable entre AdminDashboard y UsuarioDashboard
 */
const DashboardStats = ({ 
  mainStats,
  miniStats,
  isLoading,
  error,
  onClearError,
  className = "",
  onStatClick = null
}) => {
  return (
    <div className={className}>
      {/* Mostrar errores */}
      {error && (
        <ErrorAlert 
          error={error}
          onClose={onClearError}
        />
      )}

      {/* Tarjetas principales de estadísticas con animaciones */}
      {mainStats && mainStats.length > 0 && (
        <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-4 sm:gap-6 mb-6 sm:mb-8">
          {mainStats.map((stat, index) => (
            <StatCard
              key={index}
              icon={stat.icon}
              title={stat.title}
              value={stat.value}
              description={stat.description}
              iconColor={stat.iconColor}
              valueColor={stat.valueColor}
              percentage={stat.percentage}
              delay={stat.delay}
              isLoading={isLoading}
              clickable={stat.clickable || false}
              onClick={stat.clickable && onStatClick ? () => onStatClick(stat) : undefined}
            />
          ))}
        </div>
      )}

      {/* Tarjetas adicionales de estadísticas */}
      {miniStats && miniStats.length > 0 && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 mt-4 sm:mt-6">
          {miniStats.map((stat, index) => (
            <MiniStatCard
              key={index}
              value={stat.value}
              label={stat.label}
              valueColor={stat.valueColor}
              isLoading={isLoading}
            />
          ))}
        </div>
      )}
    </div>
  );
};

DashboardStats.propTypes = {
  mainStats: PropTypes.arrayOf(PropTypes.shape({
    icon: PropTypes.elementType.isRequired,
    title: PropTypes.string.isRequired,
    value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    description: PropTypes.string.isRequired,
    iconColor: PropTypes.string,
    valueColor: PropTypes.string,
    clickable: PropTypes.bool
  })),
  miniStats: PropTypes.arrayOf(PropTypes.shape({
    value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    label: PropTypes.string.isRequired,
    valueColor: PropTypes.string
  })),
  isLoading: PropTypes.bool,
  error: PropTypes.string,
  onClearError: PropTypes.func,
  className: PropTypes.string,
  onStatClick: PropTypes.func
};

export default DashboardStats;
