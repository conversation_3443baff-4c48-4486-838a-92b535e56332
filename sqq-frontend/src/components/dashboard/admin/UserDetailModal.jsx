import { User, Mail, Building, Shield, Server } from 'lucide-react';
import PropTypes from 'prop-types';
import { Modal } from '../../common';
import { useLanguage } from '../../../hooks';

/**
 * Modal hermoso para mostrar detalles completos del usuario
 * Diseño moderno hermoso minimalista y con consistencia
 */
const UserDetailModal = ({ isOpen, onClose, user, darkMode }) => {
  const { t } = useLanguage();

  if (!user) return null;

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={
        <div className="flex items-center gap-2 sm:gap-3">
          <User size={20} className="text-blue-500 sm:w-6 sm:h-6" />
          <div>
            <h3 className="text-lg sm:text-2xl font-light tracking-wide leading-relaxed text-gray-900 dark:text-white">
              {t('admin.users.detail.title')}
            </h3>
            <p className="text-xs sm:text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
              {t('admin.users.detail.subtitle')}
            </p>
          </div>
        </div>
      }
      maxWidth="max-w-4xl"
      darkMode={darkMode}
    >
      <div className="space-y-3 sm:space-y-4">
        {/* Información Personal */}
        <div className={`p-3 sm:p-4 rounded-xl border shadow-sm ${
          darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
        }`}>
          <h4 className="text-sm sm:text-base font-light tracking-wide mb-3 flex items-center gap-2 text-gray-900 dark:text-white">
            <User size={16} className="text-orange-500 sm:w-5 sm:h-5" />
            {t('admin.users.detail.personalInfo')}
          </h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4">
            <div>
              <label className="block text-xs sm:text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-1">
                {t('admin.users.detail.fullName')}
              </label>
              <div className={`px-3 sm:px-4 py-2.5 sm:py-3 border rounded-lg font-light tracking-wide text-sm sm:text-base ${
                darkMode
                  ? 'border-gray-600 bg-gray-700 text-white'
                  : 'border-gray-300 bg-gray-50 text-gray-900'
              }`}>
                {user.firstName} {user.lastName}
              </div>
            </div>

            <div>
              <label className="block text-xs sm:text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-1">
                {t('admin.users.detail.email')}
              </label>
              <div className={`px-3 sm:px-4 py-2.5 sm:py-3 border rounded-lg font-light tracking-wide text-sm sm:text-base ${
                darkMode
                  ? 'border-gray-600 bg-gray-700 text-white'
                  : 'border-gray-300 bg-gray-50 text-gray-900'
              }`}>
                {user.email}
              </div>
            </div>

            <div>
              <label className="block text-xs sm:text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-1">
                {t('admin.users.detail.company')}
              </label>
              <div className={`px-3 sm:px-4 py-2.5 sm:py-3 border rounded-lg font-light tracking-wide text-sm sm:text-base ${
                darkMode
                  ? 'border-gray-600 bg-gray-700 text-white'
                  : 'border-gray-300 bg-gray-50 text-gray-900'
              }`}>
                {user.company || t('admin.users.detail.notSpecified')}
              </div>
            </div>

            <div>
              <label className="block text-xs sm:text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-1">
                {t('admin.users.detail.role')}
              </label>
              <div className={`px-3 sm:px-4 py-2.5 sm:py-3 border rounded-lg font-light tracking-wide text-sm sm:text-base ${
                darkMode
                  ? 'border-gray-600 bg-gray-700 text-white'
                  : 'border-gray-300 bg-gray-50 text-gray-900'
              }`}>
                {user.role === 'ADMIN' ? t('admin.users.detail.administrator') : t('admin.users.detail.user')}
              </div>
            </div>

            <div>
              <label className="block text-xs sm:text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-1">
                {t('admin.users.detail.status')}
              </label>
              <div className={`px-3 sm:px-4 py-2.5 sm:py-3 border rounded-lg font-light tracking-wide text-sm sm:text-base ${
                user.isActive
                  ? darkMode
                    ? 'border-green-600 bg-green-900/20 text-green-400'
                    : 'border-green-300 bg-green-50 text-green-700'
                  : darkMode
                    ? 'border-red-600 bg-red-900/20 text-red-400'
                    : 'border-red-300 bg-red-50 text-red-700'
              }`}>
                {user.isActive ? t('admin.users.detail.active') : t('admin.users.detail.inactive')}
              </div>
            </div>
          </div>
        </div>

        {/* CTMs Asignados */}
        <div className={`p-4 rounded-xl border shadow-sm ${
          darkMode ? 'bg-blue-900/10 border-blue-700' : 'bg-blue-50 border-blue-200'
        }`}>
          <h4 className="text-base font-light tracking-wide mb-3 flex items-center gap-2 text-gray-900 dark:text-white">
            <Server size={18} className="text-blue-500" />
            {t('admin.users.detail.assignedCTMs')}
          </h4>

          <div className="space-y-3">
            {user.ctms && user.ctms.length > 0 ? (
              user.ctms.map((ctm) => (
                <div
                  key={ctm.id}
                  className={`p-3 rounded-lg border ${
                    darkMode
                      ? 'bg-gray-800 border-gray-600'
                      : 'bg-white border-gray-300'
                  }`}
                >
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <div>
                      <span className={`font-medium ${
                        darkMode ? 'text-white' : 'text-gray-900'
                      }`}>
                        {ctm.name}
                      </span>
                      {ctm.description && (
                        <p className={`text-sm ${
                          darkMode ? 'text-gray-400' : 'text-gray-600'
                        }`}>
                          {ctm.description}
                        </p>
                      )}
                      <p className={`text-xs ${
                        darkMode ? 'text-gray-500' : 'text-gray-500'
                      }`}>
                        {ctm.ipAddress}
                      </p>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <p className={`text-center py-4 ${
                darkMode ? 'text-gray-400' : 'text-gray-600'
              }`}>
                {t('admin.users.detail.noCTMsAssigned')}
              </p>
            )}
          </div>
        </div>

        {/* HSMs Asignados */}
        <div className={`p-4 rounded-xl border shadow-sm ${
          darkMode ? 'bg-emerald-900/10 border-emerald-700' : 'bg-emerald-50 border-emerald-200'
        }`}>
          <h4 className="text-base font-light tracking-wide mb-3 flex items-center gap-2 text-gray-900 dark:text-white">
            <Shield size={18} className="text-emerald-500" />
            {t('admin.users.detail.assignedHSMs')}
          </h4>

          <div className="space-y-3">
            {user.hsms && user.hsms.length > 0 ? (
              user.hsms.map((hsm) => (
                <div
                  key={hsm.id}
                  className={`p-3 rounded-lg border ${
                    darkMode
                      ? 'bg-gray-800 border-gray-600'
                      : 'bg-white border-gray-300'
                  }`}
                >
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                    <div>
                      <span className={`font-medium ${
                        darkMode ? 'text-white' : 'text-gray-900'
                      }`}>
                        {hsm.name}
                      </span>
                      {hsm.description && (
                        <p className={`text-sm ${
                          darkMode ? 'text-gray-400' : 'text-gray-600'
                        }`}>
                          {hsm.description}
                        </p>
                      )}
                      <p className={`text-xs ${
                        darkMode ? 'text-gray-500' : 'text-gray-500'
                      }`}>
                        {hsm.url}
                      </p>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <p className={`text-center py-4 ${
                darkMode ? 'text-gray-400' : 'text-gray-600'
              }`}>
                {t('admin.users.detail.noHSMsAssigned')}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="flex justify-center mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
        <button
          onClick={onClose}
          className="px-8 py-3 rounded-xl bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-light tracking-wide shadow-sm hover:shadow-md transform hover:scale-105 transition-all duration-300"
        >
          {t('common.close')}
        </button>
      </div>
    </Modal>
  );
};

UserDetailModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  user: PropTypes.object,
  darkMode: PropTypes.bool.isRequired
};

export default UserDetailModal;
