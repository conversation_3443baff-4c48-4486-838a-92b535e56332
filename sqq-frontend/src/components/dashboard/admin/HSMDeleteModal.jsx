import { useState } from 'react';
import { Server, AlertTriangle, Trash2 } from 'lucide-react';
import PropTypes from 'prop-types';
import { useLanguage } from '../../../hooks';
import { Modal } from '../../common';

const HSMDeleteModal = ({ isOpen, onClose, onConfirm, hsm, darkMode = false }) => {
  const { t } = useLanguage();
  const [isDeleting, setIsDeleting] = useState(false);

  const handleConfirm = async () => {
    setIsDeleting(true);
    try {
      await onConfirm();
    } catch (error) {
      console.error('Error deleting HSM:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-lg bg-red-100 dark:bg-red-900/30">
            <AlertTriangle size={24} className="text-red-600 dark:text-red-400" />
          </div>
          <div>
            <h3 className="text-2xl font-light tracking-wide leading-relaxed text-gray-900 dark:text-white">
              {t('admin.hsms.deleteModal.title')}
            </h3>
            <p className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
              {t('admin.hsms.deleteModal.subtitle')}
            </p>
          </div>
        </div>
      }
      maxWidth="max-w-md"
      darkMode={darkMode}
    >
      <div className="space-y-6">
        {/* Mensaje de advertencia */}
        <div className={`p-4 rounded-xl border-2 mt-2 flex items-center gap-3 ${
          darkMode ? 'bg-red-900/20 border-red-700' : 'bg-red-50 border-red-200'
        }`}> 
          <AlertTriangle size={22} className="text-red-600 dark:text-red-400" />
          <div>
            <h5 className="font-medium text-red-800 dark:text-red-300 mb-1">
              {t('admin.hsms.deleteModal.warning')}
            </h5>
            <p className="text-sm text-red-700 dark:text-red-400 font-light tracking-wide">
              {t('admin.hsms.deleteModal.warningText')}
            </p>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-center gap-3 pt-6 border-t border-gray-200 dark:border-gray-600">
          <button
            type="button"
            onClick={onClose}
            disabled={isDeleting}
            className={`px-8 py-3 rounded-xl font-light tracking-wide transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl bg-gray-100 hover:bg-gray-200 text-gray-700 border border-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-300 dark:border-gray-600 ${isDeleting ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            {t('common.cancel')}
          </button>
          <button
            type="button"
            onClick={handleConfirm}
            disabled={isDeleting}
            className={`px-8 py-3 rounded-xl font-light tracking-wide transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white flex items-center gap-2 border border-red-600 ${isDeleting ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            <Trash2 size={18} />
            {isDeleting ? t('common.loading') : t('admin.hsms.deleteModal.confirm')}
          </button>
        </div>
      </div>
    </Modal>
  );
};

HSMDeleteModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onConfirm: PropTypes.func.isRequired,
  hsm: PropTypes.object.isRequired,
  darkMode: PropTypes.bool
};

export default HSMDeleteModal;
