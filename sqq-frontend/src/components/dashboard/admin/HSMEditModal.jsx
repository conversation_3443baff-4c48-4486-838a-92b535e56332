import { useState, useEffect, useRef } from 'react';
import { Server, Save, ChevronDown, Shield } from 'lucide-react';
import PropTypes from 'prop-types';
import { useLanguage } from '../../../hooks';
import { Modal, FormField } from '../../common';
import HSMConfigurationSection from './HSMConfigurationSection';

/**
 * Modal para editar un HSM existente
 * Diseño simplificado con campos de configuración HSM + nombre y estado
 */
const HSMEditModal = ({ isOpen, onClose, onSubmit, hsm, darkMode = false }) => {
  const { t } = useLanguage();
  const [formData, setFormData] = useState({
    name: '',
    url: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Cargar datos del HSM al abrir
  useEffect(() => {
    if (hsm) {
      setFormData({
        name: hsm.name || '',
        url: hsm.url || ''
      });
    }
  }, [hsm]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Enviamos todos los campos: nombre y URL
      await onSubmit({
        name: formData.name,
        url: formData.url
      });
    } catch (error) {
      console.error('Error updating HSM:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleStatusSelect = (statusValue) => {
    handleChange('status', statusValue);
    setIsStatusDropdownOpen(false);
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={
        <div className="flex items-center gap-3">
          <Server size={24} className="text-emerald-500" />
          <div>
            <h3 className="text-2xl font-light tracking-wide leading-relaxed text-gray-900 dark:text-white">
              Hardware Security Module
            </h3>
            <p className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
              {t('admin.hsms.editModal.subtitle')}
            </p>
          </div>
        </div>
      }
      maxWidth="max-w-2xl"
      darkMode={darkMode}
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Información básica del HSM */}
        <div className={`p-4 rounded-xl border shadow-sm ${
          darkMode ? 'bg-emerald-900/10 border-emerald-700' : 'bg-emerald-50 border-emerald-200'
        }`}>
          <h4 className="text-lg font-light tracking-wide text-emerald-700 dark:text-emerald-300 mb-4 flex items-center gap-2">
            <Server size={20} className="text-emerald-600" />
            {t('admin.hsms.editModal.configTitle')}
          </h4>

          <div className="space-y-4">
            <FormField
              label={t('admin.hsms.form.name')}
              type="text"
              value={formData.name}
              onChange={(e) => handleChange('name', e.target.value)}
              placeholder={t('admin.hsms.form.namePlaceholder')}
              required
              darkMode={darkMode}
            />



            <FormField
              label={t('admin.hsms.form.url')}
              type="text"
              value={formData.url}
              onChange={(e) => handleChange('url', e.target.value)}
              placeholder="http://tomcatlinux:8080"
              required
              darkMode={darkMode}
            />
          </div>
        </div>



        {/* Footer - estilo consistente con editar CTM */}
        <div className="flex justify-center gap-3 mt-2 pt-4 border-t border-gray-200 dark:border-gray-600">
          <button
            type="button"
            onClick={onClose}
            disabled={isSubmitting}
            className={`px-8 py-3 rounded-xl font-light tracking-wide transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl ${
              darkMode
                ? 'bg-gray-700 hover:bg-gray-600 text-white border border-gray-600'
                : 'bg-gray-100 hover:bg-gray-200 text-gray-700 border border-gray-300'
            } ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            {t('common.cancel')}
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className={`px-8 py-3 rounded-xl font-light tracking-wide transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 text-white flex items-center justify-center gap-2 ${
              isSubmitting ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            {isSubmitting ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                {t('common.loading')}
              </>
            ) : (
              <>
                <Save size={16} />
                {t('admin.hsms.editModal.submit')}
              </>
            )}
          </button>
        </div>
      </form>
    </Modal>
  );
};

HSMEditModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onSubmit: PropTypes.func.isRequired,
  hsm: PropTypes.object.isRequired,
  darkMode: PropTypes.bool
};

export default HSMEditModal;
