import { useState, useRef } from 'react';
import { Server, Plus, Search, Filter, MoreVertical, Edit, Trash2, Eye, ChevronDown } from 'lucide-react';
import PropTypes from 'prop-types';
import { useLanguage } from '../../../hooks';
import { <PERSON><PERSON>, <PERSON>, FormField, LoadingSpinner, <PERSON>rror<PERSON><PERSON>t } from '../../common';
import HSMCreateModal from './HSMCreateModal';
import HSMEditModal from './HSMEditModal';
import HSMDeleteModal from './HSMDeleteModal';
import HSMViewModal from './HSMViewModal';

/**
 * Componente para gestionar HSMs del sistema
 * Basado en CTMManagement pero adaptado para HSMs
 */
const HSMManagement = ({ 
  hsms = [], 
  isLoading = false, 
  error = null, 
  onClearError, 
  onUpdateHSM, 
  onCreateHSM, 
  onDeleteHSM,
  darkMode = false 
}) => {
  const { t } = useLanguage();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedHSM, setSelectedHSM] = useState(null);
  const [showViewModal, setShowViewModal] = useState(false);
  const [viewHSM, setViewHSM] = useState(null);

  // Estados para dropdown custom
  const [isStatusDropdownOpen, setIsStatusDropdownOpen] = useState(false);
  const statusDropdownRef = useRef(null);

  // Filtrar HSMs basado en búsqueda y filtros
  const filteredHSMs = (Array.isArray(hsms) ? hsms : []).filter(hsm => {
    const matchesSearch = hsm.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         hsm.ipAddress?.includes(searchTerm) ||
                         (hsm.username && hsm.username.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesFilter = filterStatus === 'all' || hsm.status === filterStatus;

    return matchesSearch && matchesFilter;
  });

  const handleCreateHSM = async (hsmData) => {
    try {
      await onCreateHSM(hsmData);
      setShowCreateModal(false);
      setSelectedHSM(null); // Clear selected HSM after creation
    } catch (error) {
      console.error('Error creating HSM:', error);
    }
  };

  const handleUpdateHSM = async (hsmId, hsmData) => {
    try {
      await onUpdateHSM(hsmId, hsmData);
      setShowEditModal(false);
      setSelectedHSM(null);
    } catch (error) {
      console.error('Error updating HSM:', error);
    }
  };

  const handleDeleteHSM = async (hsmId) => {
    try {
      await onDeleteHSM(hsmId);
      setShowDeleteModal(false);
      setSelectedHSM(null);
    } catch (error) {
      console.error('Error deleting HSM:', error);
    }
  };

  const openEditModal = (hsm) => {
    setSelectedHSM(hsm);
    setShowEditModal(true);
  };

  const openDeleteModal = (hsm) => {
    setSelectedHSM(hsm);
    setShowDeleteModal(true);
  };

  // Handlers para los botones de acción
  const editHsm = (id) => openEditModal(hsms.find(h => h.id === id));
  const viewHsm = (id) => {
    setViewHSM(hsms.find(h => h.id === id));
    setShowViewModal(true);
  };
  const confirmDeleteHsm = (id) => openDeleteModal(hsms.find(h => h.id === id));

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'inactive':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'active':
        return t('admin.hsms.status.active');
      case 'inactive':
        return t('admin.hsms.status.inactive');
      default:
        return status;
    }
  };

  if (isLoading) {
    return <LoadingSpinner darkMode={darkMode} />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-light tracking-wide leading-relaxed text-gray-900 dark:text-white">
            {t('admin.hsms.title')}
          </h1>
          <p className="text-gray-600 dark:text-gray-400 font-light tracking-wide">
            {t('admin.hsms.subtitle')}
          </p>
        </div>
        <button
          onClick={() => setShowCreateModal(true)}
          className="flex items-center gap-2 sm:gap-3 px-4 sm:px-6 py-2.5 sm:py-3 rounded-xl font-light tracking-wide shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 text-white text-sm sm:text-base"
        >
          <Plus size={16} />
          <span>{t('admin.hsms.create')}</span>
        </button>
      </div>

      {/* Error Alert */}
      {error && (
        <ErrorAlert
          message={error}
          onClose={onClearError}
          darkMode={darkMode}
        />
      )}

      {/* Filtros y búsqueda */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <input
            type="text"
            placeholder={t('admin.hsms.searchPlaceholder')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className={`w-full pl-10 pr-4 py-2 rounded-lg border transition-colors ${
              darkMode
                ? 'bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:border-emerald-500'
                : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:border-emerald-500'
            }`}
          />
        </div>
        {/* Filtro por estado - Custom Dropdown */}
        <div className="relative" ref={statusDropdownRef}>
          <button
            type="button"
            onClick={() => setIsStatusDropdownOpen(!isStatusDropdownOpen)}
            className={`w-full px-4 py-2 border rounded-lg font-light tracking-wide focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all duration-200 flex items-center justify-between ${
              darkMode
                ? 'border-gray-600 bg-gray-700 text-white hover:bg-gray-600'
                : 'border-gray-300 bg-white text-gray-900 hover:bg-gray-50'
            } cursor-pointer`}
          >
            <span>
              {filterStatus === 'all'
                ? t('admin.hsms.filters.all')
                : filterStatus === 'active'
                ? t('admin.hsms.filters.active')
                : t('admin.hsms.filters.inactive')
              }
            </span>
            <ChevronDown
              size={16}
              className={`transition-transform duration-200 ${isStatusDropdownOpen ? 'rotate-180' : ''} ${
                darkMode ? 'text-gray-400' : 'text-gray-500'
              }`}
            />
          </button>

          {isStatusDropdownOpen && (
            <div className={`absolute top-full left-0 right-0 mt-1 border rounded-lg shadow-lg z-50 overflow-hidden ${
              darkMode
                ? 'border-gray-600 bg-gray-700'
                : 'border-gray-300 bg-white'
            }`}>
              <button
                type="button"
                onClick={() => {
                  setFilterStatus('all');
                  setIsStatusDropdownOpen(false);
                }}
                className={`w-full text-left px-4 py-3 font-light tracking-wide transition-all duration-200 ${
                  filterStatus === 'all'
                    ? darkMode
                      ? 'bg-emerald-600/20 text-emerald-400 border-l-4 border-emerald-500'
                      : 'bg-emerald-50 text-emerald-600 border-l-4 border-emerald-500'
                    : darkMode
                      ? 'text-gray-300 hover:bg-gray-600'
                      : 'text-gray-700 hover:bg-gray-50'
                }`}
              >
                {t('admin.hsms.filters.all')}
              </button>
              <button
                type="button"
                onClick={() => {
                  setFilterStatus('active');
                  setIsStatusDropdownOpen(false);
                }}
                                 className={`w-full text-left px-4 py-3 font-light tracking-wide transition-all duration-200 ${
                   filterStatus === 'active'
                     ? darkMode
                       ? 'bg-emerald-600/20 text-emerald-400 border-l-4 border-emerald-500'
                       : 'bg-emerald-50 text-emerald-600 border-l-4 border-emerald-500'
                     : darkMode
                       ? 'text-gray-300 hover:bg-gray-600'
                       : 'text-gray-700 hover:bg-gray-50'
                 }`}
              >
                {t('admin.hsms.filters.active')}
              </button>
              <button
                type="button"
                onClick={() => {
                  setFilterStatus('inactive');
                  setIsStatusDropdownOpen(false);
                }}
                                 className={`w-full text-left px-4 py-3 font-light tracking-wide transition-all duration-200 ${
                   filterStatus === 'inactive'
                     ? darkMode
                       ? 'bg-emerald-600/20 text-emerald-400 border-l-4 border-emerald-500'
                       : 'bg-emerald-50 text-emerald-600 border-l-4 border-emerald-500'
                     : darkMode
                       ? 'text-gray-300 hover:bg-gray-600'
                       : 'text-gray-700 hover:bg-gray-50'
                 }`}
              >
                {t('admin.hsms.filters.inactive')}
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Lista de HSMs */}
      <div className={`rounded-xl border ${
        darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
      }`}>
        <div className="p-6">
          <h2 className="text-xl font-light tracking-wide text-gray-900 dark:text-white mb-4">
            {t('admin.hsms.list.title')} ({filteredHSMs.length})
          </h2>
          
          {filteredHSMs.length === 0 ? (
            <div className="text-center py-8">
              <Server size={48} className="mx-auto text-gray-400 mb-4" />
              <p className="text-gray-500 dark:text-gray-400 font-light tracking-wide">
                {t('admin.hsms.list.empty')}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredHSMs.map((hsm) => (
                <div
                  key={hsm.id}
                  className={`p-4 rounded-lg border transition-colors ${
                    darkMode
                      ? 'bg-gray-700 border-gray-600 hover:bg-gray-600'
                      : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className={`p-2 rounded-lg ${
                        darkMode ? 'bg-emerald-900/30' : 'bg-emerald-100'
                      }`}>
                        <Server size={20} className="text-emerald-600 dark:text-emerald-400" />
                      </div>
                      <div>
                        <h3 className="font-light tracking-wide text-gray-900 dark:text-white">
                          {hsm.name}
                        </h3>
                        <div className="flex items-center gap-4 mt-2">
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            {hsm.ipAddress}:{hsm.port}
                          </span>
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            {hsm.username || 'N/A'}
                          </span>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(hsm.status)}`}>
                            {getStatusText(hsm.status)}
                          </span>
                        </div>
                      </div>
                    </div>
                    {/* Botones de acción alineados a la derecha, igual que CTM */}
                    <div className="flex flex-row gap-3">
                      <button
                        onClick={() => editHsm(hsm.id)}
                        className="p-2 border rounded-lg transition-all duration-200 transform border-gray-200 bg-gray-50 text-emerald-500 hover:bg-emerald-50 hover:border-emerald-300 hover:text-emerald-600 hover:scale-110 dark:border-gray-600 dark:bg-gray-700 dark:text-emerald-400 dark:hover:bg-emerald-900/20 dark:hover:border-emerald-500"
                        title={t('admin.hsms.editHsm')}
                        aria-label={t('admin.hsms.editHsm')}
                      >
                        <Edit size={18} />
                      </button>
                      <button
                        onClick={() => viewHsm(hsm.id)}
                        className="p-2 border rounded-lg transition-all duration-200 transform border-gray-200 bg-gray-50 text-green-500 hover:bg-green-50 hover:border-green-300 hover:text-green-600 hover:scale-110 dark:border-gray-600 dark:bg-gray-700 dark:text-green-400 dark:hover:bg-green-900/20 dark:hover:border-green-500"
                        title={t('admin.hsms.viewHsm')}
                        aria-label={t('admin.hsms.viewHsm')}
                      >
                        <Eye size={18} />
                      </button>
                      <button
                        onClick={() => confirmDeleteHsm(hsm.id)}
                        className="p-2 border rounded-lg transition-all duration-200 transform border-gray-200 bg-gray-50 text-red-500 hover:bg-red-50 hover:border-red-300 hover:text-red-600 hover:scale-110 dark:border-gray-600 dark:bg-gray-700 dark:text-red-400 dark:hover:bg-red-900/20 dark:hover:border-red-500"
                        title={t('admin.hsms.deleteHsm')}
                        aria-label={t('admin.hsms.deleteHsm')}
                      >
                        <Trash2 size={18} />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Modal de Crear HSM */}
      {showCreateModal && (
        <HSMCreateModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onSubmit={handleCreateHSM}
          darkMode={darkMode}
        />
      )}

      {/* Modal de Editar HSM */}
      {showEditModal && selectedHSM && (
        <HSMEditModal
          isOpen={showEditModal}
          onClose={() => setShowEditModal(false)}
          onSubmit={(data) => handleUpdateHSM(selectedHSM.id, data)}
          hsm={selectedHSM}
          darkMode={darkMode}
        />
      )}

      {/* Modal de Eliminar HSM */}
      {showDeleteModal && selectedHSM && (
        <HSMDeleteModal
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          onConfirm={() => handleDeleteHSM(selectedHSM.id)}
          hsm={selectedHSM}
          darkMode={darkMode}
        />
      )}

      {/* Modal de ver HSM */}
      {showViewModal && viewHSM && (
        <HSMViewModal
          isOpen={showViewModal}
          onClose={() => setShowViewModal(false)}
          hsm={viewHSM}
          darkMode={darkMode}
        />
      )}
    </div>
  );
};

HSMManagement.propTypes = {
  hsms: PropTypes.array,
  isLoading: PropTypes.bool,
  error: PropTypes.string,
  onClearError: PropTypes.func.isRequired,
  onUpdateHSM: PropTypes.func.isRequired,
  onCreateHSM: PropTypes.func.isRequired,
  onDeleteHSM: PropTypes.func.isRequired,
  darkMode: PropTypes.bool
};

export default HSMManagement;
