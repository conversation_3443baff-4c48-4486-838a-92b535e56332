import { useState } from 'react';
import { Database, Eye, EyeOff } from 'lucide-react';
import PropTypes from 'prop-types';
import { FormField } from '../../common';
import { useLanguage } from '../../../hooks';

/**
 * Componente reutilizable para la configuración de CTM
 */
const CTMConfigurationSection = ({ 
  config, 
  onConfigChange, 
  darkMode, 
  showTitle = true,
  required = false 
}) => {
  const { t } = useLanguage();
  const [showPassword, setShowPassword] = useState(false);

  const handleChange = (field, value) => {
    onConfigChange(field, value);
  };

  return (
    <div className="space-y-4">
      {showTitle && (
        <h5 className="font-light tracking-wide text-orange-600 dark:text-orange-400 flex items-center gap-2">
          <Database size={18} className="text-orange-500" />
          {t('admin.ctms.connection.title')}
        </h5>
      )}

      <div className={`p-4 rounded-lg border ${
        darkMode ? 'bg-gray-800 border-gray-600' : 'bg-white border-gray-200'
      }`}>
        <div className="space-y-4">
          <FormField
            label={t('admin.ctms.connection.ipAddress')}
            type="text"
            value={config.ipAddress}
            onChange={(e) => handleChange('ipAddress', e.target.value)}
            placeholder="https://ctm.example.com:443"
            required={required}
            darkMode={darkMode}
          />

          <FormField
            label={t('admin.ctms.connection.username')}
            type="text"
            value={config.username}
            onChange={(e) => handleChange('username', e.target.value)}
            required={required}
            darkMode={darkMode}
          />

          <div className="relative">
            <FormField
              label={t('admin.ctms.connection.password')}
              type={showPassword ? "text" : "password"}
              value={config.password}
              onChange={(e) => handleChange('password', e.target.value)}
              required={required}
              darkMode={darkMode}
              className="pr-10"
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-8 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
            </button>
          </div>

          <FormField
            label={t('admin.ctms.connection.domain')}
            type="text"
            value={config.domain}
            onChange={(e) => handleChange('domain', e.target.value)}
            placeholder="root"
            required={required}
            darkMode={darkMode}
          />
        </div>
      </div>
    </div>
  );
};

CTMConfigurationSection.propTypes = {
  config: PropTypes.shape({
    ipAddress: PropTypes.string,
    username: PropTypes.string,
    password: PropTypes.string,
    domain: PropTypes.string
  }).isRequired,
  onConfigChange: PropTypes.func.isRequired,
  darkMode: PropTypes.bool.isRequired,
  showTitle: PropTypes.bool,
  required: PropTypes.bool
};

export default CTMConfigurationSection;
