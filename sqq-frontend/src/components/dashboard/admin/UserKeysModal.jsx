import { useState, useMemo } from 'react';
import { Key, Eye, Calendar, Shield, AlertCircle, ChevronLeft, ChevronRight, Search, X } from 'lucide-react';
import PropTypes from 'prop-types';
import { Modal, LoadingSpinner } from '../../common';
import KeyDetailModal from './KeyDetailModal';
import { useLanguage } from '../../../hooks/useLanguage';

/**
 * Modal para mostrar las llaves de un usuario específico
 */
const UserKeysModal = ({
  isOpen,
  onClose,
  user,
  userKeys,
  isLoading,
  darkMode
}) => {
  const { t, currentLanguage } = useLanguage();
  const [selectedKey, setSelectedKey] = useState(null);
  const [showKeyDetail, setShowKeyDetail] = useState(false);
  const [hideKeysModal, setHideKeysModal] = useState(false); // Para ocultar temporalmente
  const [currentPage, setCurrentPage] = useState(1);
  const [isAnimating, setIsAnimating] = useState(false); // Estado para animaciones
  const [animationDirection, setAnimationDirection] = useState(''); // Dirección de la animación
  const [searchTerm, setSearchTerm] = useState(''); // Estado para búsqueda
  const keysPerPage = 5; // Mostrar 5 llaves por página

  const handleViewKeyDetail = (key) => {
    setSelectedKey(key);
    // Ocultar este modal temporalmente y abrir el de detalle
    setHideKeysModal(true); // Oculta el modal de llaves sin cerrar
    setTimeout(() => {
      setShowKeyDetail(true); // Abre el modal de detalle
    }, 150);
  };

  const handleCloseKeyDetail = () => {
    setShowKeyDetail(false);
    setSelectedKey(null);
    // Volver a mostrar el modal de llaves
    setTimeout(() => {
      setHideKeysModal(false); // Vuelve a mostrar el modal de llaves
    }, 150);
  };

  // Reset cuando el modal se cierre completamente
  const handleModalClose = () => {
    setHideKeysModal(false);
    setSelectedKey(null);
    setShowKeyDetail(false);
    setCurrentPage(1); // Resetear a la primera página
    setIsAnimating(false);
    setAnimationDirection('');
    setSearchTerm(''); // Limpiar búsqueda
    onClose();
  };

  // Filtrar llaves basado en el término de búsqueda
  const filteredKeys = useMemo(() => {
    if (!userKeys) return [];
    if (!searchTerm.trim()) return userKeys;

    const searchLower = searchTerm.toLowerCase();
    return userKeys.filter(key => 
      key.name?.toLowerCase().includes(searchLower) ||
      key.type?.toLowerCase().includes(searchLower) ||
      key.algorithm?.toLowerCase().includes(searchLower) ||
      key.status?.toLowerCase().includes(searchLower) ||
      key.ctmKeyId?.toLowerCase().includes(searchLower) ||
      key.id?.toLowerCase().includes(searchLower)
    );
  }, [userKeys, searchTerm]);

  // Lógica de paginación con llaves filtradas
  const paginatedKeys = useMemo(() => {
    if (!filteredKeys) return [];
    const startIndex = (currentPage - 1) * keysPerPage;
    const endIndex = startIndex + keysPerPage;
    return filteredKeys.slice(startIndex, endIndex);
  }, [filteredKeys, currentPage, keysPerPage]);

  const totalPages = Math.ceil((filteredKeys?.length || 0) / keysPerPage);

  // Resetear página cuando cambie la búsqueda
  const handleSearchChange = (value) => {
    setSearchTerm(value);
    setCurrentPage(1); // Resetear a la primera página
  };

  const handleClearSearch = () => {
    setSearchTerm('');
    setCurrentPage(1);
  };

  // Funciones de paginación con animación (igual que el sistema de llaves)
  const handlePageChange = (newPage, direction) => {
    if (isAnimating || newPage === currentPage || newPage < 1 || newPage > totalPages) return;

    setIsAnimating(true);
    setAnimationDirection(direction);

    // Cambiar página inmediatamente y luego quitar animación
    setCurrentPage(newPage);

    setTimeout(() => {
      setIsAnimating(false);
      setAnimationDirection('');
    }, 500);
  };

  const handlePreviousPage = () => {
    if (currentPage > 1) {
      handlePageChange(currentPage - 1, 'prev');
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      handlePageChange(currentPage + 1, 'next');
    }
  };

  const getStatusColor = (key) => {
    if (key.status === 'UPLOADED_TO_CTM' || key.uploadedToCtm === true) {
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
    }
    if (key.status === 'FAILED' || key.isSuccessful === false) {
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
    }
    return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
  };

  const getStatusText = (key) => {
    if (key.status === 'UPLOADED_TO_CTM') return t('admin.users.keys.statusActive');
    if (key.status === 'FAILED') return t('admin.users.keys.statusFailed');
    return key.status || t('admin.users.keys.statusPending');
  };

  const getStatusIcon = (key) => {
    if (key.status === 'UPLOADED_TO_CTM' || key.uploadedToCtm === true) {
      return <Shield size={18} className="text-green-600" />;
    }
    if (key.status === 'FAILED' || key.isSuccessful === false) {
      return <AlertCircle size={18} className="text-red-600" />;
    }
    return <Key size={18} className="text-yellow-600" />;
  };

  return (
    <>
      <Modal
        isOpen={isOpen && !hideKeysModal}
        onClose={handleModalClose}
        title={
          <div className="flex items-center gap-3">
            <Key size={24} className="text-orange-500" />
            <div>
              <h3 className="text-2xl font-light tracking-wide leading-relaxed text-gray-900 dark:text-white">
                {user?.firstName} {user?.lastName} - {t('admin.users.keys.title')}
              </h3>
              <p className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
                {t('admin.users.keys.subtitle')}
              </p>
            </div>
          </div>
        }
        maxWidth="max-w-4xl"
        darkMode={darkMode}
      >
        {isLoading ? (
          <LoadingSpinner message={t('admin.users.keys.loading')} />
        ) : (
          <div>
            {userKeys && userKeys.length > 0 ? (
              <div className="space-y-4">
                {/* Estadísticas de llaves */}
                <div className={`p-4 rounded-xl border shadow-sm mb-6 ${
                  darkMode ? 'bg-orange-900/10 border-orange-700' : 'bg-orange-50 border-orange-200'
                }`}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Key size={20} className="text-orange-600" />
                      <div>
                        <h4 className="text-lg font-light tracking-wide text-gray-900 dark:text-white">
                          {t('admin.users.keys.totalKeys')}: <span className="font-semibold">{userKeys.length}</span>
                        </h4>
                      </div>
                    </div>
                    <div className="flex gap-4 text-sm">
                      <span className="flex items-center gap-2 px-3 py-1 rounded-lg bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-200">
                        <Shield size={16} className="text-green-600" />
                        {t('admin.users.keys.active')}: {userKeys.filter(k => k.status === 'UPLOADED_TO_CTM' || k.uploadedToCtm).length}
                      </span>
                      <span className="flex items-center gap-2 px-3 py-1 rounded-lg bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-200">
                        <AlertCircle size={16} className="text-red-600" />
                        {t('admin.users.keys.failed')}: {userKeys.filter(k => k.status === 'FAILED' || k.isSuccessful === false).length}
                      </span>
                    </div>
                  </div>
                                 </div>

                 {/* Buscador */}
                 <div className="mb-6">
                   <div className="relative">
                     <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                       <Search size={20} className="text-gray-400" />
                     </div>
                     <input
                       type="text"
                       value={searchTerm}
                       onChange={(e) => handleSearchChange(e.target.value)}
                       placeholder="Buscar llaves por nombre, tipo, estado..."
                       className={`w-full pl-10 pr-10 py-3 border rounded-lg transition-all duration-200 ${
                         darkMode 
                           ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-orange-500 focus:ring-orange-500' 
                           : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:border-orange-500 focus:ring-orange-500'
                       }`}
                     />
                     {searchTerm && (
                       <button
                         onClick={handleClearSearch}
                         className="absolute inset-y-0 right-0 pr-3 flex items-center"
                       >
                         <X size={20} className="text-gray-400 hover:text-gray-600 transition-colors duration-200" />
                       </button>
                     )}
                   </div>
                   {searchTerm && (
                     <div className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                       {filteredKeys.length} de {userKeys?.length || 0} llaves encontradas
                     </div>
                   )}
                 </div>

                 <div className={`grid gap-3 transition-all duration-500 ease-in-out ${
                   isAnimating
                     ? animationDirection === 'next'
                       ? 'transform translate-x-8 opacity-60'
                       : 'transform -translate-x-8 opacity-60'
                     : 'transform translate-x-0 opacity-100'
                 }`}>
                   {paginatedKeys.map((key, index) => (
                     <div
                       key={`${key.id}-${currentPage}-${index}`}
                       className={`p-4 rounded-lg border ${
                         darkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'
                       }`}
                     >
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            {getStatusIcon(key)}
                            <h4 className="font-semibold text-lg">{key.name}</h4>
                            <span
                              className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(key)}`}
                            >
                              {getStatusText(key)}
                            </span>
                            <span className="px-2 py-1 rounded text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                              {key.type || key.algorithm}
                            </span>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div className="flex items-center gap-2">
                              <Calendar size={16} className="text-gray-500" />
                              <span className="text-gray-600 dark:text-gray-300">
                                {key.createdAt ? new Date(key.createdAt).toLocaleDateString(
                                  currentLanguage === 'es' ? 'es-ES' : 'en-US'
                                ) : 'N/A'}
                              </span>
                            </div>
                            <div>
                              <span className="text-gray-600 dark:text-gray-300">
                                {key.ctmKeyId ? 'CTM ID: ' : 'ID: '}
                              </span>
                              <span className="font-mono text-xs">
                                {(key.ctmKeyId || key.id).substring(0, 8)}...
                              </span>
                            </div>
                            <div>
                              <span className="text-gray-600 dark:text-gray-300">Bytes: </span>
                              <span className="font-medium">{key.numBytes || key.num_bytes || 'N/A'}</span>
                            </div>
                          </div>
                        </div>

                        <div className="ml-4">
                          <button
                            onClick={() => handleViewKeyDetail(key)}
                            className="p-2 border rounded-lg transition-all duration-200 transform border-gray-200 bg-gray-50 text-blue-500 hover:bg-blue-50 hover:border-blue-300 hover:text-blue-600 hover:scale-110 dark:border-gray-600 dark:bg-gray-700 dark:text-blue-400 dark:hover:bg-blue-900/20 dark:hover:border-blue-500"
                            title={t('admin.users.keys.viewDetail')}
                          >
                            <Eye size={18} />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                                 {/* Controles de paginación */}
                 {filteredKeys.length > 0 && totalPages > 1 && (
                   <div className="flex items-center justify-between mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
                                         <div className="text-sm text-gray-600 dark:text-gray-400">
                       {((currentPage - 1) * keysPerPage) + 1}-{Math.min(currentPage * keysPerPage, filteredKeys.length)} de {filteredKeys.length}
                     </div>
                    
                                         <div className="flex items-center gap-2">
                       <button
                         onClick={handlePreviousPage}
                         disabled={currentPage === 1 || isAnimating}
                         className={`w-8 h-8 rounded-md border transition-all duration-200 flex items-center justify-center ${
                           currentPage === 1 || isAnimating
                             ? 'bg-gray-200 border-gray-300 text-gray-400 cursor-not-allowed dark:bg-gray-700 dark:border-gray-600'
                             : 'bg-gray-100 border-gray-300 text-gray-600 hover:bg-gray-200 hover:border-gray-400 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700'
                         }`}
                       >
                         <ChevronLeft size={16} />
                       </button>
                       
                       <button
                         onClick={handleNextPage}
                         disabled={currentPage === totalPages || isAnimating}
                         className={`w-8 h-8 rounded-md border transition-all duration-200 flex items-center justify-center ${
                           currentPage === totalPages || isAnimating
                             ? 'bg-gray-200 border-gray-300 text-gray-400 cursor-not-allowed dark:bg-gray-700 dark:border-gray-600'
                             : 'bg-gray-100 border-gray-300 text-gray-600 hover:bg-gray-200 hover:border-gray-400 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700'
                         }`}
                       >
                         <ChevronRight size={16} />
                       </button>
                     </div>
                  </div>
                )}
              </div>
                         ) : (
               <div className="text-center py-8">
                 <Key size={48} className="mx-auto text-gray-400 mb-4" />
                 <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                   {searchTerm ? 'No se encontraron llaves' : 'Sin llaves registradas'}
                 </h3>
                 <p className="text-gray-600 dark:text-gray-400">
                   {searchTerm 
                     ? 'No hay llaves que coincidan con tu búsqueda.'
                     : 'Este usuario aún no ha creado ninguna llave cuántica.'
                   }
                 </p>
               </div>
             )}

            {/* Footer con botón centrado elegante */}
            <div className="flex justify-center mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
              <button
                onClick={handleModalClose}
                className={`px-8 py-3 rounded-xl font-light tracking-wide transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl ${
                  darkMode
                    ? 'bg-gray-700 hover:bg-gray-600 text-white border border-gray-600'
                    : 'bg-gray-100 hover:bg-gray-200 text-gray-700 border border-gray-300'
                }`}
              >
                {t('admin.users.keys.close')}
              </button>
            </div>
          </div>
        )}
      </Modal>

      {/* Modal de detalle de llave */}
      <KeyDetailModal
        isOpen={showKeyDetail}
        onClose={handleCloseKeyDetail}
        keyData={selectedKey}
        darkMode={darkMode}
        isAdmin={true}
      />
    </>
  );
};

UserKeysModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  user: PropTypes.object,
  userKeys: PropTypes.array,
  isLoading: PropTypes.bool,
  darkMode: PropTypes.bool.isRequired
};

export default UserKeysModal;
