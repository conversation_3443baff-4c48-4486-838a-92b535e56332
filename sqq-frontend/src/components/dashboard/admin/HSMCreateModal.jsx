import { useState } from 'react';
import { Server, Settings } from 'lucide-react';
import PropTypes from 'prop-types';
import { useLanguage } from '../../../hooks';
import { Modal, FormField } from '../../common';

/**
 * Modal para crear un nuevo HSM
 * Diseño basado en la configuración de servicios
 */
const HSMCreateModal = ({ isOpen, onClose, onSubmit, darkMode = false }) => {
  const { t } = useLanguage();
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    hsmConfig: {
      url: ''
    }
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      // Preparar datos según el DTO del backend
      const hsmData = {
        name: formData.name,
        description: formData.description || '',
        url: formData.hsmConfig.url || formData.hsmConfig.ipAddress
      };
      await onSubmit(hsmData);
      setFormData({
        name: '',
        description: '',
        hsmConfig: {
          url: ''
        }
      });
    } catch (error) {
      console.error('Error creating HSM:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleHsmConfigChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      hsmConfig: {
        ...prev.hsmConfig,
        [field]: value
      }
    }));
  };



  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={
        <div className="flex items-center gap-3">
          <Server size={24} className="text-emerald-500" />
          <div>
            <h3 className="text-2xl font-light tracking-wide leading-relaxed text-gray-900 dark:text-white">
              {t('admin.hsms.createModal.title')}
            </h3>
            <p className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
              {t('admin.hsms.createModal.subtitle')}
            </p>
          </div>
        </div>
      }
      maxWidth="max-w-2xl"
      darkMode={darkMode}
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Configuración de HSM - Diseño exacto de Configuración de Servicios */}
        <div className={`p-4 rounded-xl border shadow-sm ${
          darkMode ? 'bg-emerald-900/10 border-emerald-700' : 'bg-emerald-50 border-emerald-200'
        }`}>
          <h4 className="text-lg font-light tracking-wide text-emerald-700 dark:text-emerald-300 mb-4 flex items-center gap-2">
            <Settings size={20} className="text-emerald-600" />
            {t('admin.hsms.createModal.configTitle')}
          </h4>

          {/* Información básica del HSM */}
          <div className="space-y-4 mb-6">
            <FormField
              label={t('admin.hsms.form.name')}
              type="text"
              value={formData.name}
              onChange={(e) => handleChange('name', e.target.value)}
              placeholder={t('admin.hsms.form.namePlaceholder')}
              required
              darkMode={darkMode}
            />

            <FormField
              label={t('admin.hsms.form.description')}
              type="text"
              value={formData.description}
              onChange={(e) => handleChange('description', e.target.value)}
              placeholder={t('admin.hsms.form.descriptionPlaceholder')}
              darkMode={darkMode}
            />

          </div>

          {/* Configuración de conexión HSM */}
          <FormField
            label={t('admin.hsms.form.url')}
            type="url"
            value={formData.hsmConfig.url}
            onChange={(e) => handleHsmConfigChange('url', e.target.value)}
            placeholder="http://tomcatlinux:8080"
            required
            darkMode={darkMode}
          />
        </div>

        {/* Footer - Botones con el mismo estilo que CTM */}
        <div className="flex justify-center gap-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
          <button
            type="button"
            onClick={onClose}
            disabled={isSubmitting}
            className={`px-8 py-3 rounded-xl font-light tracking-wide transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl ${
              darkMode
                ? 'bg-gray-700 hover:bg-gray-600 text-white border border-gray-600'
                : 'bg-gray-100 hover:bg-gray-200 text-gray-700 border border-gray-300'
            } ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            {t('common.cancel')}
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className={`px-8 py-3 rounded-xl font-light tracking-wide transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 text-white flex items-center justify-center gap-2 ${
              isSubmitting ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            {isSubmitting ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                {t('common.loading')}
              </>
            ) : (
              <>
                <Server size={16} />
                {t('admin.hsms.createModal.submit')}
              </>
            )}
          </button>
        </div>
      </form>
    </Modal>
  );
};

HSMCreateModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onSubmit: PropTypes.func.isRequired,
  darkMode: PropTypes.bool
};

export default HSMCreateModal;
