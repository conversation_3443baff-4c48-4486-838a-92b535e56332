import { useState } from 'react';
import { User, Mail, Building, Calendar, Edit, Lock } from 'lucide-react';
import PropTypes from 'prop-types';
import { ErrorAlert } from '../../common';
import { ProfileEditModal, PasswordChangeModal } from '../user';
import { useLanguage } from '../../../hooks';

/**
 * Componente para gestión del perfil del administrador
 */
const AdminProfileManagement = ({
  currentUser,
  isLoading,
  error,
  onClearError,
  onUpdateProfile,
  onChangePassword,
  darkMode
}) => {
  const [showEditModal, setShowEditModal] = useState(false);
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const { t, currentLanguage } = useLanguage();

  const handleEditProfile = () => {
    setShowEditModal(true);
  };

  const handleUpdateProfile = async (profileData) => {
    await onUpdateProfile(profileData);
    setShowEditModal(false);
  };

  const handleChangePassword = async (passwordData) => {
    await onChangePassword(passwordData);
    setShowPasswordModal(false);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="inline-flex items-center gap-3 px-6 py-3 rounded-xl bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-200 dark:border-blue-700">
          <div className="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
          <span className="text-gray-600 dark:text-gray-300 font-light tracking-wide">
            {t('profile.loading')}
          </span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-light tracking-wide leading-relaxed text-gray-900 dark:text-white">
            {t('admin.profile.title')}
          </h1>
          <p className="text-gray-600 dark:text-gray-400 font-light tracking-wide">
            {t('admin.profile.subtitle')}
          </p>
        </div>

        {/* Botones modernos con gradientes */}
        <div className="flex flex-col xs:flex-row gap-3 w-full sm:w-auto">
          <button
            onClick={() => setShowPasswordModal(true)}
            className="group flex items-center justify-center gap-2 sm:gap-3 px-4 sm:px-6 py-2.5 sm:py-3 rounded-xl font-light tracking-wide shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white text-sm sm:text-base"
          >
            <div className="p-1 bg-white/20 rounded-lg group-hover:bg-white/30 transition-all duration-300">
              <Lock size={16} />
            </div>
            <span>{t('profile.changePassword')}</span>
          </button>

          <button
            onClick={handleEditProfile}
            className="group flex items-center justify-center gap-2 sm:gap-3 px-4 sm:px-6 py-2.5 sm:py-3 rounded-xl font-light tracking-wide shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white text-sm sm:text-base"
          >
            <div className="p-1 bg-white/20 rounded-lg group-hover:bg-white/30 transition-all duration-300">
              <Edit size={16} />
            </div>
            <span>{t('profile.editProfile')}</span>
          </button>
        </div>
      </div>

      <ErrorAlert error={error} onClose={onClearError} />

      {/* Tarjeta principal del perfil - Diseño informativo estático */}
      <div className={`p-6 sm:p-8 rounded-2xl border shadow-lg ${
        darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
      }`}>
        {currentUser ? (
          <div className="space-y-6">
            {/* Header de la card */}
            <div className="flex items-center gap-3 mb-6">
              <User size={24} className="text-blue-500" />
              <div>
                <h3 className={`text-xl font-light tracking-wide ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  {t('profile.information')}
                </h3>
                <p className={`text-sm font-light tracking-wide ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  {t('profile.informationSubtitle')}
                </p>
              </div>
            </div>

            {/* Grid de información */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Nombre Completo */}
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <User size={16} className="text-blue-500" />
                  <span className={`text-sm font-light tracking-wide ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    {t('profile.fullName')}
                  </span>
                </div>
                <p className={`text-base font-light tracking-wide ${darkMode ? 'text-white' : 'text-gray-900'} pl-6`}>
                  {currentUser.firstName} {currentUser.lastName}
                </p>
              </div>

              {/* Email */}
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Mail size={16} className="text-green-500" />
                  <span className={`text-sm font-light tracking-wide ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    {t('profile.email')}
                  </span>
                </div>
                <p className={`text-base font-light tracking-wide ${darkMode ? 'text-white' : 'text-gray-900'} pl-6 break-all`}>
                  {currentUser.email}
                </p>
              </div>

              {/* Empresa */}
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Building size={16} className="text-purple-500" />
                  <span className={`text-sm font-light tracking-wide ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    {t('profile.company')}
                  </span>
                </div>
                <p className={`text-base font-light tracking-wide ${darkMode ? 'text-white' : 'text-gray-900'} pl-6`}>
                  {currentUser.company || t('profile.notSpecified')}
                </p>
              </div>

              {/* Fecha de Registro */}
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Calendar size={16} className="text-orange-500" />
                  <span className={`text-sm font-light tracking-wide ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    {t('profile.registrationDate')}
                  </span>
                </div>
                <p className={`text-base font-light tracking-wide ${darkMode ? 'text-white' : 'text-gray-900'} pl-6`}>
                  {currentUser.createdAt ? new Date(currentUser.createdAt).toLocaleDateString(
                    currentLanguage === 'es' ? 'es-ES' : 'en-US', {
                    day: '2-digit',
                    month: '2-digit',
                    year: 'numeric'
                  }) : 'N/A'}
                </p>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="inline-flex items-center gap-3 px-6 py-3 rounded-xl bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-200 dark:border-blue-700">
              <div className="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
              <span className="text-gray-600 dark:text-gray-300 font-light tracking-wide">
                {t('profile.loadingProfile')}
              </span>
            </div>
          </div>
        )}
      </div>

      <ProfileEditModal
        isOpen={showEditModal}
        currentUser={currentUser}
        onClose={() => setShowEditModal(false)}
        onUpdate={handleUpdateProfile}
        darkMode={darkMode}
      />

      <PasswordChangeModal
        isOpen={showPasswordModal}
        onClose={() => setShowPasswordModal(false)}
        onChangePassword={handleChangePassword}
        darkMode={darkMode}
      />
    </div>
  );
};

AdminProfileManagement.propTypes = {
  currentUser: PropTypes.object,
  isLoading: PropTypes.bool,
  error: PropTypes.string,
  onClearError: PropTypes.func.isRequired,
  onUpdateProfile: PropTypes.func.isRequired,
  onChangePassword: PropTypes.func.isRequired,
  darkMode: PropTypes.bool
};

export default AdminProfileManagement;
