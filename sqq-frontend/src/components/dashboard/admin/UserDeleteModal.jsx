import PropTypes from 'prop-types';
import { Trash2, <PERSON><PERSON><PERSON><PERSON><PERSON>, User } from 'lucide-react';
import { Modal } from '../../common';
import { useLanguage } from '../../../hooks/useLanguage';

/**
 * Modal de confirmación para eliminar usuario
 */
const UserDeleteModal = ({ isOpen, user, onClose, onConfirm, darkMode }) => {
  const { t } = useLanguage();

  if (!user) return null;

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={
        <div className="flex items-center gap-2 sm:gap-3">
          <Trash2 size={20} className="text-red-500 sm:w-6 sm:h-6" />
          <div>
            <h3 className="text-lg sm:text-2xl font-light tracking-wide leading-relaxed text-gray-900 dark:text-white">
              {t('admin.users.delete.title')}
            </h3>
            <p className="text-xs sm:text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
              {t('admin.users.delete.subtitle')}
            </p>
          </div>
        </div>
      }
      maxWidth="max-w-md"
      darkMode={darkMode}
    >
      <div className="space-y-3 sm:space-y-4">
        {/* Información del Usuario */}
        <div className={`p-3 sm:p-4 rounded-xl border shadow-sm ${
          darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
        }`}>
          <h4 className="text-sm sm:text-base font-light tracking-wide mb-3 flex items-center gap-2 text-gray-900 dark:text-white">
            <User size={16} className="text-orange-500 sm:w-5 sm:h-5" />
            {t('admin.users.delete.userInfo')}
          </h4>

          <div className="space-y-2">
            <div>
              <label className="block text-xs sm:text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-1">
                {t('admin.users.detail.fullName')}
              </label>
              <div className={`px-3 sm:px-4 py-2.5 sm:py-3 border rounded-lg font-light tracking-wide text-sm sm:text-base ${
                darkMode
                  ? 'border-gray-600 bg-gray-700 text-white'
                  : 'border-gray-300 bg-gray-50 text-gray-900'
              }`}>
                {user.firstName} {user.lastName}
              </div>
            </div>

            <div>
              <label className="block text-xs sm:text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-1">
                {t('admin.users.detail.email')}
              </label>
              <div className={`px-3 sm:px-4 py-2.5 sm:py-3 border rounded-lg font-light tracking-wide text-sm sm:text-base ${
                darkMode
                  ? 'border-gray-600 bg-gray-700 text-white'
                  : 'border-gray-300 bg-gray-50 text-gray-900'
              }`}>
                {user.email}
              </div>
            </div>

            <div>
              <label className="block text-xs sm:text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 mb-1">
                {t('admin.users.detail.role')}
              </label>
              <div className={`px-3 sm:px-4 py-2.5 sm:py-3 border rounded-lg font-light tracking-wide text-sm sm:text-base ${
                darkMode
                  ? 'border-gray-600 bg-gray-700 text-white'
                  : 'border-gray-300 bg-gray-50 text-gray-900'
              }`}>
                {user.role === 'ADMIN' ? t('admin.users.detail.administrator') : t('admin.users.detail.user')}
              </div>
            </div>
          </div>
        </div>

        {/* Advertencia */}
        <div className={`p-4 rounded-xl border shadow-sm ${
          darkMode ? 'bg-red-900/10 border-red-700' : 'bg-red-50 border-red-200'
        }`}>
          <div className="flex items-start gap-3">
            <AlertTriangle size={18} className="text-red-500 flex-shrink-0" />
            <div className="flex-1">
              <h4 className="text-base font-light tracking-wide text-red-700 dark:text-red-300 mb-2">
                {t('admin.users.delete.warningTitle')}
              </h4>
              <p className="text-sm font-light tracking-wide text-red-600 dark:text-red-400">
                {t('admin.users.delete.warningMessage')}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Footer con botones centrados */}
      <div className="flex justify-center gap-4 mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
        <button
          onClick={onClose}
          className="px-8 py-3 rounded-xl bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-light tracking-wide shadow-sm hover:shadow-md transform hover:scale-105 transition-all duration-300"
        >
          {t('common.cancel')}
        </button>

        <button
          onClick={onConfirm}
          className="px-8 py-3 rounded-xl font-light tracking-wide shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white flex items-center gap-3"
        >
          <div className="p-1 bg-white/20 rounded-lg">
            <Trash2 size={16} />
          </div>
          <span>
            {t('admin.users.delete.deleteButton')}
          </span>
        </button>
      </div>
    </Modal>
  );
};

UserDeleteModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  user: PropTypes.object,
  onClose: PropTypes.func.isRequired,
  onConfirm: PropTypes.func.isRequired,
  darkMode: PropTypes.bool.isRequired
};

export default UserDeleteModal;
