import { useState, useEffect, useRef } from 'react';
import { Server, Save, ChevronDown, Shield, Zap } from 'lucide-react';
import PropTypes from 'prop-types';
import { useLanguage } from '../../../hooks';
import { Modal, FormField } from '../../common';
import CTMConfigurationSection from './CTMConfigurationSection';

/**
 * Modal para editar un CTM existente
 * Diseño simplificado con campos de configuración CTM + nombre y estado
 */
const CTMEditModal = ({ isOpen, onClose, onSubmit, ctm, darkMode = false }) => {
  const { t } = useLanguage();
  const [formData, setFormData] = useState({
    name: '',
    isActive: true,
    config: {
      ipAddress: '',
      username: '',
      password: '',
      domain: '',
      seqrngIpAddress: '',
      seqrngApiToken: ''
    }
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isStatusDropdownOpen, setIsStatusDropdownOpen] = useState(false);
  const statusDropdownRef = useRef(null);

  // Cargar datos del CTM al abrir
  useEffect(() => {
    if (ctm) {
      setFormData({
        name: ctm.name || '',
        isActive: ctm.isActive !== undefined ? ctm.isActive : true,
        config: {
          ipAddress: ctm.ipAddress || ctm?.ctmConfig?.ipAddress || '',
          username: ctm.username || ctm?.ctmConfig?.username || '',
          password: ctm.password || ctm?.ctmConfig?.password || '',
          domain: ctm.domain || ctm?.ctmConfig?.domain || '',
          seqrngIpAddress: ctm.seqrngIpAddress || '',
          seqrngApiToken: ctm.seqrngApiToken || ''
        }
      });
    }
  }, [ctm]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Enviamos todos los campos: nombre, estado y configuración
      await onSubmit({
        name: formData.name,
        isActive: formData.isActive,
        ipAddress: formData.config.ipAddress,
        username: formData.config.username,
        password: formData.config.password,
        domain: formData.config.domain,
        seqrngIpAddress: formData.config.seqrngIpAddress,
        seqrngApiToken: formData.config.seqrngApiToken
      });
    } catch (error) {
      console.error('Error updating CTM:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleConfigChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      config: {
        ...prev.config,
        [field]: value
      }
    }));
  };

  // Cerrar dropdown al hacer clic fuera
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (statusDropdownRef.current && !statusDropdownRef.current.contains(event.target)) {
        setIsStatusDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleStatusSelect = (statusValue) => {
    handleChange('status', statusValue);
    setIsStatusDropdownOpen(false);
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={
        <div className="flex items-center gap-3">
          <Server size={24} className="text-blue-500" />
          <div>
            <h3 className="text-2xl font-light tracking-wide leading-relaxed text-gray-900 dark:text-white">
              CipherTrust Manager
            </h3>
            <p className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
              {t('admin.ctms.editModal.subtitle')}
            </p>
          </div>
        </div>
      }
      maxWidth="max-w-2xl"
      darkMode={darkMode}
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Información básica del CTM */}
        <div className={`p-4 rounded-xl border shadow-sm ${
          darkMode ? 'bg-blue-900/10 border-blue-700' : 'bg-blue-50 border-blue-200'
        }`}>
          <h4 className="text-lg font-light tracking-wide text-blue-700 dark:text-blue-300 mb-4 flex items-center gap-2">
            <Server size={20} className="text-blue-600" />
            {t('admin.ctms.editModal.configTitle')}
          </h4>

                     <div className="space-y-4">
             <div>
               <label className={`block text-sm font-light tracking-wide mb-2 ${
                 darkMode ? 'text-gray-300' : 'text-gray-700'
               }`}>
                 {t('admin.ctms.form.name')} <span className="text-red-500">*</span>
               </label>
                               <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleChange('name', e.target.value)}
                  placeholder={t('admin.ctms.form.namePlaceholder')}
                  required
                  disabled={isSubmitting}
                  className={`w-full px-4 py-2.5 rounded-xl border font-light tracking-wide transition-all duration-300 shadow-sm hover:shadow-md focus:shadow-lg ${
                    darkMode
                      ? 'bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500'
                      : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:border-blue-500'
                  } ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}`}
                />
             </div>

             <div>
               <label className={`block text-sm font-light tracking-wide mb-2 ${
                 darkMode ? 'text-gray-300' : 'text-gray-700'
               }`}>
                 {t('admin.ctms.form.status')}
               </label>
               <select
                 value={formData.isActive}
                 onChange={(e) => handleChange('isActive', e.target.value === 'true')}
                 disabled={isSubmitting}
                 className={`w-full px-4 py-3.5 rounded-xl border font-light tracking-wide transition-all duration-300 shadow-sm hover:shadow-md focus:shadow-lg ${
                   darkMode
                     ? 'bg-gray-800 border-gray-600 text-white focus:border-blue-500'
                     : 'bg-white border-gray-300 text-gray-900 focus:border-blue-500'
                 } ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}`}
               >
                 <option value="true">{t('admin.ctms.status.active')}</option>
                 <option value="false">{t('admin.ctms.status.inactive')}</option>
               </select>
             </div>
          </div>
        </div>

        {/* Configuración de conexión CTM */}
        <CTMConfigurationSection
          config={formData.config}
          onConfigChange={handleConfigChange}
          darkMode={darkMode}
          required={true}
        />

        {/* Configuración de SequreRNG */}
        <div className={`p-4 rounded-xl border shadow-sm ${
          darkMode ? 'bg-purple-900/10 border-purple-700' : 'bg-purple-50 border-purple-200'
        }`}>
          <h4 className="text-lg font-light tracking-wide mb-4 flex items-center gap-3 text-gray-900 dark:text-white">
            <Zap size={22} className="text-purple-500" />
            {t('admin.ctms.editModal.seqrngConfigTitle')}
          </h4>

          <div className="space-y-4">
            <FormField
              label={t('admin.ctms.editModal.seqrng.ipAddress')}
              type="text"
              value={formData.config.seqrngIpAddress}
              onChange={(e) => handleConfigChange('seqrngIpAddress', e.target.value)}
              placeholder="https://seqrng.example.com:1982"
              darkMode={darkMode}
            />

            <FormField
              label={t('admin.ctms.editModal.seqrng.apiToken')}
              type="text"
              value={formData.config.seqrngApiToken}
              onChange={(e) => handleConfigChange('seqrngApiToken', e.target.value)}
              placeholder="1|cifovFzUWTeAPUeAYlZYefsY3tXFf9Z1TV5WV0YL"
              darkMode={darkMode}
            />
          </div>
        </div>

        {/* Footer - estilo consistente con editar HSM */}
        <div className="flex justify-center gap-3 mt-2 pt-4 border-t border-gray-200 dark:border-gray-600">
          <button
            type="button"
            onClick={onClose}
            disabled={isSubmitting}
            className={`px-8 py-3 rounded-xl font-light tracking-wide transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl ${
              darkMode
                ? 'bg-gray-700 hover:bg-gray-600 text-white border border-gray-600'
                : 'bg-gray-100 hover:bg-gray-200 text-gray-700 border border-gray-300'
            } ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            {t('common.cancel')}
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className={`px-8 py-3 rounded-xl font-light tracking-wide transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white flex items-center justify-center gap-2 ${
              isSubmitting ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            {isSubmitting ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                {t('common.loading')}
              </>
            ) : (
              <>
                <Save size={16} />
                {t('admin.ctms.editModal.submit')}
              </>
            )}
          </button>
        </div>
      </form>
    </Modal>
  );
};

CTMEditModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onSubmit: PropTypes.func.isRequired,
  ctm: PropTypes.object.isRequired,
  darkMode: PropTypes.bool
};

export default CTMEditModal;
