import { useState, useRef, useEffect } from 'react';
import { Server, Settings, ChevronDown, Shield, Zap } from 'lucide-react';
import PropTypes from 'prop-types';
import { useLanguage } from '../../../hooks';
import { Modal, FormField } from '../../common';
import CTMConfigurationSection from './CTMConfigurationSection';

/**
 * Modal para crear un nuevo CTM
 * Diseño basado en la configuración de servicios
 */
const CTMCreateModal = ({ isOpen, onClose, onSubmit, darkMode = false }) => {
  const { t } = useLanguage();
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    ctmConfig: {
      ipAddress: '',
      username: '',
      password: '',
      domain: '',
      seqrngIpAddress: '',
      seqrngApiToken: ''
    }
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      // Preparar datos según el DTO del backend
      const ctmData = {
        name: formData.name,
        description: formData.description || '',
        ipAddress: formData.ctmConfig.ipAddress,
        username: formData.ctmConfig.username,
        password: formData.ctmConfig.password,
        domain: formData.ctmConfig.domain,
        seqrngIpAddress: formData.ctmConfig.seqrngIpAddress || '',
        seqrngApiToken: formData.ctmConfig.seqrngApiToken || '',
        isActive: true
      };
      await onSubmit(ctmData);
      setFormData({
        name: '',
        description: '',
        ctmConfig: {
          ipAddress: '',
          username: '',
          password: '',
          domain: '',
          seqrngIpAddress: '',
          seqrngApiToken: ''
        }
      });
    } catch (error) {
      console.error('Error creating CTM:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleCtmConfigChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      ctmConfig: {
        ...prev.ctmConfig,
        [field]: value
      }
    }));
  };



  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={
        <div className="flex items-center gap-3">
          <Server size={24} className="text-blue-500" />
          <div>
            <h3 className="text-2xl font-light tracking-wide leading-relaxed text-gray-900 dark:text-white">
              {t('admin.ctms.createModal.title')}
            </h3>
            <p className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
              {t('admin.ctms.createModal.subtitle')}
            </p>
          </div>
        </div>
      }
      maxWidth="max-w-2xl"
      darkMode={darkMode}
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Configuración de CTM - Diseño exacto de Configuración de Servicios */}
        <div className={`p-4 rounded-xl border shadow-sm ${
          darkMode ? 'bg-blue-900/10 border-blue-700' : 'bg-blue-50 border-blue-200'
        }`}>
          <h4 className="text-lg font-light tracking-wide mb-4 flex items-center gap-3 text-gray-900 dark:text-white">
            <Settings size={22} className="text-blue-500" />
            {t('admin.ctms.createModal.configTitle')}
          </h4>

          {/* Información básica del CTM */}
          <div className="space-y-4 mb-6">
            <div>
              <label className={`block text-sm font-light tracking-wide mb-2 ${
                darkMode ? 'text-gray-300' : 'text-gray-700'
              }`}>
                {t('admin.ctms.form.name')} <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleChange('name', e.target.value)}
                placeholder={t('admin.ctms.form.namePlaceholder')}
                required
                disabled={isSubmitting}
                className={`w-full px-4 py-3.5 rounded-xl border font-light tracking-wide transition-all duration-300 shadow-sm hover:shadow-md focus:shadow-lg ${
                  darkMode
                    ? 'bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500'
                    : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:border-blue-500'
                } ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}`}
              />
            </div>

          </div>

          {/* Configuración de conexión CTM */}
          <CTMConfigurationSection
            config={formData.ctmConfig}
            onConfigChange={handleCtmConfigChange}
            darkMode={darkMode}
            showTitle={false}
            required={true}
          />
        </div>

        {/* Configuración de SequreRNG */}
        <div className={`p-4 rounded-xl border shadow-sm ${
          darkMode ? 'bg-purple-900/10 border-purple-700' : 'bg-purple-50 border-purple-200'
        }`}>
          <h4 className="text-lg font-light tracking-wide mb-4 flex items-center gap-3 text-gray-900 dark:text-white">
            <Zap size={22} className="text-purple-500" />
            {t('admin.ctms.createModal.seqrngConfigTitle')}
          </h4>

          <div className="space-y-4">
            <FormField
              label={t('admin.ctms.createModal.seqrng.ipAddress')}
              type="text"
              value={formData.ctmConfig.seqrngIpAddress}
              onChange={(e) => handleCtmConfigChange('seqrngIpAddress', e.target.value)}
              placeholder="https://seqrng.example.com:1982"
              darkMode={darkMode}
            />

            <FormField
              label={t('admin.ctms.createModal.seqrng.apiToken')}
              type="text"
              value={formData.ctmConfig.seqrngApiToken}
              onChange={(e) => handleCtmConfigChange('seqrngApiToken', e.target.value)}
              placeholder="1|cifovFzUWTeAPUeAYlZYefsY3tXFf9Z1TV5WV0YL"
              darkMode={darkMode}
            />
          </div>
        </div>

        {/* Footer - Botones con el mismo estilo que ProfileEditModal */}
        <div className="flex justify-center gap-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
          <button
            type="button"
            onClick={onClose}
            disabled={isSubmitting}
            className={`px-8 py-3 rounded-xl font-light tracking-wide transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl ${
              darkMode
                ? 'bg-gray-700 hover:bg-gray-600 text-white border border-gray-600'
                : 'bg-gray-100 hover:bg-gray-200 text-gray-700 border border-gray-300'
            } ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            {t('common.cancel')}
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className={`px-8 py-3 rounded-xl font-light tracking-wide transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white flex items-center justify-center gap-2 ${
              isSubmitting ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            {isSubmitting ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                {t('common.loading')}
              </>
            ) : (
              <>
                <Server size={16} />
                {t('admin.ctms.createModal.submit')}
              </>
            )}
          </button>
        </div>
      </form>
    </Modal>
  );
};

CTMCreateModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onSubmit: PropTypes.func.isRequired,
  darkMode: PropTypes.bool
};

export default CTMCreateModal;
