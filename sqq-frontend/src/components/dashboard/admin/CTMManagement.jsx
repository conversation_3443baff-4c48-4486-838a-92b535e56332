import { useState, useRef } from 'react';
import { Server, Plus, Search, Filter, MoreVertical, Edit, Trash2, Eye, ChevronDown } from 'lucide-react';
import PropTypes from 'prop-types';
import { useLanguage } from '../../../hooks';
import { <PERSON><PERSON>, <PERSON>, <PERSON>Field, <PERSON>ading<PERSON><PERSON><PERSON>, <PERSON>rror<PERSON><PERSON><PERSON> } from '../../common';
import CTMCreateModal from './CTMCreateModal';
import CTMEditModal from './CTMEditModal';
import CTMDeleteModal from './CTMDeleteModal';
import CTMViewModal from './CTMViewModal';

/**
 * Componente para gestionar CTMs del sistema
 * Basado en UserManagement pero adaptado para CTMs
 */
const CTMManagement = ({ 
  ctms = [], 
  isLoading = false, 
  error = null, 
  onClearError, 
  onUpdateCTM, 
  onCreateCTM, 
  onDeleteCTM,
  darkMode = false 
}) => {
  const { t } = useLanguage();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedCTM, setSelectedCTM] = useState(null);
  const [showViewModal, setShowViewModal] = useState(false);
  const [viewCTM, setViewCTM] = useState(null);

  // Estados para dropdown custom
  const [isStatusDropdownOpen, setIsStatusDropdownOpen] = useState(false);
  const statusDropdownRef = useRef(null);

  // Filtrar CTMs basado en búsqueda y filtros
  const filteredCTMs = (Array.isArray(ctms) ? ctms : []).filter(ctm => {
    const matchesSearch = ctm.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ctm.ipAddress?.includes(searchTerm) ||
                         (ctm.username && ctm.username.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesFilter = filterStatus === 'all' || ctm.status === filterStatus;

    return matchesSearch && matchesFilter;
  });

  const handleCreateCTM = async (ctmData) => {
    try {
      await onCreateCTM(ctmData);
      setShowCreateModal(false);
      setSelectedCTM(null); // Clear selected CTM after creation
    } catch (error) {
      console.error('Error creating CTM:', error);
    }
  };

  const handleUpdateCTM = async (ctmId, ctmData) => {
    try {
      await onUpdateCTM(ctmId, ctmData);
      setShowEditModal(false);
      setSelectedCTM(null);
    } catch (error) {
      console.error('Error updating CTM:', error);
    }
  };

  const handleDeleteCTM = async (ctmId) => {
    try {
      await onDeleteCTM(ctmId);
      setShowDeleteModal(false);
      setSelectedCTM(null);
    } catch (error) {
      console.error('Error deleting CTM:', error);
    }
  };

  const openEditModal = (ctm) => {
    setSelectedCTM(ctm);
    setShowEditModal(true);
  };

  const openDeleteModal = (ctm) => {
    setSelectedCTM(ctm);
    setShowDeleteModal(true);
  };

  // Handlers para los botones de acción
  const editCtm = (id) => openEditModal(ctms.find(c => c.id === id));
  const viewCtm = (id) => {
    setViewCTM(ctms.find(c => c.id === id));
    setShowViewModal(true);
  };
  const confirmDeleteCtm = (id) => openDeleteModal(ctms.find(c => c.id === id));

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'inactive':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'active':
        return t('admin.ctms.status.active');
      case 'inactive':
        return t('admin.ctms.status.inactive');
      default:
        return status;
    }
  };

  if (isLoading) {
    return <LoadingSpinner darkMode={darkMode} />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-light tracking-wide leading-relaxed text-gray-900 dark:text-white">
            {t('admin.ctms.title')}
          </h1>
          <p className="text-gray-600 dark:text-gray-400 font-light tracking-wide">
            {t('admin.ctms.subtitle')}
          </p>
        </div>
        <button
          onClick={() => setShowCreateModal(true)}
          className="flex items-center gap-2 sm:gap-3 px-4 sm:px-6 py-2.5 sm:py-3 rounded-xl font-light tracking-wide shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white text-sm sm:text-base"
        >
          <Plus size={16} />
          <span>{t('admin.ctms.create')}</span>
        </button>
      </div>

      {/* Error Alert */}
      {error && (
        <ErrorAlert
          message={error}
          onClose={onClearError}
          darkMode={darkMode}
        />
      )}

      {/* Filtros y búsqueda */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <input
            type="text"
            placeholder={t('admin.ctms.searchPlaceholder')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className={`w-full pl-10 pr-4 py-2 rounded-lg border transition-colors ${
              darkMode
                ? 'bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500'
                : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:border-blue-500'
            }`}
          />
        </div>
                 {/* Filtro por estado - Custom Dropdown */}
         <div className="relative" ref={statusDropdownRef}>
           <button
             type="button"
             onClick={() => setIsStatusDropdownOpen(!isStatusDropdownOpen)}
             className={`w-full px-4 py-2 border rounded-lg font-light tracking-wide focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 flex items-center justify-between ${
               darkMode
                 ? 'border-gray-600 bg-gray-700 text-white hover:bg-gray-600'
                 : 'border-gray-300 bg-white text-gray-900 hover:bg-gray-50'
             } cursor-pointer`}
           >
             <span>
               {filterStatus === 'all'
                 ? t('admin.ctms.filters.all')
                 : filterStatus === 'active'
                 ? t('admin.ctms.filters.active')
                 : t('admin.ctms.filters.inactive')
               }
             </span>
             <ChevronDown
               size={16}
               className={`transition-transform duration-200 ${isStatusDropdownOpen ? 'rotate-180' : ''} ${
                 darkMode ? 'text-gray-400' : 'text-gray-500'
               }`}
             />
           </button>

           {isStatusDropdownOpen && (
             <div className={`absolute top-full left-0 right-0 mt-1 border rounded-lg shadow-lg z-50 overflow-hidden ${
               darkMode
                 ? 'border-gray-600 bg-gray-700'
                 : 'border-gray-300 bg-white'
             }`}>
               <button
                 type="button"
                 onClick={() => {
                   setFilterStatus('all');
                   setIsStatusDropdownOpen(false);
                 }}
                 className={`w-full text-left px-4 py-3 font-light tracking-wide transition-all duration-200 ${
                   filterStatus === 'all'
                     ? darkMode
                       ? 'bg-blue-600/20 text-blue-400 border-l-4 border-blue-500'
                       : 'bg-blue-50 text-blue-600 border-l-4 border-blue-500'
                     : darkMode
                       ? 'text-gray-300 hover:bg-gray-600'
                       : 'text-gray-700 hover:bg-gray-50'
                 }`}
               >
                 {t('admin.ctms.filters.all')}
               </button>
               <button
                 type="button"
                 onClick={() => {
                   setFilterStatus('active');
                   setIsStatusDropdownOpen(false);
                 }}
                 className={`w-full text-left px-4 py-3 font-light tracking-wide transition-all duration-200 ${
                   filterStatus === 'active'
                     ? darkMode
                       ? 'bg-blue-600/20 text-blue-400 border-l-4 border-blue-500'
                       : 'bg-blue-50 text-blue-600 border-l-4 border-blue-500'
                     : darkMode
                       ? 'text-gray-300 hover:bg-gray-600'
                       : 'text-gray-700 hover:bg-gray-50'
                 }`}
               >
                 {t('admin.ctms.filters.active')}
               </button>
               <button
                 type="button"
                 onClick={() => {
                   setFilterStatus('inactive');
                   setIsStatusDropdownOpen(false);
                 }}
                 className={`w-full text-left px-4 py-3 font-light tracking-wide transition-all duration-200 ${
                   filterStatus === 'inactive'
                     ? darkMode
                       ? 'bg-blue-600/20 text-blue-400 border-l-4 border-blue-500'
                       : 'bg-blue-50 text-blue-600 border-l-4 border-blue-500'
                     : darkMode
                       ? 'text-gray-300 hover:bg-gray-600'
                       : 'text-gray-700 hover:bg-gray-50'
                 }`}
               >
                 {t('admin.ctms.filters.inactive')}
               </button>
             </div>
           )}
         </div>
      </div>

      {/* Lista de CTMs */}
      <div className={`rounded-xl border ${
        darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
      }`}>
        <div className="p-6">
          <h2 className="text-xl font-light tracking-wide text-gray-900 dark:text-white mb-4">
            {t('admin.ctms.list.title')} ({filteredCTMs.length})
          </h2>
          
          {filteredCTMs.length === 0 ? (
            <div className="text-center py-8">
              <Server size={48} className="mx-auto text-gray-400 mb-4" />
              <p className="text-gray-500 dark:text-gray-400 font-light tracking-wide">
                {t('admin.ctms.list.empty')}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredCTMs.map((ctm) => (
                <div
                  key={ctm.id}
                  className={`p-4 rounded-lg border transition-colors ${
                    darkMode
                      ? 'bg-gray-700 border-gray-600 hover:bg-gray-600'
                      : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className={`p-2 rounded-lg ${
                        darkMode ? 'bg-blue-900/30' : 'bg-blue-100'
                      }`}>
                        <Server size={20} className="text-blue-600 dark:text-blue-400" />
                      </div>
                      <div>
                                                 <h3 className="font-light tracking-wide text-gray-900 dark:text-white">
                           {ctm.name}
                         </h3>
                         <div className="flex items-center gap-4 mt-2">
                           <span className="text-xs text-gray-500 dark:text-gray-400">
                             {ctm.ipAddress}:{ctm.port}
                           </span>
                           <span className="text-xs text-gray-500 dark:text-gray-400">
                             {ctm.username || 'N/A'}
                           </span>
                           <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(ctm.status)}`}>
                             {getStatusText(ctm.status)}
                           </span>
                         </div>
                      </div>
                    </div>
                    {/* Botones de acción alineados a la derecha, igual que UserCard */}
                    <div className="flex flex-row gap-3">
                      <button
                        onClick={() => editCtm(ctm.id)}
                        className="p-2 border rounded-lg transition-all duration-200 transform border-gray-200 bg-gray-50 text-blue-500 hover:bg-blue-50 hover:border-blue-300 hover:text-blue-600 hover:scale-110 dark:border-gray-600 dark:bg-gray-700 dark:text-blue-400 dark:hover:bg-blue-900/20 dark:hover:border-blue-500"
                        title={t('admin.ctms.editCtm')}
                        aria-label={t('admin.ctms.editCtm')}
                      >
                        <Edit size={18} />
                      </button>
                      <button
                        onClick={() => viewCtm(ctm.id)}
                        className="p-2 border rounded-lg transition-all duration-200 transform border-gray-200 bg-gray-50 text-green-500 hover:bg-green-50 hover:border-green-300 hover:text-green-600 hover:scale-110 dark:border-gray-600 dark:bg-gray-700 dark:text-green-400 dark:hover:bg-green-900/20 dark:hover:border-green-500"
                        title={t('admin.ctms.viewCtm')}
                        aria-label={t('admin.ctms.viewCtm')}
                      >
                        <Eye size={18} />
                      </button>
                      <button
                        onClick={() => confirmDeleteCtm(ctm.id)}
                        className="p-2 border rounded-lg transition-all duration-200 transform border-gray-200 bg-gray-50 text-red-500 hover:bg-red-50 hover:border-red-300 hover:text-red-600 hover:scale-110 dark:border-gray-600 dark:bg-gray-700 dark:text-red-400 dark:hover:bg-red-900/20 dark:hover:border-red-500"
                        title={t('admin.ctms.deleteCtm')}
                        aria-label={t('admin.ctms.deleteCtm')}
                      >
                        <Trash2 size={18} />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Modal de Crear CTM */}
      {showCreateModal && (
        <CTMCreateModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onSubmit={handleCreateCTM}
          darkMode={darkMode}
        />
      )}

      {/* Modal de Editar CTM */}
      {showEditModal && selectedCTM && (
        <CTMEditModal
          isOpen={showEditModal}
          onClose={() => setShowEditModal(false)}
          onSubmit={(data) => handleUpdateCTM(selectedCTM.id, data)}
          ctm={selectedCTM}
          darkMode={darkMode}
        />
      )}

      {/* Modal de Eliminar CTM */}
      {showDeleteModal && selectedCTM && (
        <CTMDeleteModal
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          onConfirm={() => handleDeleteCTM(selectedCTM.id)}
          ctm={selectedCTM}
          darkMode={darkMode}
        />
      )}

      {/* Modal de ver CTM */}
      {showViewModal && viewCTM && (
        <CTMViewModal
          isOpen={showViewModal}
          onClose={() => setShowViewModal(false)}
          ctm={viewCTM}
          darkMode={darkMode}
        />
      )}
    </div>
  );
};

CTMManagement.propTypes = {
  ctms: PropTypes.array,
  isLoading: PropTypes.bool,
  error: PropTypes.string,
  onClearError: PropTypes.func.isRequired,
  onUpdateCTM: PropTypes.func.isRequired,
  onCreateCTM: PropTypes.func.isRequired,
  onDeleteCTM: PropTypes.func.isRequired,
  darkMode: PropTypes.bool
};

export default CTMManagement;
