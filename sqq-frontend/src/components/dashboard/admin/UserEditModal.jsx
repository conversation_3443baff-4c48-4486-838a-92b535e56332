import { useState, useEffect, useRef } from 'react';
import { Save, User, ChevronDown, Shield, Server } from 'lucide-react';
import PropTypes from 'prop-types';
import { Modal, FormField } from '../../common';
import { useLanguage } from '../../../hooks';

/**
 * Modal para editar información de usuario
 */
const UserEditModal = ({ isOpen, user, onClose, onSave, darkMode, ctms = [], hsms = [] }) => {
  const { t } = useLanguage();
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    company: '',
    role: 'USER',
    isActive: true,
    selectedCtms: [],
    selectedHsms: []
  });
  const [isSaving, setIsSaving] = useState(false);
  const [isRoleDropdownOpen, setIsRoleDropdownOpen] = useState(false);
  const [isStatusDropdownOpen, setIsStatusDropdownOpen] = useState(false);
  const roleDropdownRef = useRef(null);
  const statusDropdownRef = useRef(null);
  
  // Simplificado: Solo una configuración por servicio, sin múltiples configuraciones

  // Cerrar dropdowns al hacer clic fuera
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (roleDropdownRef.current && !roleDropdownRef.current.contains(event.target)) {
        setIsRoleDropdownOpen(false);
      }
      if (statusDropdownRef.current && !statusDropdownRef.current.contains(event.target)) {
        setIsStatusDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  useEffect(() => {
    if (user) {
      setFormData({
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        email: user.email || '',
        company: user.company || '',
        role: user.role || 'USER',
        isActive: user.isActive !== undefined ? user.isActive : true,
        selectedCtms: user.ctms ? user.ctms.map(ctm => ctm.id) : [],
        selectedHsms: user.hsms ? user.hsms.map(hsm => hsm.id) : []
      });
    }
  }, [user]);

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleCtmToggle = (ctmId) => {
    setFormData(prev => ({
      ...prev,
      selectedCtms: prev.selectedCtms.includes(ctmId)
        ? prev.selectedCtms.filter(id => id !== ctmId)
        : [...prev.selectedCtms, ctmId]
    }));
  };

  const handleHsmToggle = (hsmId) => {
    setFormData(prev => ({
      ...prev,
      selectedHsms: prev.selectedHsms.includes(hsmId)
        ? prev.selectedHsms.filter(id => id !== hsmId)
        : [...prev.selectedHsms, hsmId]
    }));
  };

  const handleRoleSelect = (roleValue) => {
    handleChange('role', roleValue);
    setIsRoleDropdownOpen(false);
  };

  const handleStatusSelect = (statusValue) => {
    handleChange('isActive', statusValue);
    setIsStatusDropdownOpen(false);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSaving(true);
    try {
      // Convertir arrays a formato compatible con el backend actual
      const backendData = {
        ...formData,
        ctmIds: formData.selectedCtms,
        hsmIds: formData.selectedHsms
      };

      await onSave(backendData);
    } finally {
      setIsSaving(false);
    }
  };

  const roleOptions = [
    { value: 'USER', label: t('admin.users.edit.roles.user') },
    { value: 'ADMIN', label: t('admin.users.edit.roles.admin') }
  ];

  const statusOptions = [
    { value: true, label: t('admin.users.edit.statusOptions.active') },
    { value: false, label: t('admin.users.edit.statusOptions.inactive') }
  ];

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={
        <div className="flex items-center gap-2 sm:gap-3">
          <User size={20} className="text-blue-500 sm:w-6 sm:h-6" />
          <div>
            <h3 className="text-lg sm:text-2xl font-light tracking-wide leading-relaxed text-gray-900 dark:text-white">
              {t('admin.users.edit.title')}
            </h3>
            <p className="text-xs sm:text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
              {t('admin.users.edit.subtitle')}
            </p>
          </div>
        </div>
      }
      maxWidth="max-w-4xl"
      darkMode={darkMode}
    >
      <form onSubmit={handleSubmit}>
        <div className="space-y-6">
          {/* Información Personal */}
          <div className={`p-4 rounded-xl border shadow-sm ${
            darkMode ? 'bg-orange-900/10 border-orange-700' : 'bg-orange-50 border-orange-200'
          }`}>
            <h4 className="text-lg font-light tracking-wide mb-4 flex items-center gap-3 text-gray-900 dark:text-white">
              <User size={22} className="text-orange-500" />
              {t('admin.users.edit.personalInfo')}
            </h4>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                label={t('admin.users.edit.firstName')}
                type="text"
                value={formData.firstName}
                onChange={(e) => handleChange('firstName', e.target.value)}
                required
                disabled={isSaving}
                darkMode={darkMode}
              />

              <FormField
                label={t('admin.users.edit.lastName')}
                type="text"
                value={formData.lastName}
                onChange={(e) => handleChange('lastName', e.target.value)}
                required
                disabled={isSaving}
                darkMode={darkMode}
              />

              <FormField
                label={t('admin.users.edit.email')}
                type="email"
                value={formData.email}
                onChange={(e) => handleChange('email', e.target.value)}
                required
                disabled={isSaving}
                darkMode={darkMode}
              />

              <FormField
                label={t('admin.users.edit.company')}
                type="text"
                value={formData.company}
                onChange={(e) => handleChange('company', e.target.value)}
                disabled={isSaving}
                darkMode={darkMode}
              />

              <div>
                <label className={`block text-sm font-light tracking-wide mb-2 ${
                  darkMode ? 'text-gray-300' : 'text-gray-700'
                }`}>
                  {t('admin.users.edit.role')}
                </label>
                <select
                  value={formData.role}
                  onChange={(e) => handleChange('role', e.target.value)}
                  disabled={isSaving}
                  className={`w-full px-4 py-3.5 rounded-xl border font-light tracking-wide transition-all duration-300 shadow-sm hover:shadow-md focus:shadow-lg ${
                    darkMode
                      ? 'bg-gray-800 border-gray-600 text-white focus:border-orange-500'
                      : 'bg-white border-gray-300 text-gray-900 focus:border-orange-500'
                  } ${isSaving ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  <option value="USER">{t('admin.users.edit.roles.user')}</option>
                  <option value="ADMIN">{t('admin.users.edit.roles.admin')}</option>
                </select>
              </div>

              <div>
                <label className={`block text-sm font-light tracking-wide mb-2 ${
                  darkMode ? 'text-gray-300' : 'text-gray-700'
                }`}>
                  {t('admin.users.edit.status')}
                </label>
                <select
                  value={formData.isActive}
                  onChange={(e) => handleChange('isActive', e.target.value === 'true')}
                  disabled={isSaving}
                  className={`w-full px-4 py-3.5 rounded-xl border font-light tracking-wide transition-all duration-300 shadow-sm hover:shadow-md focus:shadow-lg ${
                    darkMode
                      ? 'bg-gray-800 border-gray-600 text-white focus:border-orange-500'
                      : 'bg-white border-gray-300 text-gray-900 focus:border-orange-500'
                  } ${isSaving ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  <option value="true">{t('admin.users.edit.statusOptions.active')}</option>
                  <option value="false">{t('admin.users.edit.statusOptions.inactive')}</option>
                </select>
              </div>
            </div>
          </div>

          {/* Asignación de CTMs */}
          <div className={`p-4 rounded-xl border shadow-sm ${
            darkMode ? 'bg-blue-900/10 border-blue-700' : 'bg-blue-50 border-blue-200'
          }`}>
            <h4 className="text-lg font-light tracking-wide mb-4 flex items-center gap-3 text-gray-900 dark:text-white">
              <Server size={22} className="text-blue-500" />
              {t('admin.users.edit.ctms.title')}
            </h4>

            <div className="space-y-3">
              {ctms.length > 0 ? (
                ctms.map((ctm) => (
                  <div
                    key={ctm.id}
                    className={`p-3 rounded-lg border transition-all duration-200 cursor-pointer ${
                      formData.selectedCtms.includes(ctm.id)
                        ? darkMode
                          ? 'bg-blue-600/20 border-blue-500 shadow-md'
                          : 'bg-blue-100 border-blue-400 shadow-md'
                        : darkMode
                          ? 'bg-gray-800 border-gray-600 hover:border-gray-500'
                          : 'bg-white border-gray-300 hover:border-gray-400'
                    }`}
                    onClick={() => handleCtmToggle(ctm.id)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className={`w-4 h-4 rounded border-2 flex items-center justify-center ${
                          formData.selectedCtms.includes(ctm.id)
                            ? 'bg-blue-500 border-blue-500'
                            : darkMode
                              ? 'border-gray-500'
                              : 'border-gray-300'
                        }`}>
                          {formData.selectedCtms.includes(ctm.id) && (
                            <div className="w-2 h-2 bg-white rounded-sm"></div>
                          )}
                        </div>
                        <div>
                          <span className={`font-medium ${
                            darkMode ? 'text-white' : 'text-gray-900'
                          }`}>
                            {ctm.name}
                          </span>
                          {ctm.description && (
                            <p className={`text-sm ${
                              darkMode ? 'text-gray-400' : 'text-gray-600'
                            }`}>
                              {ctm.description}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <p className={`text-center py-4 ${
                  darkMode ? 'text-gray-400' : 'text-gray-600'
                }`}>
                  {t('admin.users.edit.ctms.noCTMs')}
                </p>
              )}
            </div>
          </div>

          {/* Asignación de HSMs */}
          <div className={`p-4 rounded-xl border shadow-sm ${
            darkMode ? 'bg-emerald-900/10 border-emerald-700' : 'bg-emerald-50 border-emerald-200'
          }`}>
            <h4 className="text-lg font-light tracking-wide mb-4 flex items-center gap-3 text-gray-900 dark:text-white">
              <Shield size={22} className="text-emerald-500" />
              {t('admin.users.edit.hsms.title')}
            </h4>

            <div className="space-y-3">
              {hsms.length > 0 ? (
                hsms.map((hsm) => (
                  <div
                    key={hsm.id}
                    className={`p-3 rounded-lg border transition-all duration-200 cursor-pointer ${
                      formData.selectedHsms.includes(hsm.id)
                        ? darkMode
                          ? 'bg-emerald-600/20 border-emerald-500 shadow-md'
                          : 'bg-emerald-100 border-emerald-400 shadow-md'
                        : darkMode
                          ? 'bg-gray-800 border-gray-600 hover:border-gray-500'
                          : 'bg-white border-gray-300 hover:border-gray-400'
                    }`}
                    onClick={() => handleHsmToggle(hsm.id)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className={`w-4 h-4 rounded border-2 flex items-center justify-center ${
                          formData.selectedHsms.includes(hsm.id)
                            ? 'bg-emerald-500 border-emerald-500'
                            : darkMode
                              ? 'border-gray-500'
                              : 'border-gray-300'
                        }`}>
                          {formData.selectedHsms.includes(hsm.id) && (
                            <div className="w-2 h-2 bg-white rounded-sm"></div>
                          )}
                        </div>
                        <div>
                          <span className={`font-medium ${
                            darkMode ? 'text-white' : 'text-gray-900'
                          }`}>
                            {hsm.name}
                          </span>
                          {hsm.description && (
                            <p className={`text-sm ${
                              darkMode ? 'text-gray-400' : 'text-gray-600'
                            }`}>
                              {hsm.description}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <p className={`text-center py-4 ${
                  darkMode ? 'text-gray-400' : 'text-gray-600'
                }`}>
                  {t('admin.users.edit.hsms.noHSMs')}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Footer fijo centrado */}
        <div className="flex justify-center gap-4 mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
          <button
            type="button"
            onClick={onClose}
            disabled={isSaving}
            className="px-8 py-3 rounded-xl bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-light tracking-wide shadow-sm hover:shadow-md transform hover:scale-105 transition-all duration-300 disabled:opacity-50"
          >
            {t('common.cancel')}
          </button>

          <button
            type="submit"
            disabled={isSaving}
            className="px-8 py-3 rounded-xl font-light tracking-wide shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-3"
          >
            <div className="p-1 bg-white/20 rounded-lg group-hover:bg-white/30 transition-all duration-300">
              <Save size={16} className={isSaving ? "animate-spin" : ""} />
            </div>
            <span>
              {isSaving ? t('admin.users.edit.saving') : t('admin.users.edit.saveButton')}
            </span>
          </button>
        </div>
      </form>
    </Modal>
  );
};

UserEditModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  user: PropTypes.object,
  onClose: PropTypes.func.isRequired,
  onSave: PropTypes.func.isRequired,
  darkMode: PropTypes.bool.isRequired,
  ctms: PropTypes.array,
  hsms: PropTypes.array
};

export default UserEditModal;
