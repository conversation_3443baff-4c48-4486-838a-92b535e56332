import { useState, useRef } from 'react';
import { Clock, X, Check, CheckCircle, XCircle, Search, RefreshCw, AlertTriangle, ChevronLeft, ChevronRight } from 'lucide-react';
import PropTypes from 'prop-types';
import { Modal } from '../../common';
import KeyUpdateModal from './KeyUpdateModal';
import { useLanguage } from '../../../hooks';
import { gsap } from 'gsap';

/**
 * Modal para crear nueva versión de una llave madre
 */
const CreateVersionModal = ({ isOpen, onClose, motherKeys, onUpdateKey, darkMode }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [keyToUpdate, setKeyToUpdate] = useState(null);
  const [updatingKeys, setUpdatingKeys] = useState(new Set());
  const [hideCreateVersionModal, setHideCreateVersionModal] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [isAnimating, setIsAnimating] = useState(false);
  const { t, currentLanguage } = useLanguage();

  // Ref para animaciones GSAP
  const tableRef = useRef(null);

  // Configuración de paginación
  const itemsPerPage = 10;

  const handleUpdateKeyClick = (keyId, keyName) => {
    setKeyToUpdate({ id: keyId, name: keyName });
    // Ocultar modal principal temporalmente
    setHideCreateVersionModal(true);
    setTimeout(() => {
      setShowConfirmModal(true);
    }, 150);
  };

  const handleConfirmUpdate = async () => {
    if (!keyToUpdate) return;
    
    // Agregar la llave al set de actualización
    setUpdatingKeys(prev => new Set([...prev, keyToUpdate.id]));
    
    try {
      await onUpdateKey(keyToUpdate.id);
      // Remover la llave del set de actualización después de completar
      setUpdatingKeys(prev => {
        const newSet = new Set(prev);
        newSet.delete(keyToUpdate.id);
        return newSet;
      });
    } catch (error) {
      console.error('Error updating key:', error);
      // Remover la llave del set de actualización en caso de error
      setUpdatingKeys(prev => {
        const newSet = new Set(prev);
        newSet.delete(keyToUpdate.id);
        return newSet;
      });
    }
    
    setShowConfirmModal(false);
    setKeyToUpdate(null);
    // Volver a mostrar el modal principal
    setTimeout(() => {
      setHideCreateVersionModal(false);
    }, 150);
  };

  const handleCancelUpdate = () => {
    setShowConfirmModal(false);
    setKeyToUpdate(null);
    // Volver a mostrar el modal principal
    setTimeout(() => {
      setHideCreateVersionModal(false);
    }, 150);
  };

  const handleClose = () => {
    // Limpiar animaciones GSAP
    if (tableRef.current) {
      gsap.killTweensOf(tableRef.current);
    }
    
    setSearchTerm('');
    setCurrentPage(1);
    setIsAnimating(false);
    setHideCreateVersionModal(false);
    setShowConfirmModal(false);
    setKeyToUpdate(null);
    onClose();
  };

  // Función para obtener el estado con etiqueta
  const getStatusInfo = (key) => {
    if (key.status === 'UPLOADED_TO_CTM' || key.uploadedToCtm === true) {
      return {
        text: 'active',
        className: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300',
        icon: <CheckCircle size={14} />
      };
    }
    if (key.status === 'FAILED' || key.isSuccessful === false) {
      return {
        text: 'inactive',
        className: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300',
        icon: <XCircle size={14} />
      };
    }
    return {
      text: 'inactive',
      className: 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300',
      icon: <Clock size={14} />
    };
  };

  // Función para obtener los bytes reales de la llave
  const getKeyBytes = (key) => {
    // Buscar en diferentes propiedades donde pueden estar los bytes
    return key.num_bytes || key.bytes || key.key_size || key.size || '32'; // Default a 32 si no se encuentra
  };

  // Formatear fecha
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return 'N/A';
      return date.toLocaleDateString(
        currentLanguage === 'es' ? 'es-ES' : 'en-US',
        {
          day: '2-digit',
          month: 'short',
          year: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        }
      );
    } catch {
      return 'N/A';
    }
  };

  // Filtrar llaves por término de búsqueda
  const filteredKeys = searchTerm 
    ? motherKeys.filter(key => 
        key.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        key.algorithm?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : motherKeys;

  // Calcular paginación
  const totalPages = Math.ceil(filteredKeys.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentKeys = filteredKeys.slice(startIndex, endIndex);

  // Función para ir a la página anterior
  const handlePreviousPage = async () => {
    if (currentPage > 1 && !isAnimating) {
      setIsAnimating(true);
      
      // Animación de salida
      if (tableRef.current) {
        await gsap.to(tableRef.current, {
          opacity: 0,
          x: 20,
          duration: 0.2,
          ease: "power2.out"
        });
      }
      
      setCurrentPage(currentPage - 1);
      
      // Animación de entrada
      if (tableRef.current) {
        gsap.fromTo(tableRef.current, 
          { opacity: 0, x: -20 },
          { 
            opacity: 1, 
            x: 0, 
            duration: 0.3, 
            ease: "power2.out",
            delay: 0.1
          }
        );
      }
      
      setTimeout(() => {
        setIsAnimating(false);
      }, 400);
    }
  };

  // Función para ir a la página siguiente
  const handleNextPage = async () => {
    if (currentPage < totalPages && !isAnimating) {
      setIsAnimating(true);
      
      // Animación de salida
      if (tableRef.current) {
        await gsap.to(tableRef.current, {
          opacity: 0,
          x: -20,
          duration: 0.2,
          ease: "power2.out"
        });
      }
      
      setCurrentPage(currentPage + 1);
      
      // Animación de entrada
      if (tableRef.current) {
        gsap.fromTo(tableRef.current, 
          { opacity: 0, x: 20 },
          { 
            opacity: 1, 
            x: 0, 
            duration: 0.3, 
            ease: "power2.out",
            delay: 0.1
          }
        );
      }
      
      setTimeout(() => {
        setIsAnimating(false);
      }, 400);
    }
  };

  // Resetear a la primera página cuando cambia la búsqueda
  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
    setIsAnimating(false);
  };

  return (
    <>
      <Modal
        isOpen={isOpen && !hideCreateVersionModal}
        onClose={handleClose}
        title={
          <div className="flex items-center gap-3">
            <Clock size={24} className="text-yellow-500" />
            <div>
              <h3 className="text-2xl font-light tracking-wide leading-relaxed text-gray-900 dark:text-white">
                {t('keys.versions.createNewVersion.title')}
              </h3>
              <p className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
                {t('keys.versions.createNewVersion.subtitle')}
              </p>
            </div>
          </div>
        }
        maxWidth="max-w-4xl"
        darkMode={darkMode}
      >
        <div className="space-y-4">
          {motherKeys.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-gray-400 mb-4">
                <Clock size={48} className="mx-auto" />
              </div>
              <p className="text-gray-500 dark:text-gray-400 text-sm sm:text-base">
                {t('keys.versions.createNewVersion.noMotherKeys')}
              </p>
            </div>
          ) : (
            <>
              {/* Header con búsqueda */}
              <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
                <div className="flex items-center gap-2 px-3 py-2 rounded-lg bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800">
                  <span className="text-sm font-medium text-yellow-700 dark:text-yellow-300">
                    {t('keys.versions.motherKeys')}
                  </span>
                </div>
                
                <div className="flex-1 relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search size={16} className="text-gray-400" />
                  </div>
                  <input
                    type="text"
                    placeholder={t('keys.versions.searchPlaceholder')}
                    value={searchTerm}
                    onChange={handleSearchChange}
                    className={`w-full pl-10 pr-4 py-2 border rounded-lg text-sm transition-colors ${
                      darkMode
                        ? 'bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:border-yellow-500'
                        : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:border-yellow-500'
                    }`}
                  />
                </div>
              </div>

              {/* Resumen de resultados */}
              <div className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
                {t('keys.versions.availableKeys', { filtered: filteredKeys.length, total: motherKeys.length })}
              </div>

              {/* Controles de paginación SUPERIOR */}
              {filteredKeys.length > itemsPerPage && (
                <div className="flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-6">
                  {/* Información de paginación */}
                  <div className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 text-center sm:text-left">
                    {startIndex + 1}–{Math.min(endIndex, filteredKeys.length)} {t('common.pagination.of')} {filteredKeys.length} {filteredKeys.length === 1 ? t('common.pagination.key') : t('common.pagination.keys')}
                  </div>

                  {/* Controles de navegación */}
                  <div className="flex items-center gap-2">
                    <button
                      onClick={handlePreviousPage}
                      disabled={currentPage === 1 || isAnimating}
                      className={`p-2 rounded-lg border transition-all duration-200 ${
                        currentPage === 1 || isAnimating
                          ? 'border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed dark:border-gray-700 dark:bg-gray-800 dark:text-gray-600'
                          : 'border-gray-200 bg-white text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:scale-105 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                      }`}
                      title="Página anterior"
                    >
                      <ChevronLeft size={16} />
                    </button>

                    <button
                      onClick={handleNextPage}
                      disabled={currentPage === totalPages || isAnimating}
                      className={`p-2 rounded-lg border transition-all duration-200 ${
                        currentPage === totalPages || isAnimating
                          ? 'border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed dark:border-gray-700 dark:bg-gray-800 dark:text-gray-600'
                          : 'border-gray-200 bg-white text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:scale-105 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                      }`}
                      title="Página siguiente"
                    >
                      <ChevronRight size={16} />
                    </button>
                  </div>
                </div>
              )}

              {/* Tabla de llaves madre */}
              <div className="overflow-x-auto" ref={tableRef}>
                <table className="w-full">
                  <thead>
                    <tr className={`border-b ${
                      darkMode ? 'border-gray-700' : 'border-gray-200'
                    }`}>
                      <th className="text-left py-3 px-4 font-light tracking-wide text-gray-700 dark:text-gray-300">
                        {t('keys.details.name')}
                      </th>
                      <th className="text-left py-3 px-4 font-light tracking-wide text-gray-700 dark:text-gray-300">
                        {t('keys.details.algorithm')}
                      </th>
                      <th className="text-left py-3 px-4 font-light tracking-wide text-gray-700 dark:text-gray-300">
                        Bytes
                      </th>
                      <th className="text-left py-3 px-4 font-light tracking-wide text-gray-700 dark:text-gray-300">
                        {t('keys.details.created')}
                      </th>
                      <th className="text-left py-3 px-4 font-light tracking-wide text-gray-700 dark:text-gray-300">
                        {t('keys.details.status')}
                      </th>
                      <th className="text-center py-3 px-4 font-light tracking-wide text-gray-700 dark:text-gray-300">
                        {t('common.actions')}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {currentKeys.map((key) => {
                      const statusInfo = getStatusInfo(key);
                      const keyBytes = getKeyBytes(key);
                      const isUpdating = updatingKeys.has(key.id);
                      
                      return (
                        <tr
                          key={key.id}
                          className={`border-b hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-all duration-300 ease-in-out ${
                            darkMode ? 'border-gray-700' : 'border-gray-200'
                          }`}
                        >
                          <td className="py-3 px-4">
                            <span className="font-light tracking-wide text-gray-900 dark:text-white">
                              {key.name || t('keys.noName')}
                            </span>
                          </td>
                          <td className="py-3 px-4 text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
                            {key.algorithm || key.type || 'N/A'}
                          </td>
                          <td className="py-3 px-4 text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
                            {keyBytes}
                          </td>
                          <td className="py-3 px-4 text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
                            {formatDate(key.createdAt)}
                          </td>
                          <td className="py-3 px-4">
                            <div className={`inline-flex items-center gap-2 px-2 py-1 rounded-full text-xs font-light tracking-wide ${statusInfo.className}`}>
                              {statusInfo.icon}
                              {t(`keys.status.${statusInfo.text}`)}
                            </div>
                          </td>
                          <td className="py-3 px-4">
                            <div className="flex justify-center">
                              <button
                                onClick={() => handleUpdateKeyClick(key.id, key.name)}
                                disabled={isUpdating}
                                className={`p-2 rounded-lg border transition-all duration-300 ease-in-out transform hover:scale-105 ${
                                  isUpdating
                                    ? 'border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed dark:border-gray-700 dark:bg-gray-800 dark:text-gray-600'
                                    : 'border-gray-200 bg-gray-50 text-yellow-500 hover:bg-yellow-50 hover:border-yellow-300 hover:text-yellow-600 dark:border-gray-600 dark:bg-gray-700 dark:text-yellow-400 dark:hover:bg-yellow-900/20 dark:hover:border-yellow-500'
                                }`}
                                title={t('keys.actions.updateKey')}
                              >
                                <RefreshCw size={16} className={isUpdating ? 'animate-spin' : ''} />
                              </button>
                            </div>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>

              {/* Controles de paginación INFERIOR */}
              {filteredKeys.length > itemsPerPage && (
                <div className="flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-6 border-t border-gray-200 dark:border-gray-700 pt-4">
                  {/* Información de paginación */}
                  <div className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 text-center sm:text-left">
                    {startIndex + 1}–{Math.min(endIndex, filteredKeys.length)} {t('common.pagination.of')} {filteredKeys.length} {filteredKeys.length === 1 ? t('common.pagination.key') : t('common.pagination.keys')}
                  </div>

                  {/* Controles de navegación */}
                  <div className="flex items-center gap-2">
                    <button
                      onClick={handlePreviousPage}
                      disabled={currentPage === 1 || isAnimating}
                      className={`p-2 rounded-lg border transition-all duration-200 ${
                        currentPage === 1 || isAnimating
                          ? 'border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed dark:border-gray-700 dark:bg-gray-800 dark:text-gray-600'
                          : 'border-gray-200 bg-white text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:scale-105 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                      }`}
                      title="Página anterior"
                    >
                      <ChevronLeft size={16} />
                    </button>

                    <button
                      onClick={handleNextPage}
                      disabled={currentPage === totalPages || isAnimating}
                      className={`p-2 rounded-lg border transition-all duration-200 ${
                        currentPage === totalPages || isAnimating
                          ? 'border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed dark:border-gray-700 dark:bg-gray-800 dark:text-gray-600'
                          : 'border-gray-200 bg-white text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:scale-105 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                      }`}
                      title="Página siguiente"
                    >
                      <ChevronRight size={16} />
                    </button>
                  </div>
                </div>
              )}

              {/* Mensaje cuando no hay resultados */}
              {filteredKeys.length === 0 && motherKeys.length > 0 && (
                <div className="text-center py-8">
                  <p className="text-sm font-light tracking-wide text-gray-500 dark:text-gray-400">
                    No se encontraron llaves que coincidan con la búsqueda
                  </p>
                </div>
              )}

              {/* Botones de acción */}
              <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
                <button
                  onClick={handleClose}
                  className="px-6 py-2 rounded-xl bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-light tracking-wide shadow-sm hover:shadow-md transform hover:scale-105 active:scale-95 transition-all duration-300"
                >
                  {t('common.close')}
                </button>
              </div>
            </>
          )}
        </div>
      </Modal>

      {/* Modal de confirmación usando KeyUpdateModal */}
      <KeyUpdateModal
        isOpen={showConfirmModal}
        onClose={handleCancelUpdate}
        onConfirm={handleConfirmUpdate}
        keyName={keyToUpdate?.name || ''}
        darkMode={darkMode}
      />
    </>
  );
};

CreateVersionModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  motherKeys: PropTypes.array.isRequired,
  onUpdateKey: PropTypes.func.isRequired,
  darkMode: PropTypes.bool
};

export default CreateVersionModal; 