import React, { useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import { Radar } from 'react-chartjs-2';
import { Chart as ChartJS, RadialLinearScale, PointElement, LineElement, Filler, Tooltip, Legend } from 'chart.js';
import { Shield } from 'lucide-react';
import { gsap } from 'gsap';
import { useLanguage } from '../../../hooks';

ChartJS.register(RadialLinearScale, PointElement, LineElement, Filler, Tooltip, Legend);

const SimpleRadarChart = ({ darkMode, keys = [] }) => {
  const containerRef = useRef(null);
  const chartRef = useRef(null);
  const { t } = useLanguage();

  // Procesar datos reales de algoritmos desde las llaves
  const getAlgorithmData = () => {
    if (!keys || !Array.isArray(keys) || keys.length === 0) {
      return {
        algorithms: ['AES', 'RSA', 'HMAC'],
        usageData: [0, 0, 0]
      };
    }

    // Contar llaves por algoritmo
    const algorithmCount = {};
    keys.forEach(key => {
      const algorithm = key.algorithm || key.type || 'Otro';
      algorithmCount[algorithm] = (algorithmCount[algorithm] || 0) + 1;
    });

    // Definir algoritmos simplificados
    const availableAlgorithms = ['AES', 'RSA', 'HMAC'];
    
    // Mapear algoritmos específicos a los generales
    const algorithmMapping = {
      'AES': 'AES',
      'aes': 'AES', // Agregar minúsculas
      'ARIA': 'AES',
      'aria': 'AES', // Agregar minúsculas
      'RSA': 'RSA',
      'rsa': 'RSA', // Agregar minúsculas
      'HMAC-SHA1': 'HMAC',
      'hmac-sha1': 'HMAC', // Agregar minúsculas
      'HMAC-SHA256': 'HMAC',
      'hmac-sha256': 'HMAC', // Agregar minúsculas
      'HMAC-SHA384': 'HMAC',
      'hmac-sha384': 'HMAC', // Agregar minúsculas
      'HMAC-SHA512': 'HMAC',
      'hmac-sha512': 'HMAC' // Agregar minúsculas
    };

    // Contar con mapeo
    const mappedCount = {};
    keys.forEach(key => {
      const originalAlgorithm = key.algorithm || key.type || 'Otro';
      const mappedAlgorithm = algorithmMapping[originalAlgorithm] || 'Otro';
      mappedCount[mappedAlgorithm] = (mappedCount[mappedAlgorithm] || 0) + 1;
    });

    const algorithms = availableAlgorithms;
    const usageData = algorithms.map(alg => mappedCount[alg] || 0);

    return { algorithms, usageData };
  };

  const { algorithms, usageData } = getAlgorithmData();

  // Animación GSAP solo rotación (solo al montar)
  useEffect(() => {
    if (containerRef.current && chartRef.current) {
      // Configurar estado inicial
      gsap.set(containerRef.current, {
        opacity: 1,
        scale: 1,
        rotation: -5
      });

      gsap.set(chartRef.current, {
        opacity: 0,
        rotation: 10
      });

      // Animación de entrada con timeline
      const tl = gsap.timeline();
      
      tl.to(containerRef.current, {
        rotation: 0,
        duration: 0.8,
        ease: "power2.out"
      })
      .to(chartRef.current, {
        opacity: 1,
        rotation: 0,
        duration: 1.0,
        ease: "back.out(1.1)"
      }, "-=0.5");

      // Cleanup
      return () => {
        tl.kill();
      };
    }
  }, []); // Solo al montar el componente

  const data = {
    labels: algorithms,
    datasets: [
      {
        label: t('dashboard.charts.algorithms.title'),
        data: usageData,
        backgroundColor: darkMode ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.15)',
        borderColor: darkMode ? 'rgba(59, 130, 246, 0.8)' : 'rgba(59, 130, 246, 0.6)',
        borderWidth: 2,
        pointBackgroundColor: darkMode ? '#3b82f6' : '#2563eb',
        pointBorderColor: darkMode ? '#1e40af' : '#1d4ed8',
        pointBorderWidth: 2,
        pointRadius: 5,
        pointHoverRadius: 7,
      },
    ],
  };

  const options = {
    responsive: true,
    plugins: {
      legend: { display: false },
      tooltip: {
        backgroundColor: darkMode ? '#1E293B' : '#fff',
        titleColor: darkMode ? '#fff' : '#1E293B',
        bodyColor: darkMode ? '#F1F5F9' : '#1E293B',
        borderColor: darkMode ? '#334155' : '#E5E7EB',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: false,
        callbacks: {
          label: function(context) {
            return `${context.label}: ${context.raw} ${t('dashboard.charts.algorithms.keys')}`;
          }
        }
      },
    },
    scales: {
      r: {
        beginAtZero: true,
        max: Math.max(...usageData, 5), // Ajustar máximo dinámicamente
        grid: {
          color: darkMode ? 'rgba(51,65,85,0.4)' : '#E5E7EB',
        },
        angleLines: {
          color: darkMode ? 'rgba(51,65,85,0.4)' : '#E5E7EB',
        },
        pointLabels: {
          color: darkMode ? '#CBD5E1' : '#374151',
          font: {
            size: 12,
            weight: 'bold'
          }
        },
        ticks: {
          color: darkMode ? '#9CA3AF' : '#6B7280',
          font: {
            size: 10
          },
          stepSize: Math.ceil(Math.max(...usageData, 5) / 5), // Ajustar step size dinámicamente
          showLabelBackdrop: false
        }
      }
    },
    animation: false,
    maintainAspectRatio: false,
  };

  return (
    <div 
      ref={containerRef}
      className={`p-6 rounded-2xl shadow-xl transition-all duration-300 hover:shadow-2xl hover:scale-[1.02] group ${
        darkMode ? 'bg-gray-800 border border-gray-700 hover:bg-gray-700' : 'bg-white border border-gray-200 hover:bg-gray-50'
      }`}
    >
      <div className="flex items-center gap-3 mb-6">
        <Shield className="text-blue-500 transition-colors duration-300 group-hover:text-blue-400" size={24} />
        <div>
          <h3 className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            {t('dashboard.charts.algorithms.title')}
          </h3>
          <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            {t('dashboard.charts.algorithms.subtitle')}
          </p>
        </div>
      </div>
      
      <div ref={chartRef} className="h-64">
        <Radar data={data} options={options} />
      </div>
      
      {/* Leyenda personalizada */}
      <div className="grid grid-cols-3 gap-3 mt-4">
        {algorithms.map((algorithm, index) => (
          <div key={algorithm} className="text-center transition-all duration-300 group-hover:scale-105">
            <div className="flex items-center justify-center gap-2 mb-1">
              <span className="w-2 h-2 rounded-full bg-blue-500"></span>
              <span className={`text-xs font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                {algorithm}
              </span>
            </div>
            <span className={`text-sm font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              {usageData[index]}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};

SimpleRadarChart.propTypes = {
  darkMode: PropTypes.bool.isRequired,
  keys: PropTypes.array,
};

export default SimpleRadarChart;