import React, { useRef } from 'react';
import PropTypes from 'prop-types';
import { Bar } from 'react-chartjs-2';
import { Chart as ChartJS, BarElement, CategoryScale, LinearScale, Tooltip, Legend } from 'chart.js';
import { Key } from 'lucide-react';

ChartJS.register(BarElement, CategoryScale, LinearScale, Tooltip, Legend);

const KeysBarChart = ({ darkMode, keys = [] }) => {
  const chartRef = useRef(null);

  // Procesar datos reales de los últimos 7 días
  const getLast7DaysData = () => {
    const days = ['Lun', 'Mar', 'Mi<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>áb', 'Dom'];
    const today = new Date();
    const last7Days = [];
    const keysCount = [0, 0, 0, 0, 0, 0, 0];

    // Generar las últimas 7 fechas
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(today.getDate() - i);
      last7Days.push(date);
    }

    // Contar llaves por día
    keys.forEach(key => {
      if (key.createdAt) {
        const keyDate = new Date(key.createdAt);
        last7Days.forEach((day, index) => {
          if (
            keyDate.getDate() === day.getDate() &&
            keyDate.getMonth() === day.getMonth() &&
            keyDate.getFullYear() === day.getFullYear()
          ) {
            keysCount[index]++;
          }
        });
      }
    });

    return { days, keysCount };
  };

  const { days, keysCount: keysGenerated } = getLast7DaysData();

  const data = {
    labels: days,
    datasets: [
      {
        label: 'Llaves Generadas',
        data: keysGenerated,
        backgroundColor: darkMode
          ? 'rgba(59, 130, 246, 0.7)'
          : 'rgba(59, 130, 246, 0.6)',
        borderRadius: 12,
        barPercentage: 0.6,
        categoryPercentage: 0.7,
      },
    ],
  };

  const options = {
    responsive: true,
    plugins: {
      legend: { display: false },
      tooltip: {
        backgroundColor: darkMode ? '#1E293B' : '#fff',
        titleColor: darkMode ? '#fff' : '#1E293B',
        bodyColor: darkMode ? '#F1F5F9' : '#1E293B',
        borderColor: darkMode ? '#334155' : '#E5E7EB',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: false,
      },
    },
    scales: {
      x: {
        grid: { color: darkMode ? 'rgba(51,65,85,0.3)' : '#E5E7EB' },
        ticks: {
          color: darkMode ? '#CBD5E1' : '#334155',
          font: { weight: 'bold' },
        },
      },
      y: {
        beginAtZero: true,
        grid: { color: darkMode ? 'rgba(51,65,85,0.3)' : '#E5E7EB' },
        ticks: {
          color: darkMode ? '#CBD5E1' : '#334155',
          font: { weight: 'bold' },
          stepSize: 1,
        },
      },
    },
    animation: false,
  };

  return (
    <div className={`p-6 rounded-2xl border shadow-lg transition-all duration-300 backdrop-blur-md bg-white/60 dark:bg-gray-800/70 dark:backdrop-blur-md dark:border-gray-700`}>
      <div className="flex items-center gap-3 mb-6">
        <div className={`p-3 rounded-xl ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
          <Key className="text-blue-500" size={24} />
        </div>
        <div>
          <h3 className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            Llaves Generadas (Últimos 7 días)
          </h3>
          <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            Evolución diaria
          </p>
        </div>
      </div>
      <div className="h-64">
        <Bar ref={chartRef} data={data} options={options} />
      </div>
    </div>
  );
};

KeysBarChart.propTypes = {
  darkMode: PropTypes.bool.isRequired,
  keys: PropTypes.array,
};

export default KeysBarChart;