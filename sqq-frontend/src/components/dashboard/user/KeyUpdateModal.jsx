import { RefreshCw, AlertTriangle } from 'lucide-react';
import PropTypes from 'prop-types';
import { Modal } from '../../common';
import { useLanguage } from '../../../hooks';

/**
 * Modal elegante para confirmar actualización de llave
 */
const KeyUpdateModal = ({ isOpen, onClose, onConfirm, keyName, darkMode }) => {
  const { t } = useLanguage();

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={
        <div className="flex items-center gap-3">
          <RefreshCw size={24} className="text-yellow-500" />
          <div>
            <h3 className="text-2xl font-light tracking-wide leading-relaxed text-gray-900 dark:text-white">
              {t('keys.update.title')}
            </h3>
            <p className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
              {t('keys.update.subtitle')}
            </p>
          </div>
        </div>
      }
      maxWidth="max-w-lg"
      darkMode={darkMode}
    >
      {/* Contenido del modal */}
      <div className="space-y-4">
        {/* Mensaje de confirmación */}
        <div className={`p-4 rounded-xl border shadow-sm ${
          darkMode
            ? 'bg-yellow-900/10 border-yellow-700'
            : 'bg-yellow-50 border-yellow-200'
        }`}>
          <div className="flex items-start gap-3">
            <AlertTriangle size={18} className="text-yellow-500 flex-shrink-0" />
            <div className="flex-1">
              <h4 className="text-base font-light tracking-wide text-yellow-700 dark:text-yellow-300 mb-3">
                {t('keys.update.confirmQuestion')}
              </h4>
              <div className={`p-3 rounded-lg border mb-3 ${
                darkMode
                  ? 'bg-gray-800 border-gray-700'
                  : 'bg-white border-gray-200'
              }`}>
                <p className="font-mono text-sm font-light tracking-wide text-gray-900 dark:text-white">
                  "{keyName}"
                </p>
              </div>
              <p className="text-sm font-light tracking-wide text-yellow-600 dark:text-yellow-400">
                {t('keys.update.warningMessage')}
              </p>
              <div className="mt-3 p-3 rounded-lg bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700">
                <p className="text-sm font-light tracking-wide text-blue-700 dark:text-blue-300">
                  <strong>{t('keys.update.whatWillHappen')}</strong>
                </p>
                <ul className="text-xs font-light tracking-wide text-blue-600 dark:text-blue-400 mt-2 space-y-1">
                  <li>• {t('keys.update.consequences.newVersion')}</li>
                  <li>• {t('keys.update.consequences.newVersionActive')}</li>
                  <li>• {t('keys.update.consequences.previousVersionsInactive')}</li>
                  <li>• {t('keys.update.consequences.listWillUpdate')}</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer con botones animados */}
      <div className="flex justify-center gap-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
        <button
          onClick={onClose}
          className="px-8 py-3 rounded-xl bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-light tracking-wide shadow-sm hover:shadow-md transform hover:scale-105 active:scale-95 transition-all duration-300"
        >
          {t('common.cancel')}
        </button>

        <button
          onClick={onConfirm}
          className="px-8 py-3 rounded-xl bg-yellow-600 hover:bg-yellow-700 text-white font-light tracking-wide shadow-lg hover:shadow-xl transform hover:scale-105 active:scale-95 transition-all duration-300 flex items-center gap-2"
        >
          <RefreshCw size={16} />
          {t('keys.update.updateButton')}
        </button>
      </div>
    </Modal>
  );
};

KeyUpdateModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onConfirm: PropTypes.func.isRequired,
  keyName: PropTypes.string.isRequired,
  darkMode: PropTypes.bool.isRequired
};

export default KeyUpdateModal; 