import { useState, useMemo, useEffect, useRef } from 'react';
import { Search, ChevronLeft, ChevronRight, History, Calendar, Clock, CheckCircle, XCircle, Eye, Loader2 } from 'lucide-react';
import PropTypes from 'prop-types';
import { Modal } from '../../common';
import KeyDetailModal from '../admin/KeyDetailModal';
import { useLanguage } from '../../../hooks';
import keyService from '../../../services/keys/keyService.js';
import { gsap } from 'gsap';

/**
 * Modal para mostrar las versiones de una llave
 */
const KeyVersionsModal = ({ isOpen, onClose, keyData, getKeyVersions, darkMode, forceRefresh }) => {
  const { t } = useLanguage();
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [isPreparing, setIsPreparing] = useState(false);
  const [error, setError] = useState(null);
  const [versions, setVersions] = useState([]);
  const [totalVersions, setTotalVersions] = useState(0);
  const [isReady, setIsReady] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const itemsPerPage = 4;
  
  // Refs para animaciones
  const tableRef = useRef(null);
  
  // Estados para el modal de detalle
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedVersion, setSelectedVersion] = useState(null);
  const [hideVersionsModal, setHideVersionsModal] = useState(false);

  // Estado de preparación del modal
  useEffect(() => {
    if (isOpen && keyData?.id) {
      setIsPreparing(true);
      setIsReady(false);
      setError(null);
      
      // Pequeño delay para que el modal se abra suavemente
      const timer = setTimeout(() => {
        loadVersions();
      }, 100);
      
      return () => clearTimeout(timer);
    } else if (!isOpen) {
      // Resetear estados cuando se cierra el modal
      setIsPreparing(false);
      setIsReady(false);
      setVersions([]);
      setTotalVersions(0);
      setError(null);
    }
  }, [isOpen, keyData?.id]);

  // Recargar versiones cuando se detecta una actualización
  useEffect(() => {
    if (forceRefresh && isOpen && keyData?.id && isReady) {
      // Solo recargar si el modal ya está listo
      const timer = setTimeout(() => {
        loadVersions();
      }, 1000);
      
      return () => clearTimeout(timer);
    }
  }, [forceRefresh, isOpen, keyData?.id, isReady]);

  const loadVersions = async () => {
    if (!keyData?.id) return;

    setIsLoading(true);
    setError(null);

    try {
      // Usar la función getKeyVersions del hook si está disponible, sino usar el servicio directamente
      const response = getKeyVersions ? await getKeyVersions(keyData.id) : await keyService.getKeyVersions(keyData.id);
      setVersions(response.versions || []);
      setTotalVersions(response.totalVersions || 0);
      setIsReady(true);
    } catch (error) {
      console.error('Error loading key versions:', error);
      setError('Error al cargar las versiones de la llave');
      setVersions([]);
      setTotalVersions(0);
      setIsReady(true); // Marcar como listo incluso con error
    } finally {
      setIsLoading(false);
      setIsPreparing(false);
    }
  };

  // Transformar datos de versiones para la tabla
  const transformedVersions = useMemo(() => {
    if (!versions || versions.length === 0) return [];

    // Primero ordenar por fecha de creación para obtener los números de versión correctos
    const chronologicallySorted = [...versions].sort((a, b) => {
      const dateA = new Date(a.createdAt || a.updatedAt || 0);
      const dateB = new Date(b.createdAt || b.updatedAt || 0);
      return dateA - dateB; // Más antigua primero
    });

    // Crear un mapa de números de versión basado en el orden cronológico
    const versionNumberMap = new Map();
    chronologicallySorted.forEach((version, index) => {
      versionNumberMap.set(version.id, index + 1); // Empezar en 1
    });

    // Ahora ordenar para mostrar: primero la activa, luego las inactivas por fecha
    const displaySorted = [...versions].sort((a, b) => {
      // Si una es activa y la otra no, la activa va primero
      if (a.active && !b.active) return -1;
      if (!a.active && b.active) return 1;
      
      // Si ambas son activas o ambas inactivas, ordenar por fecha de creación
      const dateA = new Date(a.createdAt || a.updatedAt || 0);
      const dateB = new Date(b.createdAt || b.updatedAt || 0);
      return dateA - dateB;
    });

    return displaySorted.map((version) => {
      const isActive = version.active === true;
      const versionNumber = versionNumberMap.get(version.id); // Usar el número cronológico
      
      // Log para debug
      if (isActive) {
        console.log('Versión activa (primera en lista):', {
          id: version.id,
          name: version.name,
          versionNumber,
          isActive
        });
      }
      
      return {
        id: version.id,
        name: version.name,
        versionNumber, // Mantener el número cronológico
        state: isActive ? t('keys.versions.states.active') : t('keys.versions.states.inactive'),
        creationDate: version.createdAt,
        lastModified: version.updatedAt || version.createdAt,
        activationDate: version.activationDate || null,
        version: version.version
      };
    });
  }, [versions]);

  // Filtrar versiones por término de búsqueda
  const filteredVersions = useMemo(() => {
    if (!searchTerm) return transformedVersions;
    return transformedVersions.filter(version =>
      version.versionNumber && version.versionNumber.toString().toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [searchTerm, transformedVersions]);

  // Calcular paginación
  const totalItems = filteredVersions.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedVersions = filteredVersions.slice(startIndex, endIndex);

  // Formatear fecha
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return 'N/A';
      return date.toLocaleDateString('es-ES', {
        day: '2-digit',
        month: 'short',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return 'N/A';
    }
  };

  // Obtener estado visual
  const getStateInfo = (state) => {
    switch (state) {
      case t('keys.versions.states.active'):
        return {
          className: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300',
          icon: <CheckCircle size={14} />
        };
      case t('keys.versions.states.inactive'):
        return {
          className: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300',
          icon: <XCircle size={14} />
        };
      default:
        return {
          className: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300',
          icon: <Clock size={14} />
        };
    }
  };

  const handleConfirmSelection = () => {
    // Por ahora solo cerramos el modal
    onClose();
  };



  const goToNextPage = async () => {
    if (currentPage < totalPages && !isAnimating) {
      setIsAnimating(true);
      
      // Animación de salida
      if (tableRef.current) {
        await gsap.to(tableRef.current, {
          opacity: 0,
          x: -20,
          duration: 0.2,
          ease: "power2.out"
        });
      }
      
      setCurrentPage(currentPage + 1);
      
      // Animación de entrada
      if (tableRef.current) {
        gsap.fromTo(tableRef.current, 
          { opacity: 0, x: 20 },
          { 
            opacity: 1, 
            x: 0, 
            duration: 0.3, 
            ease: "power2.out",
            delay: 0.1
          }
        );
      }
      
      setTimeout(() => {
        setIsAnimating(false);
      }, 400);
    }
  };

  const goToPrevPage = async () => {
    if (currentPage > 1 && !isAnimating) {
      setIsAnimating(true);
      
      // Animación de salida
      if (tableRef.current) {
        await gsap.to(tableRef.current, {
          opacity: 0,
          x: 20,
          duration: 0.2,
          ease: "power2.out"
        });
      }
      
      setCurrentPage(currentPage - 1);
      
      // Animación de entrada
      if (tableRef.current) {
        gsap.fromTo(tableRef.current, 
          { opacity: 0, x: -20 },
          { 
            opacity: 1, 
            x: 0, 
            duration: 0.3, 
            ease: "power2.out",
            delay: 0.1
          }
        );
      }
      
      setTimeout(() => {
        setIsAnimating(false);
      }, 400);
    }
  };

  // Funciones para manejar el modal de detalle
  const handleViewVersionDetail = (version) => {
    console.log('🔍 [DEBUG] handleViewVersionDetail llamado con version:', version);
    
    // Obtener la versión seleccionada de forma segura
    const selectedVersion = versions?.find(v => v.id === version.id);
    console.log('✅ [DEBUG] Versión seleccionada:', selectedVersion);
    
    if (!selectedVersion) {
      console.error('❌ [DEBUG] No se encontró la versión seleccionada');
      return;
    }

    // Crear un objeto compatible con KeyDetailModal usando SOLO los datos de la versión seleccionada
    const versionKeyData = {
      // Datos específicos de la versión
      id: selectedVersion.id,
      versionId: selectedVersion.id,
      ctmKeyId: selectedVersion.ctmKeyId,
      name: selectedVersion.displayName || selectedVersion.name,
      algorithm: selectedVersion.algorithm,
      numBytes: selectedVersion.numBytes,
      exportable: selectedVersion.exportable,
      entropyReport: selectedVersion.entropyReport,
      active: selectedVersion.active,
      state: selectedVersion.state,
      
      // Fechas específicas de la versión
      createdAt: selectedVersion.createdAt,
      updatedAt: selectedVersion.updatedAt,
      activationDate: selectedVersion.activationDate,
      
      // Estado basado en active field
      status: selectedVersion.active ? 'UPLOADED_TO_CTM' : 'FAILED',
      uploadedToCtm: selectedVersion.uploadedToCtm,
      isSuccessful: selectedVersion.isSuccessful,
      
      // Datos base de la llave (solo para contexto, no para mostrar)
      baseKeyId: keyData.id,
      baseKeyName: keyData.name
    };
    
    console.log('📦 [DEBUG] Datos preparados para KeyDetailModal:', versionKeyData);
    
    setSelectedVersion(versionKeyData);
    // Ocultar modal de versiones temporalmente
    setHideVersionsModal(true);
    setTimeout(() => {
      setShowDetailModal(true);
    }, 150);
  };

  const handleCloseDetailModal = () => {
    setShowDetailModal(false);
    setSelectedVersion(null);
    // Volver a mostrar el modal de versiones
    setTimeout(() => {
      setHideVersionsModal(false);
    }, 150);
  };

  // Reset cuando el modal se cierre completamente
  const handleModalClose = () => {
    // Limpiar animaciones GSAP
    if (tableRef.current) {
      gsap.killTweensOf(tableRef.current);
    }
    
    setHideVersionsModal(false);
    setSelectedVersion(null);
    setShowDetailModal(false);
    setSearchTerm('');
    setCurrentPage(1);
    setError(null);
    setIsPreparing(false);
    setIsReady(false);
    setIsAnimating(false);
    setVersions([]);
    setTotalVersions(0);
    onClose();
  };

  // Solo renderizar el contenido cuando esté listo
  const shouldShowContent = isReady && !isPreparing;

  return (
    <>
    <Modal
      isOpen={isOpen && !hideVersionsModal}
      onClose={handleModalClose}
      title={
        <div className="flex items-center gap-3">
          <History size={24} className="text-purple-500" />
          <div>
            <h3 className="text-2xl font-light tracking-wide leading-relaxed text-gray-900 dark:text-white">
              {t('keys.versions.title', { keyName: keyData?.name || 'Unknown Key' })}
            </h3>
            <p className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
              {t('keys.versions.subtitle')}
            </p>
          </div>
        </div>
      }
      maxWidth="max-w-4xl"
      darkMode={darkMode}
    >
      {/* Estado de carga inicial */}
      {!shouldShowContent && (
        <div className="flex items-center justify-center py-16">
          <div className="flex flex-col items-center gap-4">
            <Loader2 className="animate-spin text-purple-500 dark:text-purple-400" size={40} />
            <p className="text-gray-500 dark:text-gray-400 text-sm font-medium">
              {t('keys.versions.preparing')}
            </p>
          </div>
        </div>
      )}

      {/* Contenido del modal con transición suave */}
      <div className={`transition-all duration-500 ease-in-out ${
        shouldShowContent ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
      }`}>
        {/* Header con búsqueda y filtros */}
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
            <div className="flex items-center gap-2 px-3 py-2 rounded-lg bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800">
              <span className="text-sm font-medium text-purple-700 dark:text-purple-300">
                {t('keys.versions.version')}
              </span>
            </div>
            
            <div className="flex-1 relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search size={16} className="text-gray-400" />
              </div>
              <input
                type="text"
                placeholder={t('keys.versions.searchPlaceholder')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={`w-full pl-10 pr-4 py-2 border rounded-lg text-sm transition-colors ${
                  darkMode
                    ? 'bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:border-purple-500'
                    : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:border-purple-500'
                }`}
              />
            </div>

            
          </div>

          {/* Resumen de resultados */}
          <div className="text-sm text-gray-600 dark:text-gray-400">
            {totalItems} {t('keys.versions.results')} | {totalVersions} {t('keys.versions.totalVersions')}
            {forceRefresh && (
              <span className="ml-2 text-blue-600 dark:text-blue-400 font-medium">
                • {t('keys.versions.reloadingAfterUpdate')}
              </span>
            )}
          </div>

          {/* Mensaje de confirmación de actualización */}
          {forceRefresh && (
            <div className="p-3 rounded-lg bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700">
              <p className="text-sm font-medium text-green-700 dark:text-green-300">
                ✓ {t('keys.versions.keyUpdatedSuccessfully')}
              </p>
              <p className="text-xs text-green-600 dark:text-green-400 mt-1">
                {t('keys.versions.newVersionActive')}
              </p>
            </div>
          )}

          {/* Tabla de versiones */}
          <div className="overflow-x-auto" ref={tableRef}>
            {isLoading || isPreparing ? (
              <div className="text-center py-12">
                <div className="flex flex-col items-center gap-3">
                  <Loader2 className="animate-spin text-purple-500 dark:text-purple-400" size={32} />
                  <p className="text-gray-500 dark:text-gray-400 text-sm">
                    {isLoading ? t('keys.versions.loading') : t('keys.versions.preparing')}
                  </p>
                </div>
              </div>
            ) : error ? (
              <div className="text-center py-8 text-red-500 dark:text-red-400">
                {error}
              </div>
            ) : filteredVersions.length > 0 ? (
            <table className="w-full">
              <thead>
                <tr className={`border-b ${
                  darkMode ? 'border-gray-700' : 'border-gray-200'
                }`}>
                  <th className="text-left py-3 px-4 font-medium text-gray-700 dark:text-gray-300">
                    {t('keys.versions.columns.version')}
                  </th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700 dark:text-gray-300">
                    {t('keys.versions.columns.state')}
                  </th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700 dark:text-gray-300">
                    {t('keys.versions.columns.creationDate')}
                  </th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700 dark:text-gray-300">
                    {t('keys.versions.columns.lastModified')}
                  </th>
                  <th className="text-center py-3 px-4 font-medium text-gray-700 dark:text-gray-300">
                    {t('keys.detail.title')}
                  </th>
                </tr>
              </thead>
              <tbody>
                {paginatedVersions.map((version) => {
                  const stateInfo = getStateInfo(version.state);
                  return (
                    <tr
                      key={version.id}
                      className={`border-b hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors ${
                        darkMode ? 'border-gray-700' : 'border-gray-200'
                      }`}
                    >
                      <td className="py-3 px-4">
                        <span className="font-medium text-gray-900 dark:text-white">
                          {version.versionNumber}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <div className={`inline-flex items-center gap-2 px-2 py-1 rounded-full text-xs font-medium ${stateInfo.className}`}>
                          {stateInfo.icon}
                          {version.state}
                        </div>
                      </td>
                      <td className="py-3 px-4 text-sm text-gray-600 dark:text-gray-400">
                        {formatDate(version.creationDate)}
                      </td>
                      <td className="py-3 px-4 text-sm text-gray-600 dark:text-gray-400">
                        {formatDate(version.lastModified)}
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex justify-center">
                          <button
                            onClick={() => handleViewVersionDetail(version)}
                            className="p-2 border rounded-lg transition-all duration-200 transform border-gray-200 bg-gray-50 text-blue-500 hover:bg-blue-50 hover:border-blue-300 hover:text-blue-600 hover:scale-110 dark:border-gray-600 dark:bg-gray-700 dark:text-blue-400 dark:hover:bg-blue-900/20 dark:hover:border-blue-500"
                            title={t('keys.actions.viewDetail')}
                          >
                            <Eye size={16} />
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500 dark:text-gray-400">
                {keyData ? t('keys.versions.noVersionsFound') : t('keys.versions.noKeyData')}
              </p>
            </div>
          )}
        </div>

        {/* Paginación mejorada */}
        {totalPages > 1 && filteredVersions.length > itemsPerPage && (
          <div className="flex items-center justify-center gap-6 pt-4 border-t border-gray-200 dark:border-gray-600">
            {/* Información de paginación */}
            <div className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-400">
              {startIndex + 1}–{Math.min(endIndex, totalItems)} {t('keys.versions.of')} {totalItems} {totalItems === 1 ? t('keys.versions.versionOf') : t('keys.versions.versionsOf')}
            </div>

            {/* Controles de navegación */}
            <div className="flex items-center gap-2">
              <button
                onClick={goToPrevPage}
                disabled={currentPage === 1 || isAnimating}
                className={`p-2 rounded-lg border transition-all duration-200 ${
                  currentPage === 1 || isAnimating
                    ? 'border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed dark:border-gray-700 dark:bg-gray-800 dark:text-gray-600'
                    : 'border-gray-200 bg-white text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:scale-105 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                }`}
                title={t('keys.versions.previousPage')}
              >
                <ChevronLeft size={16} />
              </button>

              <button
                onClick={goToNextPage}
                disabled={currentPage === totalPages || isAnimating}
                className={`p-2 rounded-lg border transition-all duration-200 ${
                  currentPage === totalPages || isAnimating
                    ? 'border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed dark:border-gray-700 dark:bg-gray-800 dark:text-gray-600'
                    : 'border-gray-200 bg-white text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:scale-105 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                }`}
                title={t('keys.versions.nextPage')}
              >
                <ChevronRight size={16} />
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Footer con botones */}
      <div className="flex justify-end gap-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
        <button
          onClick={onClose}
          className="px-6 py-2 rounded-xl bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-light tracking-wide shadow-sm hover:shadow-md transform hover:scale-105 active:scale-95 transition-all duration-300"
        >
          {t('common.cancel')}
        </button>
        
        <button
          onClick={handleConfirmSelection}
          className="px-6 py-2 rounded-xl bg-purple-600 hover:bg-purple-700 text-white font-light tracking-wide shadow-lg hover:shadow-xl transform hover:scale-105 active:scale-95 transition-all duration-300 flex items-center gap-2"
        >
          {t('common.close')}
        </button>
      </div>
      </div>
    </Modal>

    {/* Modal de detalle de versión */}
    <KeyDetailModal
      isOpen={showDetailModal}
      onClose={handleCloseDetailModal}
      keyData={selectedVersion}
      darkMode={darkMode}
    />
    </>
  );
};

KeyVersionsModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  keyData: PropTypes.object.isRequired,
  getKeyVersions: PropTypes.func,
  darkMode: PropTypes.bool.isRequired,
  forceRefresh: PropTypes.bool
};

export default KeyVersionsModal; 