import React from 'react';
import PropTypes from 'prop-types';
import { Doughnut } from 'react-chartjs-2';
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js';
import { PieChart } from 'lucide-react';

ChartJS.register(ArcElement, Tooltip, Legend);

const KeysDoughnutChart = ({ darkMode, statistics = {} }) => {
  // Usar datos reales de las estadísticas
  const successful = statistics.successful || 0;
  const failed = statistics.failed || 0;
  const uploadedToCtm = statistics.uploadedToCtm || 0;
  const total = successful + failed + uploadedToCtm;

  const data = {
    labels: ['Exitosas', 'Fallidas', 'En CTM'],
    datasets: [
      {
        data: [successful, failed, uploadedToCtm],
        backgroundColor: [
          '#22c55e', // Verde
          '#ef4444', // Rojo
          '#3b82f6', // Azul
        ],
        borderColor: [
          '#166534',
          '#991b1b',
          '#1e40af',
        ],
        borderWidth: 2,
        hoverOffset: 8,
      },
    ],
  };

  const options = {
    cutout: '70%',
    plugins: {
      legend: { display: false },
      tooltip: {
        backgroundColor: darkMode ? '#1E293B' : '#fff',
        titleColor: darkMode ? '#fff' : '#1E293B',
        bodyColor: darkMode ? '#F1F5F9' : '#1E293B',
        borderColor: darkMode ? '#334155' : '#E5E7EB',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: false,
        callbacks: {
          label: function(context) {
            const label = context.label || '';
            const value = context.parsed;
            const total = context.dataset.data.reduce((a, b) => a + b, 0);
            const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
            return `${label}: ${value} (${percentage}%)`;
          }
        }
      },
    },
    animation: false,
    maintainAspectRatio: false,
  };

  return (
    <div className={`p-6 rounded-2xl border shadow-lg transition-all duration-300 backdrop-blur-md bg-white/60 dark:bg-gray-800/70 dark:backdrop-blur-md dark:border-gray-700`}>
      <div className="flex items-center gap-3 mb-6">
        <div className={`p-3 rounded-xl ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
          <PieChart className="text-blue-500" size={24} />
        </div>
        <div>
          <h3 className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            Estado de Llaves
          </h3>
          <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            Distribución actual
          </p>
        </div>
      </div>
      
      <div className="h-64 flex flex-col items-center justify-center">
        <div className="relative w-44 h-44 flex items-center justify-center">
          <Doughnut data={data} options={options} />
          <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-center pointer-events-none select-none">
            <span className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'} block leading-none`}>{total}</span>
            <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'} block mt-1`}>Total</span>
          </div>
        </div>
        
        <div className="flex justify-center gap-4 mt-4">
          <div className="flex items-center gap-1.5">
            <span className="inline-block w-3 h-3 rounded-full bg-green-500"></span>
            <span className={`text-xs ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Exitosas</span>
            <span className={`text-xs font-semibold ${darkMode ? 'text-white' : 'text-gray-900'} ml-1`}>{successful}</span>
          </div>
          <div className="flex items-center gap-1.5">
            <span className="inline-block w-3 h-3 rounded-full bg-red-500"></span>
            <span className={`text-xs ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Fallidas</span>
            <span className={`text-xs font-semibold ${darkMode ? 'text-white' : 'text-gray-900'} ml-1`}>{failed}</span>
          </div>
          <div className="flex items-center gap-1.5">
            <span className="inline-block w-3 h-3 rounded-full bg-blue-500"></span>
            <span className={`text-xs ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>En CTM</span>
            <span className={`text-xs font-semibold ${darkMode ? 'text-white' : 'text-gray-900'} ml-1`}>{uploadedToCtm}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

KeysDoughnutChart.propTypes = {
  darkMode: PropTypes.bool.isRequired,
  statistics: PropTypes.object,
};

export default KeysDoughnutChart;