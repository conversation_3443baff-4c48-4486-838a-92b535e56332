import React, { useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import { Doughnut } from 'react-chartjs-2';
import { Chart as ChartJS, ArcElement, Tooltip, Legend } from 'chart.js';
import { PieChart } from 'lucide-react';
import { gsap } from 'gsap';
import { useLanguage } from '../../../hooks';

ChartJS.register(ArcElement, Tooltip, Legend);

const SimpleDoughnutChart = ({ darkMode, statistics }) => {
  const containerRef = useRef(null);
  const chartRef = useRef(null);
  const counterRef = useRef(null);
  const { t } = useLanguage();

  // Datos reales de las estadísticas o valores por defecto
  const successful = statistics?.successful || 0;
  const failed = statistics?.failed || 0;
  const uploadedToCtm = statistics?.uploadedToCtm || 0;
  const total = statistics?.total || 0; // Usar el total real de las estadísticas

  // Animación GSAP de entrada elegante (solo al montar)
  useEffect(() => {
    if (containerRef.current && chartRef.current && counterRef.current) {
      // Configurar estado inicial
      gsap.set(containerRef.current, {
        opacity: 0,
        y: 30,
        scale: 0.95
      });

      gsap.set(chartRef.current, {
        opacity: 0,
        scale: 0.8,
        rotation: -10
      });

      // Configurar contador en 0 inicial
      counterRef.current.textContent = "0";

      // Animación de entrada elegante con timeline
      const tl = gsap.timeline();
      
      tl.to(containerRef.current, {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: 0.7,
        ease: "back.out(1.2)"
      })
      .to(chartRef.current, {
        opacity: 1,
        scale: 1,
        rotation: 0,
        duration: 0.9,
        ease: "elastic.out(1, 0.8)"
      }, "-=0.6")
      // Animar el contador desde 0 hasta el total
      .to({ value: 0 }, {
        value: total,
        duration: 1.2,
        ease: "power2.out",
        snap: { value: 1 }, // Solo números enteros
        onUpdate: function() {
          if (counterRef.current) {
            counterRef.current.textContent = Math.round(this.targets()[0].value);
          }
        }
      }, "-=0.3"); // Empezar un poco antes de que termine la rotación

      // Cleanup
      return () => {
        tl.kill();
      };
    }
  }, []); // Solo al montar el componente

  const data = {
    labels: [t('dashboard.charts.keyStatus.successful'), t('dashboard.charts.keyStatus.failed'), t('dashboard.charts.keyStatus.inCTM')],
    datasets: [
      {
        data: [successful, failed, uploadedToCtm],
        backgroundColor: ['#22c55e', '#ef4444', '#3b82f6'],
        borderWidth: 2,
        borderColor: darkMode ? '#374151' : '#fff',
      },
    ],
  };

  const options = {
    cutout: '65%',
    plugins: {
      legend: { display: false },
      tooltip: {
        backgroundColor: darkMode ? '#1E293B' : '#fff',
        titleColor: darkMode ? '#fff' : '#1E293B',
        bodyColor: darkMode ? '#F1F5F9' : '#1E293B',
        borderColor: darkMode ? '#334155' : '#E5E7EB',
        borderWidth: 1,
        cornerRadius: 8,
      },
    },
    animation: false,
    maintainAspectRatio: false,
  };

  return (
    <div 
      ref={containerRef}
      className={`p-6 rounded-2xl shadow-xl transition-all duration-300 hover:shadow-2xl hover:scale-[1.02] group ${
        darkMode ? 'bg-gray-800 border border-gray-700 hover:bg-gray-700' : 'bg-white border border-gray-50'
      }`}
    >
      <div className="flex items-center gap-3 mb-6">
        <PieChart className="text-blue-500 transition-colors duration-300 group-hover:text-blue-400" size={24} />
        <div>
          <h3 className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            {t('dashboard.charts.keyStatus.title')}
          </h3>
          <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            {t('dashboard.charts.keyStatus.subtitle')}
          </p>
        </div>
      </div>
      
      <div ref={chartRef} className="h-64 flex flex-col items-center justify-center">
        <div className="relative w-40 h-40">
          <Doughnut data={data} options={options} />
          <div className="absolute inset-0 flex flex-col items-center justify-center">
            <span 
              ref={counterRef}
              className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}
            >
              0
            </span>
            <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              {t('dashboard.charts.keyStatus.total')}
            </span>
          </div>
        </div>
        
        <div className="flex justify-center gap-6 mt-4">
          <div className="text-center transition-all duration-300 group-hover:scale-105">
            <div className="flex items-center gap-2 mb-1">
              <span className="w-3 h-3 rounded-full bg-green-500 transition-all duration-300 group-hover:shadow-lg group-hover:shadow-green-500/50"></span>
              <span className={`text-xs ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                {t('dashboard.charts.keyStatus.successful')}
              </span>
            </div>
            <span className={`text-sm font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              {successful}
            </span>
          </div>
          <div className="text-center transition-all duration-300 group-hover:scale-105">
            <div className="flex items-center gap-2 mb-1">
              <span className="w-3 h-3 rounded-full bg-red-500 transition-all duration-300 group-hover:shadow-lg group-hover:shadow-red-500/50"></span>
              <span className={`text-xs ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                {t('dashboard.charts.keyStatus.failed')}
              </span>
            </div>
            <span className={`text-sm font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              {failed}
            </span>
          </div>
          <div className="text-center transition-all duration-300 group-hover:scale-105">
            <div className="flex items-center gap-2 mb-1">
              <span className="w-3 h-3 rounded-full bg-blue-500 transition-all duration-300 group-hover:shadow-lg group-hover:shadow-blue-500/50"></span>
              <span className={`text-xs ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                {t('dashboard.charts.keyStatus.inCTM')}
              </span>
            </div>
            <span className={`text-sm font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              {uploadedToCtm}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

SimpleDoughnutChart.propTypes = {
  darkMode: PropTypes.bool.isRequired,
  statistics: PropTypes.object,
};

export default SimpleDoughnutChart;