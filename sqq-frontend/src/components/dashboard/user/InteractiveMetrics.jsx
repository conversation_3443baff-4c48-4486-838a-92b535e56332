import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { 
  Zap, 
  Shield, 
  TrendingUp, 
  Clock, 
  Cpu, 
  <PERSON>,
  MousePointer,
  <PERSON>rk<PERSON> 
} from 'lucide-react';


/**
 * Componente de métricas interactivas simplificado
 * Datos locales temporales con animaciones básicas
 */
const InteractiveMetrics = ({ darkMode }) => {
  const [hoveredCard, setHoveredCard] = useState(null);
  const [realTimeData, setRealTimeData] = useState({
    performance: 87.5,
    security: 96.2,
    uptime: 99.8,
    efficiency: 94.1
  });

  // 🌊 DATOS MOCK PARA SIMULACIÓN EN TIEMPO REAL
  const mockRealTimeData = {
    // Simulación de datos cambiantes cada 4 segundos
    iterations: [
      { performance: 87.5, security: 96.2, uptime: 99.8, efficiency: 94.1 },
      { performance: 89.1, security: 95.8, uptime: 99.9, efficiency: 95.3 },
      { performance: 91.3, security: 97.1, uptime: 99.7, efficiency: 93.8 },
      { performance: 88.7, security: 96.5, uptime: 99.8, efficiency: 96.2 },
      { performance: 92.4, security: 94.9, uptime: 99.9, efficiency: 97.1 }
    ],
    currentIndex: 0
  };

  // 📊 Configuración de métricas KPI
  const kpiMetrics = [
    {
      id: 'performance',
      title: 'Rendimiento Cuántico',
      value: realTimeData.performance,
      unit: '%',
      icon: Zap,
      color: 'from-blue-500 to-cyan-500',
      bgColor: darkMode ? 'from-blue-900/20 to-cyan-900/20' : 'from-blue-50 to-cyan-50',
      description: 'Eficiencia de procesamiento',
      target: 90,
      trend: '+2.3%'
    },
    {
      id: 'security',
      title: 'Seguridad Avanzada',
      value: realTimeData.security,
      unit: '%',
      icon: Shield,
      color: 'from-green-500 to-emerald-500',
      bgColor: darkMode ? 'from-green-900/20 to-emerald-900/20' : 'from-green-50 to-emerald-50',
      description: 'Protección de llaves',
      target: 95,
      trend: '+0.8%'
    },
    {
      id: 'uptime',
      title: 'Tiempo Activo',
      value: realTimeData.uptime,
      unit: '%',
      icon: Clock,
      color: 'from-purple-500 to-violet-500',
      bgColor: darkMode ? 'from-purple-900/20 to-violet-900/20' : 'from-purple-50 to-violet-50',
      description: 'Disponibilidad del sistema',
      target: 99.5,
      trend: '+0.1%'
    },
    {
      id: 'efficiency',
      title: 'Eficiencia Global',
      value: realTimeData.efficiency,
      unit: '%',
      icon: Cpu,
      color: 'from-orange-500 to-red-500',
      bgColor: darkMode ? 'from-orange-900/20 to-red-900/20' : 'from-orange-50 to-red-50',
      description: 'Optimización de recursos',
      target: 92,
      trend: '+1.7%'
    }
  ];

  // 🔄 Simulación de datos en tiempo real
  useEffect(() => {
    const interval = setInterval(() => {
      const nextIndex = (mockRealTimeData.currentIndex + 1) % mockRealTimeData.iterations.length;
      mockRealTimeData.currentIndex = nextIndex;
      
      const newData = mockRealTimeData.iterations[nextIndex];
      setRealTimeData(newData);

    }, 4000);

    return () => clearInterval(interval);
  }, []);

  // 🎭 Efectos de hover simples
  const handleCardHover = (cardId, isEntering) => {
    setHoveredCard(isEntering ? cardId : null);
  };

  // 🎯 Componente de tarjeta KPI interactiva
  const KpiCard = ({ metric }) => {
    const circumference = 2 * Math.PI * 35;
    const strokeDasharray = `${(metric.value / 100) * circumference} ${circumference}`;
    const isAboveTarget = metric.value >= metric.target;

    return (
      <div
        className={`kpi-card relative p-6 rounded-2xl border overflow-hidden transition-all duration-300 cursor-pointer ${
          darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
        }`}
        data-card={metric.id}
        onMouseEnter={() => handleCardHover(metric.id, true)}
        onMouseLeave={() => handleCardHover(metric.id, false)}
      >
        {/* Fondo degradado */}
        <div className={`absolute inset-0 bg-gradient-to-br ${metric.bgColor} opacity-50`} />

        {/* Contenido principal */}
        <div className="relative z-10">
          {/* Header con icono y tendencia */}
          <div className="flex items-center justify-between mb-4">
            <div className={`p-3 rounded-xl bg-gradient-to-r ${metric.color}`}>
              <metric.icon size={24} className="text-white" />
            </div>
            <div className="text-right">
              <span className={`text-sm font-medium ${
                isAboveTarget ? 'text-green-500' : 'text-orange-500'
              }`}>
                {metric.trend}
              </span>
              <div className="flex items-center gap-1 mt-1">
                <TrendingUp size={12} className={isAboveTarget ? 'text-green-500' : 'text-orange-500'} />
                <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  vs target
                </span>
              </div>
            </div>
          </div>

          {/* Título y descripción */}
          <div className="mb-4">
            <h3 className={`text-lg font-semibold mb-1 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              {metric.title}
            </h3>
            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              {metric.description}
            </p>
          </div>

          {/* Gráfico circular y valor */}
          <div className="flex items-center justify-between">
            <div className="relative">
              <svg width="80" height="80" className="transform -rotate-90">
                {/* Círculo de fondo */}
                <circle
                  cx="40"
                  cy="40"
                  r="35"
                  fill="none"
                  stroke={darkMode ? '#374151' : '#E5E7EB'}
                  strokeWidth="4"
                />
                {/* Círculo de progreso */}
                <circle
                  cx="40"
                  cy="40"
                  r="35"
                  fill="none"
                  stroke="url(#gradient)"
                  strokeWidth="4"
                  strokeLinecap="round"
                  strokeDasharray={strokeDasharray}
                  strokeDashoffset="0"
                />
                {/* Definición del gradiente */}
                <defs>
                  <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor={metric.color.split(' ')[1]} />
                    <stop offset="100%" stopColor={metric.color.split(' ')[3]} />
                  </linearGradient>
                </defs>
              </svg>

              {/* Valor central */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center">
                  <span className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                    {metric.value.toFixed(1)}
                  </span>
                  <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                    {metric.unit}
                  </span>
                </div>
              </div>
            </div>

            {/* Información adicional */}
            <div className="text-right">
              <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Objetivo: {metric.target}{metric.unit}
              </div>
              <div className={`text-xs mt-1 ${
                isAboveTarget ? 'text-green-500' : 'text-orange-500'
              }`}>
                {isAboveTarget ? '✓ Alcanzado' : '⚠ En progreso'}
              </div>
            </div>
          </div>

          {/* Indicador de tiempo real */}
          <div className="flex items-center gap-2 mt-4 pt-3 border-t border-gray-200 dark:border-gray-600">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
            <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              Datos en tiempo real
            </span>
            <Sparkles size={12} className="text-yellow-500 animate-pulse" />
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Título de la sección */}
      <div className="text-center mb-8">
        <h2 className={`text-3xl font-light tracking-wide mb-2 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
          ⚡ Métricas Interactivas en Tiempo Real
        </h2>
        <p className={`text-lg ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          Monitoreo avanzado de tu ecosistema cuántico
        </p>
        <div className="flex items-center justify-center gap-2 mt-2">
          <Eye size={16} className="text-blue-500" />
          <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            Actualización automática cada 4 segundos
          </span>
        </div>
      </div>

      {/* Grid de métricas KPI */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {kpiMetrics.map((metric) => (
          <KpiCard key={metric.id} metric={metric} />
        ))}
      </div>

      {/* Panel de control en tiempo real */}
      <div className={`p-6 rounded-2xl border ${
        darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
      }`}>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <MousePointer className="text-indigo-500" size={24} />
            <div>
              <h3 className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Centro de Control Interactivo
              </h3>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Interactúa con las métricas para obtener información detallada
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse" />
            <span className={`text-sm font-medium ${darkMode ? 'text-green-400' : 'text-green-600'}`}>
              Sistema Activo
            </span>
          </div>
        </div>

        {/* Información del hover */}
        {hoveredCard && (
          <div className={`p-4 rounded-lg border ${
            darkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'
          }`}>
            <div className="flex items-center gap-3">
              <div className="w-4 h-4 bg-blue-500 rounded-full animate-pulse" />
              <span className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Visualizando: {kpiMetrics.find(m => m.id === hoveredCard)?.title}
              </span>
            </div>
            <p className={`text-sm mt-1 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              Pasa el cursor sobre las tarjetas para explorar datos detallados y tendencias.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

InteractiveMetrics.propTypes = {
  darkMode: PropTypes.bool.isRequired
};

export default InteractiveMetrics;