import { useState, useEffect } from 'react';
import { Plus, Trash2, ChevronLeft, ChevronRight } from 'lucide-react';
import PropTypes from 'prop-types';
import { <PERSON><PERSON>, Loading<PERSON><PERSON>ner, <PERSON><PERSON>r<PERSON><PERSON>t } from '../../common';
import { useLanguage } from '../../../hooks';
import KeyCard from './KeyCard';
import KeyUploadModal from './KeyUploadModal';
import KeyDeleteModal from './KeyDeleteModal';
import KeyUpdateModal from './KeyUpdateModal';
import KeyVersionsModal from './KeyVersionsModal';
import CreateVersionModal from './CreateVersionModal';
import { KeyDetailModal } from '../admin'; // Reutilizamos el modal del admin

/**
 * Componente para gestión de llaves en el UsuarioDashboard
 */
const KeyManagement = ({
  keys,
  isLoading,
  error,
  onClearError,
  onUploadKey,
  onDeleteKey,
  onUpdateKey,
  getKeyVersions,
  darkMode,
  selectedService,
  // Props para paginación del servidor
  currentPage,
  totalPages,
  totalKeys,
  keysPerPage,
  isChangingPage,
  onPageChange,
  onNextPage,
  onPrevPage
}) => {
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showUpdateModal, setShowUpdateModal] = useState(false);
  const [showVersionsModal, setShowVersionsModal] = useState(false);
  const [selectedKey, setSelectedKey] = useState(null);
  const [keyToDelete, setKeyToDelete] = useState(null);
  const [keyToUpdate, setKeyToUpdate] = useState(null);
  const [forceRefreshVersions, setForceRefreshVersions] = useState(false);
  const [keyVersionsInfo, setKeyVersionsInfo] = useState({});
  
  // Estados para el modal de crear nueva versión
  const [showCreateVersionModal, setShowCreateVersionModal] = useState(false);

  // Estados para paginación local (para mostrar 4 llaves por página en el frontend)
  const [localCurrentPage, setLocalCurrentPage] = useState(1);
  const [isAnimating, setIsAnimating] = useState(false);
  const [animationDirection, setAnimationDirection] = useState('');

  const { t } = useLanguage();

  // Resetear página local cuando cambie la página del servidor
  useEffect(() => {
    setLocalCurrentPage(1);
  }, [currentPage]);

  // Filtrar solo las llaves madre (originales, no versiones actualizadas)
  const motherKeys = keys.filter(key => {
    // Una llave es madre si:
    // 1. Tiene un ctmKeyId (está subida a CTM) O es una llave HSM
    // 2. No es una versión actualizada (no tiene isUpdatedVersion = true)
    // 3. Está activa o tiene estado válido
    const isCtmKey = key.ctmKeyId &&
                     (key.status === 'UPLOADED_TO_CTM' || key.status === 'ACTIVE' || key.uploadedToCtm === true);
    const isHsmKey = key.isHsmKey ||
                     (key.hsmId && (key.status === 'uploaded_to_ctm' || key.status === 'ACTIVE' || key.active));

    return (isCtmKey || isHsmKey) && !key.isUpdatedVersion;
  });

  // Configuración de paginación local (10 llaves por página en el frontend)
  const LOCAL_KEYS_PER_PAGE = 10;
  const localTotalPages = Math.ceil(motherKeys.length / LOCAL_KEYS_PER_PAGE);
  const localStartIndex = (localCurrentPage - 1) * LOCAL_KEYS_PER_PAGE;
  const localEndIndex = localStartIndex + LOCAL_KEYS_PER_PAGE;
  const currentKeys = motherKeys.slice(localStartIndex, localEndIndex);
  const localTotalKeys = motherKeys.length;

  const handleUploadKey = async (keyData) => {
    await onUploadKey(keyData);
    setShowUploadModal(false);
  };

  // Función para cargar las versiones de una llave
  const loadKeyVersions = async (keyId) => {
    if (!getKeyVersions || keyVersionsInfo[keyId]) {
      return; // Ya tenemos la información o no hay función disponible
    }

    try {
      // Marcar como cargando para evitar múltiples llamadas
      setKeyVersionsInfo(prev => ({
        ...prev,
        [keyId]: { loading: true }
      }));

      const response = await getKeyVersions(keyId);
      setKeyVersionsInfo(prev => ({
        ...prev,
        [keyId]: response
      }));
    } catch (error) {
      console.error('Error loading key versions:', error);
      // En caso de error, limpiar el estado de carga
      setKeyVersionsInfo(prev => {
        const newInfo = { ...prev };
        delete newInfo[keyId];
        return newInfo;
      });
    }
  };

  // Funciones de paginación con animación
  const handlePageChange = (newPage, direction) => {
    if (isAnimating || newPage === localCurrentPage || newPage < 1 || newPage > localTotalPages) return;

    setIsAnimating(true);
    setAnimationDirection(direction);

    // Cambiar página inmediatamente y luego quitar animación
    setLocalCurrentPage(newPage);

    setTimeout(() => {
      setIsAnimating(false);
      setAnimationDirection('');
    }, 500);
  };

  const goToNextPage = () => {
    if (localCurrentPage < localTotalPages) {
      handlePageChange(localCurrentPage + 1, 'next');
    }
  };

  const goToPrevPage = () => {
    if (localCurrentPage > 1) {
      handlePageChange(localCurrentPage - 1, 'prev');
    }
  };

  const handleDeleteKey = (keyId, keyName) => {
    setKeyToDelete({ id: keyId, name: keyName });
    setShowDeleteModal(true);
  };

  const handleCancelDelete = () => {
    setShowDeleteModal(false);
    setKeyToDelete(null);
  };

  const handleViewDetail = (key) => {
    setSelectedKey(key);
    setShowDetailModal(true);
  };

  const handleCloseDetailModal = () => {
    setShowDetailModal(false);
    setSelectedKey(null);
  };

  const handleUpdateKey = (keyId, keyName) => {
    setKeyToUpdate({ id: keyId, name: keyName });
    setShowUpdateModal(true);
  };

  const handleCancelUpdate = () => {
    setShowUpdateModal(false);
    setKeyToUpdate(null);
  };

  const handleConfirmUpdate = async () => {
    try {
      if (onUpdateKey && keyToUpdate) {
        await onUpdateKey(keyToUpdate.id);
        
        // Limpiar la información de versiones de la llave actualizada para recargarla
        setKeyVersionsInfo(prev => {
          const newInfo = { ...prev };
          delete newInfo[keyToUpdate.id];
          return newInfo;
        });
        
        // Forzar recarga de versiones si el modal está abierto
        if (selectedKey && showVersionsModal) {
          setForceRefreshVersions(true);
          // Resetear el flag después de un tiempo
          setTimeout(() => {
            setForceRefreshVersions(false);
          }, 2000);
        }
      }
      
      setShowUpdateModal(false);
      setKeyToUpdate(null);
    } catch (error) {
      console.error('Error updating key:', error);
    }
  };

  const handleViewVersions = (key) => {
    setSelectedKey(key);
    setShowVersionsModal(true);
  };

  const handleCloseVersionsModal = () => {
    setShowVersionsModal(false);
    setSelectedKey(null);
  };



  // Ajustar página después de eliminar
  const handleConfirmDeleteWithPagination = async () => {
    try {
      await onDeleteKey(keyToDelete.id);
      setShowDeleteModal(false);
      setKeyToDelete(null);

      // Limpiar la información de versiones de la llave eliminada
      setKeyVersionsInfo(prev => {
        const newInfo = { ...prev };
        delete newInfo[keyToDelete.id];
        return newInfo;
      });

      // Ajustar página si es necesario después de eliminar
      const newTotalPages = Math.ceil((keys.length - 1) / LOCAL_KEYS_PER_PAGE);
      if (localCurrentPage > newTotalPages && newTotalPages > 0) {
        setLocalCurrentPage(newTotalPages);
      }
    } catch (error) {
      console.error('Error deleting key:', error);
    }
  };

  // Componente de paginación local (4 llaves por página en el frontend)
  const LocalPaginationControls = () => {
    if (localTotalKeys === 0) return null;

    const startItem = localStartIndex + 1;
    const endItem = Math.min(localEndIndex, localTotalKeys);

    return (
      <div className="flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-6">
                 {/* Información de paginación local */}
         <div className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 text-center sm:text-left">
           {startItem}–{endItem} {t('common.pagination.of')} {localTotalKeys} {localTotalKeys === 1 ? t('common.pagination.key') : t('common.pagination.keys')}
         </div>

        {/* Controles de navegación local */}
        <div className="flex items-center gap-2">
          <button
            onClick={goToPrevPage}
            disabled={localCurrentPage === 1 || isAnimating}
            className={`p-2 rounded-lg border transition-all duration-200 ${
              localCurrentPage === 1 || isAnimating
                ? 'border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed dark:border-gray-700 dark:bg-gray-800 dark:text-gray-600'
                : 'border-gray-200 bg-white text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:scale-105 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
            }`}
            title="Página anterior (local)"
          >
            <ChevronLeft size={16} />
          </button>

          <button
            onClick={goToNextPage}
            disabled={localCurrentPage === localTotalPages || isAnimating}
            className={`p-2 rounded-lg border transition-all duration-200 ${
              localCurrentPage === localTotalPages || isAnimating
                ? 'border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed dark:border-gray-700 dark:bg-gray-800 dark:text-gray-600'
                : 'border-gray-200 bg-white text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:scale-105 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
            }`}
            title="Página siguiente (local)"
          >
            <ChevronRight size={16} />
          </button>
        </div>
      </div>
    );
  };

  // Componente de paginación del servidor
  const ServerPaginationControls = () => {
    if (totalKeys === 0) return null;

    const startItem = ((currentPage - 1) * keysPerPage) + 1;
    const endItem = Math.min(currentPage * keysPerPage, totalKeys);

    return (
      <div className="flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-6 mb-4">
                 {/* Información de paginación del servidor */}
         <div className="text-sm font-light tracking-wide text-gray-600 dark:text-gray-400 text-center sm:text-left">
           {t('common.pagination.page')} {currentPage} {t('common.pagination.ofPages')} {totalPages} ({startItem}–{endItem} {t('common.pagination.of')} {totalKeys} {t('common.pagination.keys')} {t('common.pagination.total')})
         </div>

        {/* Controles de navegación del servidor */}
        <div className="flex items-center gap-2">
          <button
            onClick={onPrevPage}
            disabled={currentPage === 1 || isChangingPage}
            className={`p-2 rounded-lg border transition-all duration-200 ${
              currentPage === 1 || isChangingPage
                ? 'border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed dark:border-gray-700 dark:bg-gray-800 dark:text-gray-600'
                : 'border-gray-200 bg-white text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:scale-105 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
            }`}
            title="Página anterior (servidor)"
          >
            <ChevronLeft size={16} />
          </button>

          <button
            onClick={onNextPage}
            disabled={currentPage === totalPages || isChangingPage}
            className={`p-2 rounded-lg border transition-all duration-200 ${
              currentPage === totalPages || isChangingPage
                ? 'border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed dark:border-gray-700 dark:bg-gray-800 dark:text-gray-600'
                : 'border-gray-200 bg-white text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:scale-105 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
            }`}
            title="Página siguiente (servidor)"
          >
            <ChevronRight size={16} />
          </button>
        </div>
      </div>
    );
  };

  return (
    <>
      {/* Header con botón en la esquina superior derecha */}
      <div className="mb-6 sm:mb-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 sm:gap-6">
          <div className="text-center sm:text-left">
            <h1 className="text-2xl sm:text-3xl font-light tracking-wide leading-relaxed mb-2 text-gray-900 dark:text-white">
              {t('keys.title')}
            </h1>
            <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300 font-light tracking-wide">
              {t('keys.subtitle')}
            </p>
          </div>

          <div className="flex justify-center sm:justify-end gap-3">
            <button
              onClick={() => setShowUploadModal(true)}
              className="flex items-center gap-2 sm:gap-3 px-4 sm:px-6 py-2.5 sm:py-3 rounded-xl font-light tracking-wide shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white text-sm sm:text-base"
            >
              <Plus size={16} />
              <span>{t('keys.uploadNew')}</span>
            </button>
            
            {/* Solo mostrar botón de crear nueva versión si NO es servicio HSM */}
            {selectedService?.type !== 'HSM' && (
              <button
                onClick={() => setShowCreateVersionModal(true)}
                className="flex items-center gap-2 sm:gap-3 px-4 sm:px-6 py-2.5 sm:py-3 rounded-xl font-light tracking-wide shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-white text-sm sm:text-base"
              >
                <Plus size={16} />
                <span>{t('keys.versions.createNewVersion.title')}</span>
              </button>
            )}
          </div>
        </div>
      </div>

      <ErrorAlert error={error} onClose={onClearError} />

      {isLoading ? (
        <LoadingSpinner message={t('keys.loading')} />
      ) : (
        <>
          {localTotalKeys === 0 ? (
            <div className="text-center py-12 sm:py-16">
              <div className="max-w-md mx-auto">
                <div className="text-gray-400 mb-4">
                  <Plus size={48} className="mx-auto" />
                </div>
                <p className="text-gray-500 dark:text-gray-400 text-sm sm:text-base">
                  {t('keys.noKeys')}
                </p>
                <button
                  onClick={() => setShowUploadModal(true)}
                  className="mt-4 text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium text-sm"
                >
                  {t('keys.uploadNew')}
                </button>
              </div>
            </div>
          ) : (
            <>
              {/* Controles de paginación del servidor - ARRIBA */}
              {!isLoading && totalKeys > 0 && totalPages > 1 && (
                <div className="mb-6">
                  <ServerPaginationControls />
                </div>
              )}

              {/* Controles de paginación local - ARRIBA */}
              {!isLoading && localTotalKeys > 0 && localTotalKeys > LOCAL_KEYS_PER_PAGE && (
                <div className="mb-6">
                  <LocalPaginationControls />
                </div>
              )}

                             {/* Contenedor con animaciones para las llaves */}
               <div
                 className={`space-y-3 sm:space-y-4 transition-all duration-500 ease-in-out ${
                   isAnimating
                     ? animationDirection === 'next'
                       ? 'transform translate-x-8 opacity-60'
                       : 'transform -translate-x-8 opacity-60'
                     : 'transform translate-x-0 opacity-100'
                 }`}
               >
                 {currentKeys.map((key) => {
                   // Cargar versiones de la llave si no se han cargado aún
                   // Solo para llaves CTM (las llaves HSM no tienen versiones)
                   if (getKeyVersions && !keyVersionsInfo[key.id] && !key.isHsmKey && key.ctmKeyId) {
                     loadKeyVersions(key.id);
                   }

                   return (
                     <KeyCard
                       key={key.id}
                       keyData={key}
                       darkMode={darkMode}
                       onDelete={() => handleDeleteKey(key.id, key.name)}
                       onViewDetail={handleViewDetail}
                       onUpdate={() => handleUpdateKey(key.id, key.name)}
                       onViewVersions={() => handleViewVersions(key)}
                       disabled={isLoading || isAnimating}
                       versionInfo={keyVersionsInfo[key.id]}
                     />
                   );
                 })}
               </div>

               {/* Controles de paginación local - ABAJO */}
               {!isLoading && localTotalKeys > 0 && localTotalKeys > LOCAL_KEYS_PER_PAGE && (
                 <div className="mt-8">
                   <LocalPaginationControls />
                 </div>
               )}

             </>
           )}
        </>
      )}



      <KeyUploadModal
        isOpen={showUploadModal}
        onClose={() => setShowUploadModal(false)}
        onUpload={handleUploadKey}
        darkMode={darkMode}
        selectedService={selectedService}
      />

      <KeyDetailModal
        isOpen={showDetailModal}
        onClose={handleCloseDetailModal}
        keyData={selectedKey}
        darkMode={darkMode}
      />

      <KeyDeleteModal
        isOpen={showDeleteModal}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDeleteWithPagination}
        keyName={keyToDelete?.name || ''}
        darkMode={darkMode}
      />

      <KeyUpdateModal
        isOpen={showUpdateModal}
        onClose={handleCancelUpdate}
        onConfirm={handleConfirmUpdate}
        keyName={keyToUpdate?.name || ''}
        darkMode={darkMode}
      />

      <KeyVersionsModal
        isOpen={showVersionsModal}
        onClose={handleCloseVersionsModal}
        keyData={selectedKey}
        getKeyVersions={getKeyVersions}
        darkMode={darkMode}
        forceRefresh={forceRefreshVersions}
      />

      <CreateVersionModal
        isOpen={showCreateVersionModal}
        onClose={() => setShowCreateVersionModal(false)}
        motherKeys={motherKeys}
        onUpdateKey={onUpdateKey}
        darkMode={darkMode}
      />
    </>
  );
};

KeyManagement.propTypes = {
  keys: PropTypes.array.isRequired,
  isLoading: PropTypes.bool.isRequired,
  error: PropTypes.string,
  onClearError: PropTypes.func.isRequired,
  onUploadKey: PropTypes.func.isRequired,
  onDeleteKey: PropTypes.func.isRequired,
  onUpdateKey: PropTypes.func,
  getKeyVersions: PropTypes.func,
  darkMode: PropTypes.bool.isRequired,
  selectedService: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.object
  ]),
  // Props para paginación del servidor
  currentPage: PropTypes.number.isRequired,
  totalPages: PropTypes.number.isRequired,
  totalKeys: PropTypes.number.isRequired,
  keysPerPage: PropTypes.number.isRequired,
  isChangingPage: PropTypes.bool.isRequired,
  onPageChange: PropTypes.func.isRequired,
  onNextPage: PropTypes.func.isRequired,
  onPrevPage: PropTypes.func.isRequired
};

export default KeyManagement;
