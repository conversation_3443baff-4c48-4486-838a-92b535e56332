/**
 * Utilidades de validación reutilizables
 * Funciones comunes para validar formularios
 */

export const validationRules = {
  required: (value) => {
    if (!value || (typeof value === 'string' && value.trim() === '')) {
      return 'Este campo es requerido';
    }
    return null;
  },

  email: (value) => {
    if (!value) return null;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(value)) {
      return 'Ingrese un email válido';
    }
    return null;
  },

  minLength: (min) => (value) => {
    if (!value) return null;
    if (value.length < min) {
      return `Debe tener al menos ${min} caracteres`;
    }
    return null;
  },

  maxLength: (max) => (value) => {
    if (!value) return null;
    if (value.length > max) {
      return `No puede tener más de ${max} caracteres`;
    }
    return null;
  },

  password: (value) => {
    if (!value) return null;
    if (value.length < 8) {
      return 'La contraseña debe tener al menos 8 caracteres';
    }
    if (!/^[a-zA-Z0-9!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]+$/.test(value)) {
      return 'La contraseña debe contener solo caracteres alfanuméricos y símbolos';
    }
    return null;
  },

  url: (value) => {
    if (!value) return null;
    try {
      new URL(value);
      return null;
    } catch {
      return 'Ingrese una URL válida';
    }
  },

  number: (value) => {
    if (!value) return null;
    if (isNaN(value)) {
      return 'Debe ser un número válido';
    }
    return null;
  },

  positiveNumber: (value) => {
    if (!value) return null;
    const num = Number(value);
    if (isNaN(num) || num <= 0) {
      return 'Debe ser un número positivo';
    }
    return null;
  },

  range: (min, max) => (value) => {
    if (!value) return null;
    const num = Number(value);
    if (isNaN(num)) {
      return 'Debe ser un número válido';
    }
    if (num < min || num > max) {
      return `Debe estar entre ${min} y ${max}`;
    }
    return null;
  }
};

/**
 * Combina múltiples reglas de validación
 */
export const combineValidations = (...rules) => (value, allValues) => {
  for (const rule of rules) {
    const error = rule(value, allValues);
    if (error) return error;
  }
  return null;
};

/**
 * Validaciones específicas para el dominio de la aplicación
 */
export const appValidations = {
  keyName: combineValidations(
    validationRules.required,
    validationRules.minLength(3),
    validationRules.maxLength(50),
    (value) => {
      if (!/^[a-zA-Z0-9_-]+$/.test(value)) {
        return 'Solo se permiten letras, números, guiones y guiones bajos';
      }
      return null;
    }
  ),

  ctmIpAddress: (value) => {
    if (!value) return null;
    // Validar formato de IP o URL
    if (!/^https?:\/\/.+/.test(value) && !/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}/.test(value)) {
      return 'Ingrese una IP válida o URL con protocolo (http/https)';
    }
    return null;
  },

  apiToken: (value) => {
    if (!value) return null;
    if (value.length < 10) {
      return 'El token debe tener al menos 10 caracteres';
    }
    return null;
  }
};

export default validationRules;
