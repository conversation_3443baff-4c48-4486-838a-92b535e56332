// 📊 SecurityLogger - Sistema centralizado de logging de seguridad

class SecurityLogger {
  constructor() {
    this.logs = [];
    this.maxLogs = 1000; // Máximo de logs en memoria
    this.sessionId = this.generateSessionId();
    this.startTime = Date.now();
    
    // Configuración de niveles de log
    this.levels = {
      INFO: 'INFO',
      WARNING: 'WARNING',
      ERROR: 'ERROR',
      CRITICAL: 'CRITICAL'
    };
    
    // Inicializar logging
    this.initializeLogging();
  }

  // Generar ID único de sesión
  generateSessionId() {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
  }

  // Inicializar sistema de logging
  initializeLogging() {
    // Capturar errores JavaScript no manejados
    window.addEventListener('error', (event) => {
      this.logError('UNHANDLED_ERROR', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack
      });
    });

    // Capturar promesas rechazadas no manejadas
    window.addEventListener('unhandledrejection', (event) => {
      this.logError('UNHANDLED_PROMISE_REJECTION', {
        reason: event.reason,
        promise: event.promise
      });
    });

    // Log inicial del sistema
    this.logInfo('SECURITY_LOGGER_INITIALIZED', {
      sessionId: this.sessionId,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString(),
      viewport: `${window.innerWidth}x${window.innerHeight}`,
      language: navigator.language,
      platform: navigator.userAgentData?.platform || navigator.platform,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine
    });
  }

  // Crear entrada de log
  createLogEntry(level, event, details = {}) {
    const logEntry = {
      id: this.generateLogId(),
      sessionId: this.sessionId,
      timestamp: new Date().toISOString(),
      level: level,
      event: event,
      details: details,
      url: window.location.href,
      userAgent: navigator.userAgent,
      sessionDuration: Date.now() - this.startTime
    };

    // Agregar información adicional según el nivel
    if (level === this.levels.ERROR || level === this.levels.CRITICAL) {
      logEntry.stackTrace = new Error().stack;
    }

    return logEntry;
  }

  // Generar ID único para cada log
  generateLogId() {
    return 'log_' + Date.now() + '_' + Math.random().toString(36).substring(2, 7);
  }

  // Agregar log a la cola
  addLog(logEntry) {
    this.logs.push(logEntry);
    
    // Mantener solo los últimos N logs
    if (this.logs.length > this.maxLogs) {
      this.logs.shift();
    }

    // Mostrar en consola con formato
    this.displayLog(logEntry);
    
    // En producción, enviar al backend
    this.sendToBackend(logEntry);
  }

  // Mostrar log en consola con formato
  displayLog(logEntry) {
    const prefix = `[${logEntry.level}] [${logEntry.sessionId.substr(-8)}]`;
    const message = `${prefix} ${logEntry.event}:`;
    
    switch (logEntry.level) {
      case this.levels.INFO:
        console.log(message, logEntry.details);
        break;
      case this.levels.WARNING:
        console.warn(message, logEntry.details);
        break;
      case this.levels.ERROR:
        console.error(message, logEntry.details);
        break;
      case this.levels.CRITICAL:
        console.error(`🚨 ${message}`, logEntry.details);
        break;
      default:
        console.log(message, logEntry.details);
    }
  }

  // Enviar al backend (simulado)
  sendToBackend() {
    // En un entorno real, esto enviaría los logs al backend de Nicolás
    try {
      // Simular envío al backend
      if (import.meta.env.PROD) {
        // fetch('/api/security-logs', {
        //   method: 'POST',
        //   headers: { 'Content-Type': 'application/json' },
        //   body: JSON.stringify(logEntry)
        // });
      }
    } catch (error) {
      console.error('Failed to send log to backend:', error);
    }
  }

  // Métodos de logging por nivel
  logInfo(event, details = {}) {
    const logEntry = this.createLogEntry(this.levels.INFO, event, details);
    this.addLog(logEntry);
  }

  logWarning(event, details = {}) {
    const logEntry = this.createLogEntry(this.levels.WARNING, event, details);
    this.addLog(logEntry);
  }

  logError(event, details = {}) {
    const logEntry = this.createLogEntry(this.levels.ERROR, event, details);
    this.addLog(logEntry);
  }

  logCritical(event, details = {}) {
    const logEntry = this.createLogEntry(this.levels.CRITICAL, event, details);
    this.addLog(logEntry);
    
    // Para eventos críticos, también alertar al usuario
    if (details.alertUser !== false) {
      this.alertCriticalEvent(event, details);
    }
  }

  // Alertar eventos críticos al usuario
  alertCriticalEvent(event) {
    // Solo mostrar alerta si es realmente crítico
    const criticalEvents = [
      'SECURITY_BREACH_DETECTED',
      'MULTIPLE_FAILED_LOGINS',
      'SUSPICIOUS_ACTIVITY_DETECTED',
      'TOKEN_MANIPULATION_DETECTED'
    ];

    if (criticalEvents.includes(event)) {
      alert(`⚠️ Evento de seguridad detectado: ${event}. La sesión será cerrada por seguridad.`);
    }
  }

  // Métodos específicos de seguridad
  logLoginAttempt(email, success, details = {}) {
    this.logInfo('LOGIN_ATTEMPT', {
      email: email.substring(0, 3) + '***',
      success: success,
      ip: 'frontend-log', // En producción, el backend capturaría la IP real
      ...details
    });
  }

  logLogout(userRole, forced = false) {
    this.logInfo('LOGOUT', {
      userRole: userRole,
      forced: forced,
      sessionDuration: Date.now() - this.startTime
    });
  }

  logUnauthorizedAccess(attemptedRoute, userRole) {
    this.logWarning('UNAUTHORIZED_ACCESS_ATTEMPT', {
      attemptedRoute: attemptedRoute,
      userRole: userRole,
      currentUrl: window.location.href
    });
  }

  logSuspiciousActivity(activityType, details = {}) {
    this.logWarning('SUSPICIOUS_ACTIVITY', {
      activityType: activityType,
      ...details
    });
  }

  logSecurityBreach(breachType, details = {}) {
    this.logCritical('SECURITY_BREACH', {
      breachType: breachType,
      ...details
    });
  }

  // Obtener logs filtrados
  getLogs(level = null, limit = 100) {
    let filteredLogs = this.logs;
    
    if (level) {
      filteredLogs = this.logs.filter(log => log.level === level);
    }
    
    return filteredLogs.slice(-limit);
  }

  // Exportar logs para análisis
  exportLogs() {
    const exportData = {
      sessionId: this.sessionId,
      exportTime: new Date().toISOString(),
      totalLogs: this.logs.length,
      sessionDuration: Date.now() - this.startTime,
      logs: this.logs
    };
    
    return JSON.stringify(exportData, null, 2);
  }

  // Limpiar logs
  clearLogs() {
    this.logInfo('LOGS_CLEARED', {
      clearedCount: this.logs.length
    });
    this.logs = [];
  }

  // Obtener estadísticas de seguridad
  getSecurityStats() {
    const stats = {
      totalLogs: this.logs.length,
      byLevel: {},
      sessionDuration: Date.now() - this.startTime,
      sessionId: this.sessionId
    };

    // Contar logs por nivel
    Object.values(this.levels).forEach(level => {
      stats.byLevel[level] = this.logs.filter(log => log.level === level).length;
    });

    return stats;
  }
}

// Instancia singleton
const securityLogger = new SecurityLogger();

export default securityLogger;
