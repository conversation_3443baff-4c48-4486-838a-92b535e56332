/**
 * Utilidad segura para manejar el último acceso del usuario
 * Usa sessionStorage con encriptación básica para mayor seguridad
 */

// Función simple para encriptar/desencriptar (no es criptografía fuerte, pero es suficiente para fechas)
const simpleEncrypt = (text) => {
  return btoa(encodeURIComponent(text));
};

const simpleDecrypt = (encryptedText) => {
  try {
    return decodeURIComponent(atob(encryptedText));
  } catch {
    return null;
  }
};

// Clave única para esta aplicación
const STORAGE_KEY = 'sqrng_last_access';

/**
 * Obtiene la fecha del último acceso de forma segura
 * @returns {string|null} Fecha del último acceso o null si no existe
 */
export const getLastAccess = () => {
  try {
    const encrypted = sessionStorage.getItem(STORAGE_KEY);
    if (!encrypted) return null;
    
    const decrypted = simpleDecrypt(encrypted);
    if (!decrypted) return null;
    
    // Validar que sea una fecha válida
    const date = new Date(decrypted);
    if (isNaN(date.getTime())) return null;
    
    return decrypted;
  } catch (error) {
    console.warn('Error al obtener último acceso:', error);
    return null;
  }
};

/**
 * Guarda la fecha actual como último acceso de forma segura
 * @param {Date} date - Fecha a guardar (opcional, usa fecha actual por defecto)
 */
export const setLastAccess = (date = new Date()) => {
  try {
    const dateString = date.toISOString();
    const encrypted = simpleEncrypt(dateString);
    sessionStorage.setItem(STORAGE_KEY, encrypted);
  } catch (error) {
    console.warn('Error al guardar último acceso:', error);
  }
};

/**
 * Obtiene la fecha de último acceso para mostrar en la UI
 * Si el usuario accede hoy, muestra la fecha anterior
 * @returns {string} Fecha formateada para mostrar
 */
export const getDisplayLastAccess = () => {
  const lastAccess = getLastAccess();
  const today = new Date();
  
  if (!lastAccess) {
    // Si no hay fecha guardada, usar fecha de hace 2 días como ejemplo
    const twoDaysAgo = new Date();
    twoDaysAgo.setDate(today.getDate() - 2);
    return twoDaysAgo.toISOString();
  }
  
  const lastAccessDate = new Date(lastAccess);
  const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
  const lastAccessStart = new Date(lastAccessDate.getFullYear(), lastAccessDate.getMonth(), lastAccessDate.getDate());
  
  // Si el último acceso fue hoy, buscar la fecha anterior
  if (lastAccessStart.getTime() === todayStart.getTime()) {
    // Buscar en sessionStorage por fechas anteriores
    const previousAccess = findPreviousAccess();
    return previousAccess || lastAccess;
  }
  
  return lastAccess;
};

/**
 * Busca una fecha de acceso anterior en sessionStorage
 * @returns {string|null} Fecha anterior o null si no existe
 */
const findPreviousAccess = () => {
  try {
    // Buscar en sessionStorage por claves que contengan fechas
    const keys = Object.keys(sessionStorage);
    const dateKeys = keys.filter(key => key.includes('access') && key !== STORAGE_KEY);
    
    if (dateKeys.length === 0) {
      // Si no hay fechas anteriores, usar fecha de hace 1 día
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      return yesterday.toISOString();
    }
    
    // Obtener la fecha más reciente que no sea hoy
    const today = new Date();
    const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    
    let mostRecent = null;
    let mostRecentDate = null;
    
    dateKeys.forEach(key => {
      try {
        const encrypted = sessionStorage.getItem(key);
        if (encrypted) {
          const decrypted = simpleDecrypt(encrypted);
          if (decrypted) {
            const date = new Date(decrypted);
            const dateStart = new Date(date.getFullYear(), date.getMonth(), date.getDate());
            
            if (dateStart.getTime() !== todayStart.getTime() && 
                (!mostRecentDate || date > mostRecentDate)) {
              mostRecent = decrypted;
              mostRecentDate = date;
            }
          }
        }
      } catch {
        // Ignorar errores en fechas individuales
      }
    });
    
    return mostRecent;
  } catch (error) {
    console.warn('Error al buscar acceso anterior:', error);
    return null;
  }
};

/**
 * Formatea una fecha para mostrar en la UI
 * @param {string} dateString - Fecha en formato ISO
 * @param {string} locale - Locale para formateo (es-ES por defecto)
 * @returns {string} Fecha formateada
 */
export const formatLastAccess = (dateString, locale = 'es-ES') => {
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return 'N/A';
    
    return date.toLocaleDateString(locale, {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  } catch (error) {
    console.warn('Error al formatear fecha:', error);
    return 'N/A';
  }
}; 