/**
 * Auth Service
 * Servicio para manejo de autenticación
 */

import httpClient from '../core/httpClient.js';
import { API_ENDPOINTS } from '../config/apiConfig.js';
import secureStorage from '../../utils/SecureStorage.js';
import securityLogger from '../../utils/SecurityLogger.js';

class AuthService {
  /**
   * Iniciar sesión
   * @param {string} email - Email del usuario
   * @param {string} password - Contraseña del usuario
   * @returns {Promise<Object>} Datos del usuario y tokens
   */
  async login(email, password) {
    try {
      securityLogger.logInfo('LOGIN_ATTEMPT', {
        email: email.substring(0, 3) + '***',
        timestamp: new Date().toISOString(),
      });

      const response = await httpClient.post(API_ENDPOINTS.AUTH.LOGIN, {
        email,
        password,
      });

      if (response.user && response.accessToken) {
        // Almacenar tokens de forma segura
        this.storeTokens(response.accessToken, response.refreshToken, response.user.role);

        // Almacenar servicios si vienen en la respuesta
        if (response.services) {
          localStorage.setItem('userServices', JSON.stringify(response.services));
          securityLogger.logInfo('SERVICES_STORED', {
            ctmsCount: response.services.ctms?.length || 0,
            hsmsCount: response.services.hsms?.length || 0,
            timestamp: new Date().toISOString(),
          });
        }

        securityLogger.logInfo('LOGIN_SUCCESS', {
          userId: response.user.id,
          role: response.user.role,
          hasServices: !!response.services,
          timestamp: new Date().toISOString(),
        });

        return response;
      }

      throw new Error('Respuesta de login inválida');
    } catch (error) {
      securityLogger.logError('LOGIN_ERROR', {
        email: email.substring(0, 3) + '***',
        error: error.message,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  /**
   * Renovar tokens
   * @param {string} refreshToken - Token de renovación
   * @returns {Promise<Object>} Nuevos tokens y datos del usuario
   */
  async refreshToken(refreshToken) {
    try {
      securityLogger.logInfo('TOKEN_REFRESH_ATTEMPT', {
        timestamp: new Date().toISOString(),
      });

      const response = await httpClient.post(API_ENDPOINTS.AUTH.REFRESH, {
        refreshToken,
      });

      if (response.user && response.accessToken) {
        // Actualizar tokens almacenados
        this.storeTokens(response.accessToken, response.refreshToken, response.user.role);
        
        securityLogger.logInfo('TOKEN_REFRESH_SUCCESS', {
          userId: response.user.id,
          timestamp: new Date().toISOString(),
        });

        return response;
      }

      throw new Error('Respuesta de refresh inválida');
    } catch (error) {
      securityLogger.logError('TOKEN_REFRESH_ERROR', {
        error: error.message,
        timestamp: new Date().toISOString(),
      });
      
      // Si falla el refresh, limpiar tokens y redirigir
      this.logout();
      throw error;
    }
  }

  /**
   * Obtener perfil del usuario autenticado
   * @returns {Promise<Object>} Datos del perfil del usuario
   */
  async getProfile() {
    try {
      securityLogger.logInfo('PROFILE_REQUEST', {
        timestamp: new Date().toISOString(),
      });

      const response = await httpClient.get(API_ENDPOINTS.AUTH.PROFILE);

      securityLogger.logInfo('PROFILE_SUCCESS', {
        userId: response.id,
        timestamp: new Date().toISOString(),
      });

      return response;
    } catch (error) {
      securityLogger.logError('PROFILE_ERROR', {
        error: error.message,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  /**
   * Cambiar contraseña del usuario autenticado
   * @param {string} currentPassword - Contraseña actual
   * @param {string} newPassword - Nueva contraseña
   * @param {string} confirmPassword - Confirmación de nueva contraseña
   * @returns {Promise<Object>} Respuesta del servidor
   */
  async changePassword(currentPassword, newPassword, confirmPassword) {
    try {
      securityLogger.logInfo('CHANGE_PASSWORD_ATTEMPT', {
        timestamp: new Date().toISOString(),
      });

      // Obtener token de autenticación
      const token = secureStorage.getSecure('auth_token') || sessionStorage.getItem('token');

      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await httpClient.post(API_ENDPOINTS.AUTH.CHANGE_PASSWORD, {
        currentPassword,
        newPassword,
        confirmPassword,
      });

      securityLogger.logInfo('CHANGE_PASSWORD_SUCCESS', {
        timestamp: new Date().toISOString(),
      });

      return response;
    } catch (error) {
      securityLogger.logError('CHANGE_PASSWORD_ERROR', {
        error: error.message,
        timestamp: new Date().toISOString(),
      });

      // Preservar información del error del servidor
      const serverError = new Error(error.message);
      serverError.status = error.status;
      serverError.statusCode = error.statusCode;
      throw serverError;
    }
  }

  /**
   * Almacenar tokens de forma segura
   * @param {string} accessToken - Token de acceso
   * @param {string} refreshToken - Token de renovación
   * @param {string} role - Rol del usuario
   */
  storeTokens(accessToken, refreshToken, role) {
    const expiry = Date.now() + (24 * 60 * 60 * 1000); // 24 horas
    const loginTime = Date.now();

    // Almacenar datos encriptados
    secureStorage.setSecure('auth_token', accessToken);
    secureStorage.setSecure('refresh_token', refreshToken);
    secureStorage.setSecure('user_role', role);
    secureStorage.setSecure('token_expiry', expiry);
    secureStorage.setSecure('login_time', loginTime);

    // También almacenar en sessionStorage como backup
    sessionStorage.setItem('token', accessToken);
    sessionStorage.setItem('refreshToken', refreshToken);
    sessionStorage.setItem('userRole', role);
    sessionStorage.setItem('tokenExpiry', expiry.toString());
    sessionStorage.setItem('loginTime', loginTime.toString());

    securityLogger.logInfo('TOKENS_STORED', {
      role: role,
      expiry: new Date(expiry).toISOString(),
      encrypted: true,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Verificar si el token es válido
   * @returns {boolean} True si el token es válido
   */
  isTokenValid() {
    // Verificar primero datos encriptados
    const encryptedToken = secureStorage.getSecure('auth_token');
    const encryptedExpiry = secureStorage.getSecure('token_expiry');

    if (encryptedToken && encryptedExpiry) {
      if (Date.now() > encryptedExpiry) {
        this.logout();
        return false;
      }
      return true;
    }

    // Fallback a sessionStorage
    const token = sessionStorage.getItem('token');
    const expiry = sessionStorage.getItem('tokenExpiry');

    if (!token || !expiry) {
      return false;
    }

    if (Date.now() > parseInt(expiry)) {
      this.logout();
      return false;
    }
    return true;
  }

  /**
   * Obtener rol del usuario
   * @returns {string|null} Rol del usuario o null
   */
  getUserRole() {
    return secureStorage.getSecure('user_role') || sessionStorage.getItem('userRole');
  }

  /**
   * Obtener token de renovación
   * @returns {string|null} Token de renovación o null
   */
  getRefreshToken() {
    return secureStorage.getSecure('refresh_token') || sessionStorage.getItem('refreshToken');
  }

  /**
   * Cerrar sesión
   */
  logout() {
    securityLogger.logInfo('LOGOUT', {
      timestamp: new Date().toISOString(),
    });

    // Limpiar datos encriptados
    secureStorage.removeSecure('auth_token');
    secureStorage.removeSecure('refresh_token');
    secureStorage.removeSecure('user_role');
    secureStorage.removeSecure('token_expiry');
    secureStorage.removeSecure('login_time');

    // Limpiar sessionStorage
    sessionStorage.removeItem('token');
    sessionStorage.removeItem('refreshToken');
    sessionStorage.removeItem('userRole');
    sessionStorage.removeItem('tokenExpiry');
    sessionStorage.removeItem('loginTime');

    // Limpiar servicios almacenados
    localStorage.removeItem('userServices');
    localStorage.removeItem('selectedService');

    // Marcar que fue un logout intencional
    sessionStorage.setItem('wasLoggedOut', 'true');

    // Redirigir al login si estamos en el navegador
    if (typeof window !== 'undefined') {
      window.location.href = '/';
    }
  }

  /**
   * Obtener servicios almacenados del usuario
   * @returns {Object|null} Servicios del usuario o null
   */
  getStoredServices() {
    try {
      const services = localStorage.getItem('userServices');
      return services ? JSON.parse(services) : null;
    } catch (error) {
      console.error('Error parsing stored services:', error);
      localStorage.removeItem('userServices');
      return null;
    }
  }

  /**
   * Renovar token automáticamente si es necesario
   * @returns {Promise<boolean>} True si el token es válido o se renovó exitosamente
   */
  async ensureValidToken() {
    if (this.isTokenValid()) {
      return true;
    }

    const refreshToken = this.getRefreshToken();
    if (!refreshToken) {
      this.logout();
      return false;
    }

    try {
      await this.refreshToken(refreshToken);
      return true;
    } catch {
      this.logout();
      return false;
    }
  }
}

// Exportar instancia singleton
const authService = new AuthService();
export default authService;
