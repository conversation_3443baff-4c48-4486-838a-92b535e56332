/**
 * HSM Service
 * Servicio para manejo de Hardware Security Modules
 */

import httpClient from '../core/httpClient.js';
import { API_ENDPOINTS } from '../config/apiConfig.js';
import securityLogger from '../../utils/SecurityLogger.js';

class HsmService {
  /**
   * Obtener todos los HSMs (Solo Admin)
   * @param {Object} filters - Filtros opcionales
   * @returns {Promise<Array>} Lista de HSMs
   */
  async getAllHsms(filters = {}) {
    try {
      const queryParams = this.buildQueryParams(filters);
      const url = `${API_ENDPOINTS.HSMS.BASE}${queryParams}`;

      securityLogger.logInfo('GET_ALL_HSMS_REQUEST', {
        filters,
        timestamp: new Date().toISOString(),
      });

      const response = await httpClient.get(url);

      // Manejar respuesta paginada del backend
      let hsmsArray = [];
      if (response && response.hsms && Array.isArray(response.hsms)) {
        hsmsArray = response.hsms;
      } else if (Array.isArray(response)) {
        hsmsArray = response;
      }

      securityLogger.logInfo('GET_ALL_HSMS_SUCCESS', {
        totalHsms: hsmsArray.length,
        totalFromBackend: response.total || hsmsArray.length,
        timestamp: new Date().toISOString(),
      });

      return hsmsArray;
    } catch (error) {
      securityLogger.logError('GET_ALL_HSMS_ERROR', {
        error: error.message,
        status: error.status,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  /**
   * Obtener HSMs asignados al usuario autenticado
   * @returns {Promise<Array>} Lista de HSMs del usuario
   */
  async getUserHsms() {
    try {
      securityLogger.logInfo('GET_USER_HSMS_REQUEST', {
        timestamp: new Date().toISOString(),
      });

      const response = await httpClient.get(API_ENDPOINTS.HSMS.USER.MY_HSMS);

      // Manejar respuesta paginada del backend
      let hsmsArray = [];
      if (response && response.hsms && Array.isArray(response.hsms)) {
        hsmsArray = response.hsms;
      } else if (Array.isArray(response)) {
        hsmsArray = response;
      }

      securityLogger.logInfo('GET_USER_HSMS_SUCCESS', {
        totalHsms: hsmsArray.length,
        totalFromBackend: response.total || hsmsArray.length,
        timestamp: new Date().toISOString(),
      });

      return hsmsArray;
    } catch (error) {
      securityLogger.logError('GET_USER_HSMS_ERROR', {
        error: error.message,
        status: error.status,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  /**
   * Obtener HSM por ID
   * @param {string} hsmId - ID del HSM
   * @returns {Promise<Object>} HSM encontrado
   */
  async getHsmById(hsmId) {
    try {
      securityLogger.logInfo('GET_HSM_BY_ID_REQUEST', {
        hsmId,
        timestamp: new Date().toISOString(),
      });

      const response = await httpClient.get(API_ENDPOINTS.HSMS.BY_ID(hsmId));
      
      securityLogger.logInfo('GET_HSM_BY_ID_SUCCESS', {
        hsmId: response.id,
        hsmName: response.name,
        timestamp: new Date().toISOString(),
      });

      return response;
    } catch (error) {
      securityLogger.logError('GET_HSM_BY_ID_ERROR', {
        hsmId,
        error: error.message,
        status: error.status,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  /**
   * Crear nuevo HSM (Solo Admin)
   * @param {Object} hsmData - Datos del HSM
   * @returns {Promise<Object>} HSM creado
   */
  async createHsm(hsmData) {
    try {
      this.validateHsmData(hsmData);

      securityLogger.logInfo('CREATE_HSM_REQUEST', {
        hsmName: hsmData.name,
        timestamp: new Date().toISOString(),
      });

      const response = await httpClient.post(API_ENDPOINTS.HSMS.CREATE, hsmData);
      
      securityLogger.logInfo('CREATE_HSM_SUCCESS', {
        hsmId: response.id,
        hsmName: response.name,
        timestamp: new Date().toISOString(),
      });

      return response;
    } catch (error) {
      securityLogger.logError('CREATE_HSM_ERROR', {
        hsmName: hsmData.name,
        error: error.message,
        status: error.status,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  /**
   * Actualizar HSM (Solo Admin)
   * @param {string} hsmId - ID del HSM
   * @param {Object} hsmData - Datos actualizados del HSM
   * @returns {Promise<Object>} HSM actualizado
   */
  async updateHsm(hsmId, hsmData) {
    try {
      securityLogger.logInfo('UPDATE_HSM_REQUEST', {
        hsmId,
        hsmName: hsmData.name,
        timestamp: new Date().toISOString(),
      });

      const response = await httpClient.patch(API_ENDPOINTS.HSMS.UPDATE(hsmId), hsmData);
      
      securityLogger.logInfo('UPDATE_HSM_SUCCESS', {
        hsmId: response.id,
        hsmName: response.name,
        timestamp: new Date().toISOString(),
      });

      return response;
    } catch (error) {
      securityLogger.logError('UPDATE_HSM_ERROR', {
        hsmId,
        error: error.message,
        status: error.status,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  /**
   * Eliminar HSM (Solo Admin)
   * @param {string} hsmId - ID del HSM
   * @returns {Promise<Object>} Respuesta de eliminación
   */
  async deleteHsm(hsmId) {
    try {
      securityLogger.logInfo('DELETE_HSM_REQUEST', {
        hsmId,
        timestamp: new Date().toISOString(),
      });

      const response = await httpClient.delete(API_ENDPOINTS.HSMS.DELETE(hsmId));
      
      securityLogger.logInfo('DELETE_HSM_SUCCESS', {
        hsmId,
        timestamp: new Date().toISOString(),
      });

      return response;
    } catch (error) {
      securityLogger.logError('DELETE_HSM_ERROR', {
        hsmId,
        error: error.message,
        status: error.status,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  /**
   * Asignar HSMs a un usuario (Solo Admin)
   * @param {string} userId - ID del usuario
   * @param {Array} hsmIds - IDs de los HSMs a asignar
   * @returns {Promise<Object>} Respuesta de asignación
   */
  async assignHsmsToUser(userId, hsmIds) {
    try {
      securityLogger.logInfo('ASSIGN_HSMS_TO_USER_REQUEST', {
        userId,
        hsmIds,
        timestamp: new Date().toISOString(),
      });

      const response = await httpClient.post(
        API_ENDPOINTS.HSMS.ASSIGN_TO_USER(userId),
        { hsmIds }
      );
      
      securityLogger.logInfo('ASSIGN_HSMS_TO_USER_SUCCESS', {
        userId,
        hsmIds,
        timestamp: new Date().toISOString(),
      });

      return response;
    } catch (error) {
      securityLogger.logError('ASSIGN_HSMS_TO_USER_ERROR', {
        userId,
        hsmIds,
        error: error.message,
        status: error.status,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  /**
   * Remover usuario de un HSM (Solo Admin)
   * @param {string} hsmId - ID del HSM
   * @param {string} userId - ID del usuario
   * @returns {Promise<Object>} Respuesta de remoción
   */
  async removeUserFromHsm(hsmId, userId) {
    try {
      securityLogger.logInfo('REMOVE_USER_FROM_HSM_REQUEST', {
        hsmId,
        userId,
        timestamp: new Date().toISOString(),
      });

      const response = await httpClient.delete(
        API_ENDPOINTS.HSMS.REMOVE_USER(hsmId, userId)
      );
      
      securityLogger.logInfo('REMOVE_USER_FROM_HSM_SUCCESS', {
        hsmId,
        userId,
        timestamp: new Date().toISOString(),
      });

      return response;
    } catch (error) {
      securityLogger.logError('REMOVE_USER_FROM_HSM_ERROR', {
        hsmId,
        userId,
        error: error.message,
        status: error.status,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  /**
   * Construir query parameters para filtros
   * @param {Object} filters - Filtros
   * @returns {string} Query string
   */
  buildQueryParams(filters) {
    const params = new URLSearchParams();
    
    Object.keys(filters).forEach(key => {
      if (filters[key] !== undefined && filters[key] !== null && filters[key] !== '') {
        params.append(key, filters[key]);
      }
    });
    
    return params.toString() ? `?${params.toString()}` : '';
  }

  /**
   * Validar datos de HSM
   * @param {Object} hsmData - Datos del HSM
   */
  validateHsmData(hsmData) {
    const errors = [];

    if (!hsmData.name) {
      errors.push('Nombre del HSM es requerido');
    }

    if (!hsmData.url) {
      errors.push('URL del HSM es requerida');
    }

    if (errors.length > 0) {
      throw new Error(`Datos de HSM inválidos: ${errors.join(', ')}`);
    }
  }
}

export default new HsmService();
