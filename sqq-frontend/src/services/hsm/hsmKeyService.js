/**
 * HSM Key Service
 * Servicio para manejo de llaves HSM
 */

import httpClient from '../core/httpClient.js';
import { API_ENDPOINTS } from '../config/apiConfig.js';
import securityLogger from '../../utils/SecurityLogger.js';

class HsmKeyService {
  /**
   * Crear nueva llave HSM
   * @param {Object} keyData - Datos de la llave HSM
   * @returns {Promise<Object>} Llave HSM creada
   */
  async createHsmKey(keyData) {
    try {
      this.validateHsmKeyData(keyData);

      securityLogger.logInfo('CREATE_HSM_KEY_REQUEST', {
        keyType: keyData.keyType,
        hsmId: keyData.hsmId,
        timestamp: new Date().toISOString(),
      });

      const response = await httpClient.post(API_ENDPOINTS.HSM_KEYS.CREATE, keyData);
      
      securityLogger.logInfo('CREATE_HSM_KEY_SUCCESS', {
        keyId: response.id,
        keyType: response.keyType,
        hsmId: response.hsmId,
        timestamp: new Date().toISOString(),
      });

      return response;
    } catch (error) {
      securityLogger.logError('CREATE_HSM_KEY_ERROR', {
        keyType: keyData.keyType,
        hsmId: keyData.hsmId,
        error: error.message,
        status: error.status,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  /**
   * Obtener llaves HSM del usuario autenticado
   * @param {Object} filters - Filtros opcionales
   * @returns {Promise<Array>} Lista de llaves HSM del usuario
   */
  async getUserHsmKeys(filters = {}) {
    try {
      const queryParams = this.buildQueryParams(filters);
      const url = `${API_ENDPOINTS.HSM_KEYS.USER_KEYS}${queryParams}`;

      securityLogger.logInfo('GET_USER_HSM_KEYS_REQUEST', {
        filters,
        timestamp: new Date().toISOString(),
      });

      const response = await httpClient.get(url);
      
      securityLogger.logInfo('GET_USER_HSM_KEYS_SUCCESS', {
        totalKeys: response.length,
        timestamp: new Date().toISOString(),
      });

      return response;
    } catch (error) {
      securityLogger.logError('GET_USER_HSM_KEYS_ERROR', {
        error: error.message,
        status: error.status,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  /**
   * Obtener todas las llaves HSM (Solo Admin)
   * @param {Object} filters - Filtros opcionales
   * @returns {Promise<Array>} Lista de todas las llaves HSM
   */
  async getAllHsmKeys(filters = {}) {
    try {
      const queryParams = this.buildQueryParams(filters);
      const url = `${API_ENDPOINTS.HSM_KEYS.ALL}${queryParams}`;

      securityLogger.logInfo('GET_ALL_HSM_KEYS_REQUEST', {
        filters,
        timestamp: new Date().toISOString(),
      });

      const response = await httpClient.get(url);
      
      securityLogger.logInfo('GET_ALL_HSM_KEYS_SUCCESS', {
        totalKeys: response.length,
        timestamp: new Date().toISOString(),
      });

      return response;
    } catch (error) {
      securityLogger.logError('GET_ALL_HSM_KEYS_ERROR', {
        error: error.message,
        status: error.status,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  /**
   * Obtener llave HSM por ID
   * @param {string} keyId - ID de la llave HSM
   * @returns {Promise<Object>} Llave HSM encontrada
   */
  async getHsmKeyById(keyId) {
    try {
      securityLogger.logInfo('GET_HSM_KEY_BY_ID_REQUEST', {
        keyId,
        timestamp: new Date().toISOString(),
      });

      const response = await httpClient.get(API_ENDPOINTS.HSM_KEYS.BY_ID(keyId));
      
      securityLogger.logInfo('GET_HSM_KEY_BY_ID_SUCCESS', {
        keyId: response.id,
        keyType: response.keyType,
        timestamp: new Date().toISOString(),
      });

      return response;
    } catch (error) {
      securityLogger.logError('GET_HSM_KEY_BY_ID_ERROR', {
        keyId,
        error: error.message,
        status: error.status,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  /**
   * Obtener llaves HSM filtradas por HSM específico
   * @param {string} hsmId - ID del HSM
   * @param {Object} filters - Filtros adicionales
   * @returns {Promise<Array>} Lista de llaves del HSM
   */
  async getKeysByHsm(hsmId, filters = {}) {
    try {
      const queryParams = this.buildQueryParams(filters);
      const url = `${API_ENDPOINTS.HSM_KEYS.BY_HSM(hsmId)}${queryParams}`;

      securityLogger.logInfo('GET_KEYS_BY_HSM_REQUEST', {
        hsmId,
        filters,
        timestamp: new Date().toISOString(),
      });

      const response = await httpClient.get(url);

      securityLogger.logInfo('GET_KEYS_BY_HSM_SUCCESS', {
        hsmId,
        totalKeys: response.length,
        timestamp: new Date().toISOString(),
      });

      return response;
    } catch (error) {
      securityLogger.logError('GET_KEYS_BY_HSM_ERROR', {
        hsmId,
        error: error.message,
        status: error.status,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  /**
   * Construir query parameters para filtros
   * @param {Object} filters - Filtros
   * @returns {string} Query string
   */
  buildQueryParams(filters) {
    const params = new URLSearchParams();
    
    Object.keys(filters).forEach(key => {
      if (filters[key] !== undefined && filters[key] !== null && filters[key] !== '') {
        params.append(key, filters[key]);
      }
    });
    
    return params.toString() ? `?${params.toString()}` : '';
  }

  /**
   * Validar datos de llave HSM
   * @param {Object} keyData - Datos de la llave HSM
   */
  validateHsmKeyData(keyData) {
    const errors = [];

    if (!keyData.keyType) {
      errors.push('Tipo de llave es requerido');
    }

    if (!keyData.hsmId) {
      errors.push('ID del HSM es requerido');
    }

    // Validaciones específicas por tipo de llave
    if (keyData.keyType === 'AES') {
      if (!keyData.keySize || ![128, 192, 256].includes(keyData.keySize)) {
        errors.push('Tamaño de llave AES debe ser 128, 192 o 256 bits');
      }
    } else if (keyData.keyType === 'RSA') {
      if (!keyData.keySize || ![1024, 2048, 3072, 4096].includes(keyData.keySize)) {
        errors.push('Tamaño de llave RSA debe ser 1024, 2048, 3072 o 4096 bits');
      }
    } else if (keyData.keyType === 'EC') {
      if (!keyData.curve || !['P-256', 'P-384', 'P-521'].includes(keyData.curve)) {
        errors.push('Curva EC debe ser P-256, P-384 o P-521');
      }
    }

    if (errors.length > 0) {
      throw new Error(`Datos de llave HSM inválidos: ${errors.join(', ')}`);
    }
  }
}

export default new HsmKeyService();
