# SQQ Frontend Services

Esta documentación describe la implementación modular de los servicios para conectar con la API SQQ Backend.

## 📁 Estructura de Servicios

```
src/services/
├── config/
│   └── apiConfig.js          # Configuración de API y endpoints
├── core/
│   └── httpClient.js         # Cliente HTTP con autenticación
├── auth/
│   └── authService.js        # Servicio de autenticación
├── users/
│   └── userService.js        # Servicio de usuarios
├── keys/
│   └── keyService.js         # Servicio de llaves
└── index.js                  # Exportaciones centralizadas
```

## 🔧 Configuración

### Variables de Entorno (.env)

```env
VITE_API_BASE_URL=http://localhost:3000
VITE_API_TIMEOUT=30000
VITE_NODE_ENV=development
```

### Configuración de API

```javascript
import { API_CONFIG, API_ENDPOINTS } from './services/config/apiConfig.js';

// Configuración base
console.log(API_CONFIG.baseURL); // http://localhost:3000

// Endpoints disponibles
console.log(API_ENDPOINTS.AUTH.LOGIN); // /auth/login
console.log(API_ENDPOINTS.USERS.BASE); // /users
console.log(API_ENDPOINTS.KEYS.UPLOAD_TO_CTM); // /keys/upload-to-ctm
```

## 🚀 Uso de Servicios

### 1. Autenticación

```javascript
import { authService } from './services/index.js';

// Login
try {
  const response = await authService.login('<EMAIL>', 'password');
  console.log('Usuario:', response.user);
  console.log('Token:', response.accessToken);
} catch (error) {
  console.error('Error de login:', error.message);
}

// Obtener perfil
const profile = await authService.getProfile();

// Verificar token válido
const isValid = authService.isTokenValid();

// Logout
authService.logout();
```

### 2. Gestión de Usuarios (Solo Admin)

```javascript
import { userService } from './services/index.js';

// Obtener todos los usuarios
const users = await userService.getAllUsers();

// Crear usuario
const newUser = await userService.createUser({
  email: '<EMAIL>',
  firstName: 'Juan',
  lastName: 'Pérez',
  company: 'Acme Corp',
  password: 'password123',
  role: 'USER',
  ctmIpAddress: 'https://ctm.example.com:443',
  ctmUsername: 'ctm_user',
  ctmPassword: 'ctm_pass',
  ctmDomain: 'root',
  seqrngIpAddress: 'https://seqrng.example.com:1982',
  seqrngApiToken: 'token123'
});

// Actualizar usuario
const updatedUser = await userService.updateUser('user-id', {
  firstName: 'Juan Carlos',
  isActive: false
});
```

### 3. Gestión de Llaves

```javascript
import { keyService } from './services/index.js';

// Subir llave a CTM
const keyResponse = await keyService.uploadToCTM({
  key_name: 'my-encryption-key-001',
  algorithm: 'AES',
  num_bytes: 32,
  exportable: false,
  key_material_base64: 'base64-encoded-key' // Opcional
});

// Obtener llaves de usuario con filtros
const keysResponse = await keyService.getKeysByUser('user-id', {
  page: 1,
  limit: 10,
  algorithm: 'AES',
  status: 'UPLOADED_TO_CTM',
  uploadedToCtm: true,
  search: 'encryption'
});

// Obtener estadísticas
const stats = await keyService.getKeyStatistics('user-id');
console.log('Total:', stats.total);
console.log('Exitosas:', stats.successful);
console.log('Fallidas:', stats.failed);
```

## 🎣 Hooks de React

### useAuth Hook

```javascript
import { useAuth } from './hooks/useAuth.js';

function MyComponent() {
  const { 
    user, 
    isAuthenticated, 
    isLoading, 
    error, 
    login, 
    logout, 
    hasRole, 
    isAdmin 
  } = useAuth();

  const handleLogin = async () => {
    try {
      await login('<EMAIL>', 'password');
    } catch (error) {
      console.error('Login failed:', error);
    }
  };

  if (isLoading) return <div>Cargando...</div>;
  if (!isAuthenticated) return <LoginForm onLogin={handleLogin} />;

  return (
    <div>
      <h1>Bienvenido, {user.firstName}!</h1>
      {isAdmin() && <AdminPanel />}
      <button onClick={logout}>Cerrar Sesión</button>
    </div>
  );
}
```

### useUsers Hook

```javascript
import { useUsers } from './hooks/useUsers.js';

function UserManagement() {
  const { 
    users, 
    isLoading, 
    error, 
    getAllUsers, 
    createUser, 
    updateUser 
  } = useUsers();

  useEffect(() => {
    getAllUsers();
  }, [getAllUsers]);

  const handleCreateUser = async (userData) => {
    try {
      await createUser(userData);
      // Usuario se agrega automáticamente a la lista
    } catch (error) {
      console.error('Error creating user:', error);
    }
  };

  return (
    <div>
      {isLoading && <div>Cargando usuarios...</div>}
      {error && <div>Error: {error}</div>}
      {users.map(user => (
        <UserCard key={user.id} user={user} />
      ))}
    </div>
  );
}
```

### useKeys Hook

```javascript
import { useKeys } from './hooks/useKeys.js';

function KeyManagement() {
  const { 
    keys, 
    statistics, 
    isLoading, 
    error, 
    uploadToCTM, 
    getKeysByUser 
  } = useKeys();

  const handleUploadKey = async (keyData) => {
    try {
      const result = await uploadToCTM(keyData);
      console.log('Llave subida:', result);
    } catch (error) {
      console.error('Error uploading key:', error);
    }
  };

  return (
    <div>
      {statistics && (
        <div>
          <p>Total: {statistics.total}</p>
          <p>Exitosas: {statistics.successful}</p>
          <p>En CTM: {statistics.uploadedToCtm}</p>
        </div>
      )}
      {keys.map(key => (
        <KeyCard key={key.id} keyData={key} />
      ))}
    </div>
  );
}
```

## 🔒 Seguridad

### Manejo de Tokens

- Los tokens se almacenan de forma segura usando `SecureStorage`
- Renovación automática de tokens cuando es necesario
- Limpieza automática en caso de tokens expirados
- Logging de eventos de seguridad

### Validación de Datos

- Validación de entrada en todos los servicios
- Sanitización de datos antes del envío
- Manejo de errores HTTP específicos
- Timeouts configurables para peticiones

## 🚨 Manejo de Errores

```javascript
import { HttpError } from './services/core/httpClient.js';

try {
  await userService.createUser(userData);
} catch (error) {
  if (error instanceof HttpError) {
    switch (error.status) {
      case 401:
        console.log('No autorizado');
        break;
      case 403:
        console.log('Acceso denegado');
        break;
      case 409:
        console.log('Conflicto - usuario ya existe');
        break;
      default:
        console.log('Error HTTP:', error.message);
    }
  } else {
    console.log('Error de red:', error.message);
  }
}
```

## 📊 Logging y Monitoreo

Todos los servicios incluyen logging automático de:
- Peticiones HTTP realizadas
- Respuestas recibidas
- Errores encontrados
- Eventos de autenticación
- Operaciones de usuarios y llaves

Los logs se integran con `SecurityLogger` para un monitoreo centralizado.

## 🔄 Próximos Pasos

1. Implementar endpoints faltantes (eliminar usuario, etc.)
2. Agregar cache para mejorar rendimiento
3. Implementar retry automático para peticiones fallidas
4. Agregar más validaciones específicas del dominio
5. Implementar WebSocket para actualizaciones en tiempo real
