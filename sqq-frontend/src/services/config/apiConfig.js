/**
 * API Configuration
 * Configuración centralizada para todas las llamadas a la API
 */

export const API_CONFIG = {
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000',
  timeout: parseInt(import.meta.env.VITE_API_TIMEOUT) || 30000,
  headers: {
    'Content-Type': 'application/json',
  },
};

export const API_ENDPOINTS = {
  // Auth endpoints
  AUTH: {
    LOGIN: '/auth/login',
    REFRESH: '/auth/refresh',
    PROFILE: '/auth/profile',
    CHANGE_PASSWORD: '/auth/change-password',
  },

  // Users endpoints
  USERS: {
    BASE: '/users',
    BY_ID: (id) => `/users/${id}`,
    CREATE: '/users',
    UPDATE: (id) => `/users/${id}`,
    DELETE: (id) => `/users/${id}`,
    STATS: '/users/stats',
  },

  // CTMs endpoints
  CTMS: {
    BASE: '/ctms',
    BY_ID: (id) => `/ctms/${id}`,
    CREATE: '/ctms',
    UPDATE: (id) => `/ctms/${id}`,
    DELETE: (id) => `/ctms/${id}`,
    ASSIGN_USERS: (id) => `/ctms/${id}/assign-users`,
    ASSIGN_TO_USER: (userId) => `/ctms/assign-to-user/${userId}`,
    REMOVE_FROM_USER: (ctmId, userId) => `/ctms/${ctmId}/remove-from-user/${userId}`,
    USER_CTMS: (userId) => `/ctms/user/${userId}`,
    // Endpoints para usuarios
    USER: {
      MY_CTMS: '/user/ctms',
    },
  },

  // HSMs endpoints
  HSMS: {
    BASE: '/hsms',
    BY_ID: (id) => `/hsms/${id}`,
    CREATE: '/hsms',
    UPDATE: (id) => `/hsms/${id}`,
    DELETE: (id) => `/hsms/${id}`,
    ASSIGN_USERS: (id) => `/hsms/${id}/assign-users`,
    ASSIGN_TO_USER: (userId) => `/hsms/assign-to-user/${userId}`,
    REMOVE_USER: (id, userId) => `/hsms/${id}/users/${userId}`,
    USER_HSMS: (userId) => `/hsms/user/${userId}`,
    // Endpoints para usuarios
    USER: {
      MY_HSMS: '/user/hsms',
    },
  },

  // HSM Keys endpoints
  HSM_KEYS: {
    BASE: '/hsm-keys',
    BY_ID: (id) => `/hsm-keys/${id}`,
    BY_HSM: (hsmId) => `/hsm-keys/by-hsm/${hsmId}`,
    CREATE: '/hsm-keys',
    ALL: '/hsm-keys/all',
    USER_KEYS: '/hsm-keys',
  },

  // Keys endpoints
  KEYS: {
    BASE: '/keys',
    UPLOAD_TO_CTM: '/keys/upload-to-ctm',
    BY_USER: (id) => `/keys/by-user/${id}`,
    BY_CTM: '/keys/by-ctm',
    BY_HSM: '/keys/by-hsm',
    BY_ID: (id) => `/keys/${id}`,
    DELETE: (id) => `/keys/${id}`,
    UPDATE: (id) => `/keys/${id}`,
    GENERATE_BYTES: '/keys/generate/bytes',
    GENERATE_HEX: '/keys/generate/hex',
    GENERATE_ALPHANUMERIC: '/keys/generate/alphanumeric',
    UPLOAD_BATCH: '/keys/upload-batch-to-ctm',
    CHECK_EXISTS: (keyName) => `/keys/ctm/${keyName}/exists`,
    GET_CTM_TOKEN: '/keys/ctm/auth/token',
    USER_KEYS: '/keys/user',
    UPDATE_KEY_VERSIONS: (keyId) => `/keys/ctm/${keyId}/versions`,
    GET_VERSIONS: (keyId) => `/keys/versions/${keyId}`,
    SYNCHRONIZE: '/keys/synchronize',
    GET_KEY_INFO: (keyId) => `/keys/ctm/${keyId}/info`,
  },
};

export default API_CONFIG;
