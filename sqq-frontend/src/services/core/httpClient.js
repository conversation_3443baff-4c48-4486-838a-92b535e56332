/**
 * HTTP Client
 * Cliente HTTP centralizado con manejo de autenticación, interceptores y errores
 */

import { API_CONFIG } from '../config/apiConfig.js';
import secureStorage from '../../utils/SecureStorage.js';
import securityLogger from '../../utils/SecurityLogger.js';

class HttpClient {
  constructor() {
    this.baseURL = API_CONFIG.baseURL;
    this.timeout = API_CONFIG.timeout;
    this.defaultHeaders = API_CONFIG.headers;
  }

  /**
   * Obtiene el token de autenticación
   */
  getAuthToken() {
    // Intentar obtener token encriptado primero
    const encryptedToken = secureStorage.getSecure('auth_token');
    if (encryptedToken) {
      return encryptedToken;
    }
    
    // Fallback a sessionStorage
    return sessionStorage.getItem('token');
  }

  /**
   * Obtiene headers con autenticación
   */
  getAuthHeaders() {
    const token = this.getAuthToken();
    const headers = { ...this.defaultHeaders };
    
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
    
    return headers;
  }

  /**
   * Maneja errores de respuesta HTTP
   */
  async handleResponse(response, url, method) {
    const responseData = {
      status: response.status,
      statusText: response.statusText,
      url,
      method,
    };

    // Log de la respuesta
    securityLogger.logInfo('HTTP_RESPONSE', {
      ...responseData,
      timestamp: new Date().toISOString(),
    });

    if (!response.ok) {
      let errorData;
      try {
        // Intentar parsear como JSON
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          errorData = await response.json();
        } else {
          // Si no es JSON, crear un error estándar
          errorData = {
            message: response.statusText || 'Error de autenticación',
            details: 'El servidor respondió con formato no-JSON'
          };
        }
      } catch (parseError) {
        errorData = {
          message: response.statusText || 'Error de autenticación',
          details: `Error parseando respuesta del servidor: ${parseError.message}`
        };
      }

      // Log de error
      securityLogger.logError('HTTP_ERROR', {
        ...responseData,
        error: errorData,
        timestamp: new Date().toISOString(),
      });

      // Manejar errores específicos
      if (response.status === 401) {
        // No redirigir si es un error de cambio de contraseña
        if (!url.includes('/auth/change-password')) {
          this.handleUnauthorized();
        }
      }

      throw new HttpError(response.status, errorData.message || response.statusText, errorData);
    }

    try {
      return await response.json();
    } catch {
      return null;
    }
  }

  /**
   * Maneja errores 401 (no autorizado)
   */
  handleUnauthorized() {
    securityLogger.logWarning('UNAUTHORIZED_ACCESS', {
      timestamp: new Date().toISOString(),
      action: 'clearing_tokens',
    });

    // Solo limpiar tokens si ya estamos autenticados
    // No limpiar durante intentos de login fallidos
    const currentToken = this.getAuthToken();
    if (currentToken) {
      // Limpiar tokens solo si había una sesión activa
      secureStorage.removeSecure('auth_token');
      secureStorage.removeSecure('user_role');
      secureStorage.removeSecure('token_expiry');
      secureStorage.removeSecure('login_time');

      sessionStorage.removeItem('token');
      sessionStorage.removeItem('userRole');
      sessionStorage.removeItem('tokenExpiry');
      sessionStorage.removeItem('loginTime');

      // Solo redirigir si estamos en una ruta protegida (no en login)
      if (typeof window !== 'undefined' && window.location.pathname !== '/') {
        window.location.href = '/';
      }
    }
  }

  /**
   * Realiza una petición HTTP
   */
  async request(url, options = {}) {
    const fullUrl = `${this.baseURL}${url}`;
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const config = {
        ...options,
        headers: {
          ...this.getAuthHeaders(),
          ...options.headers,
        },
        signal: controller.signal,
      };

      // Log de la petición
      securityLogger.logInfo('HTTP_REQUEST', {
        url: fullUrl,
        method: options.method || 'GET',
        timestamp: new Date().toISOString(),
      });

      const response = await fetch(fullUrl, config);
      clearTimeout(timeoutId);

      return await this.handleResponse(response, fullUrl, options.method || 'GET');
    } catch (error) {
      clearTimeout(timeoutId);

      if (error.name === 'AbortError') {
        const timeoutError = new Error('Request timeout');
        timeoutError.name = 'TimeoutError';
        throw timeoutError;
      }

      throw error;
    }
  }

  /**
   * Métodos HTTP específicos
   */
  async get(url, options = {}) {
    return this.request(url, { ...options, method: 'GET' });
  }

  async post(url, data, options = {}) {
    return this.request(url, {
      ...options,
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async patch(url, data, options = {}) {
    return this.request(url, {
      ...options,
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  }

  async delete(url, options = {}) {
    return this.request(url, { ...options, method: 'DELETE' });
  }
}

/**
 * Clase de error HTTP personalizada
 */
class HttpError extends Error {
  constructor(status, message, data = null) {
    super(message);
    this.name = 'HttpError';
    this.status = status;
    this.data = data;
  }
}

// Exportar instancia singleton
const httpClient = new HttpClient();
export default httpClient;
export { HttpError };
