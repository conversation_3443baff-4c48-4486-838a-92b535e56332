/**
 * CTM Service
 * Servicio para manejo de CipherTrust Managers
 */

import httpClient from '../core/httpClient.js';
import { API_ENDPOINTS } from '../config/apiConfig.js';
import securityLogger from '../../utils/SecurityLogger.js';

class CtmService {
  /**
   * Obtener todos los CTMs (Solo Admin)
   * @param {Object} filters - Filtros opcionales
   * @returns {Promise<Array>} Lista de CTMs
   */
  async getAllCtms(filters = {}) {
    try {
      const queryParams = this.buildQueryParams(filters);
      const url = `${API_ENDPOINTS.CTMS.BASE}${queryParams}`;

      securityLogger.logInfo('GET_ALL_CTMS_REQUEST', {
        filters,
        timestamp: new Date().toISOString(),
      });

      const response = await httpClient.get(url);

      // Manejar respuesta paginada del backend
      let ctmsArray = [];
      if (response && response.ctms && Array.isArray(response.ctms)) {
        ctmsArray = response.ctms;
      } else if (Array.isArray(response)) {
        ctmsArray = response;
      }

      securityLogger.logInfo('GET_ALL_CTMS_SUCCESS', {
        totalCtms: ctmsArray.length,
        totalFromBackend: response.total || ctmsArray.length,
        timestamp: new Date().toISOString(),
      });

      return ctmsArray;
    } catch (error) {
      securityLogger.logError('GET_ALL_CTMS_ERROR', {
        error: error.message,
        status: error.status,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  /**
   * Obtener CTMs asignados al usuario autenticado
   * @returns {Promise<Array>} Lista de CTMs del usuario
   */
  async getUserCtms() {
    try {
      securityLogger.logInfo('GET_USER_CTMS_REQUEST', {
        timestamp: new Date().toISOString(),
      });

      const response = await httpClient.get(API_ENDPOINTS.CTMS.USER.MY_CTMS);

      // Manejar respuesta paginada del backend
      let ctmsArray = [];
      if (response && response.ctms && Array.isArray(response.ctms)) {
        ctmsArray = response.ctms;
      } else if (Array.isArray(response)) {
        ctmsArray = response;
      }

      securityLogger.logInfo('GET_USER_CTMS_SUCCESS', {
        totalCtms: ctmsArray.length,
        totalFromBackend: response.total || ctmsArray.length,
        timestamp: new Date().toISOString(),
      });

      return ctmsArray;
    } catch (error) {
      securityLogger.logError('GET_USER_CTMS_ERROR', {
        error: error.message,
        status: error.status,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  /**
   * Obtener CTM por ID
   * @param {string} ctmId - ID del CTM
   * @returns {Promise<Object>} CTM encontrado
   */
  async getCtmById(ctmId) {
    try {
      securityLogger.logInfo('GET_CTM_BY_ID_REQUEST', {
        ctmId,
        timestamp: new Date().toISOString(),
      });

      const response = await httpClient.get(API_ENDPOINTS.CTMS.BY_ID(ctmId));
      
      securityLogger.logInfo('GET_CTM_BY_ID_SUCCESS', {
        ctmId: response.id,
        ctmName: response.name,
        timestamp: new Date().toISOString(),
      });

      return response;
    } catch (error) {
      securityLogger.logError('GET_CTM_BY_ID_ERROR', {
        ctmId,
        error: error.message,
        status: error.status,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  /**
   * Crear nuevo CTM (Solo Admin)
   * @param {Object} ctmData - Datos del CTM
   * @returns {Promise<Object>} CTM creado
   */
  async createCtm(ctmData) {
    try {
      this.validateCtmData(ctmData);

      securityLogger.logInfo('CREATE_CTM_REQUEST', {
        ctmName: ctmData.name,
        timestamp: new Date().toISOString(),
      });

      const response = await httpClient.post(API_ENDPOINTS.CTMS.CREATE, ctmData);
      
      securityLogger.logInfo('CREATE_CTM_SUCCESS', {
        ctmId: response.id,
        ctmName: response.name,
        timestamp: new Date().toISOString(),
      });

      return response;
    } catch (error) {
      securityLogger.logError('CREATE_CTM_ERROR', {
        ctmName: ctmData.name,
        error: error.message,
        status: error.status,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  /**
   * Actualizar CTM (Solo Admin)
   * @param {string} ctmId - ID del CTM
   * @param {Object} ctmData - Datos actualizados del CTM
   * @returns {Promise<Object>} CTM actualizado
   */
  async updateCtm(ctmId, ctmData) {
    try {
      securityLogger.logInfo('UPDATE_CTM_REQUEST', {
        ctmId,
        ctmName: ctmData.name,
        timestamp: new Date().toISOString(),
      });

      const response = await httpClient.patch(API_ENDPOINTS.CTMS.UPDATE(ctmId), ctmData);
      
      securityLogger.logInfo('UPDATE_CTM_SUCCESS', {
        ctmId: response.id,
        ctmName: response.name,
        timestamp: new Date().toISOString(),
      });

      return response;
    } catch (error) {
      securityLogger.logError('UPDATE_CTM_ERROR', {
        ctmId,
        error: error.message,
        status: error.status,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  /**
   * Eliminar CTM (Solo Admin)
   * @param {string} ctmId - ID del CTM
   * @returns {Promise<Object>} Respuesta de eliminación
   */
  async deleteCtm(ctmId) {
    try {
      securityLogger.logInfo('DELETE_CTM_REQUEST', {
        ctmId,
        timestamp: new Date().toISOString(),
      });

      const response = await httpClient.delete(API_ENDPOINTS.CTMS.DELETE(ctmId));
      
      securityLogger.logInfo('DELETE_CTM_SUCCESS', {
        ctmId,
        timestamp: new Date().toISOString(),
      });

      return response;
    } catch (error) {
      securityLogger.logError('DELETE_CTM_ERROR', {
        ctmId,
        error: error.message,
        status: error.status,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  /**
   * Asignar CTMs a un usuario (Solo Admin)
   * @param {string} userId - ID del usuario
   * @param {Array} ctmIds - IDs de los CTMs a asignar
   * @returns {Promise<Object>} Respuesta de asignación
   */
  async assignCtmsToUser(userId, ctmIds) {
    try {
      securityLogger.logInfo('ASSIGN_CTMS_TO_USER_REQUEST', {
        userId,
        ctmIds,
        timestamp: new Date().toISOString(),
      });

      const response = await httpClient.post(
        API_ENDPOINTS.CTMS.ASSIGN_TO_USER(userId),
        { ctmIds }
      );
      
      securityLogger.logInfo('ASSIGN_CTMS_TO_USER_SUCCESS', {
        userId,
        ctmIds,
        timestamp: new Date().toISOString(),
      });

      return response;
    } catch (error) {
      securityLogger.logError('ASSIGN_CTMS_TO_USER_ERROR', {
        userId,
        ctmIds,
        error: error.message,
        status: error.status,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  /**
   * Remover CTM de un usuario (Solo Admin)
   * @param {string} ctmId - ID del CTM
   * @param {string} userId - ID del usuario
   * @returns {Promise<Object>} Respuesta de remoción
   */
  async removeCtmFromUser(ctmId, userId) {
    try {
      securityLogger.logInfo('REMOVE_CTM_FROM_USER_REQUEST', {
        ctmId,
        userId,
        timestamp: new Date().toISOString(),
      });

      const response = await httpClient.delete(
        API_ENDPOINTS.CTMS.REMOVE_FROM_USER(ctmId, userId)
      );
      
      securityLogger.logInfo('REMOVE_CTM_FROM_USER_SUCCESS', {
        ctmId,
        userId,
        timestamp: new Date().toISOString(),
      });

      return response;
    } catch (error) {
      securityLogger.logError('REMOVE_CTM_FROM_USER_ERROR', {
        ctmId,
        userId,
        error: error.message,
        status: error.status,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  /**
   * Construir query parameters para filtros
   * @param {Object} filters - Filtros
   * @returns {string} Query string
   */
  buildQueryParams(filters) {
    const params = new URLSearchParams();
    
    Object.keys(filters).forEach(key => {
      if (filters[key] !== undefined && filters[key] !== null && filters[key] !== '') {
        params.append(key, filters[key]);
      }
    });
    
    return params.toString() ? `?${params.toString()}` : '';
  }

  /**
   * Validar datos de CTM
   * @param {Object} ctmData - Datos del CTM
   */
  validateCtmData(ctmData) {
    const errors = [];

    if (!ctmData.name) {
      errors.push('Nombre del CTM es requerido');
    }

    if (!ctmData.ipAddress) {
      errors.push('Dirección IP es requerida');
    }

    if (!ctmData.username) {
      errors.push('Usuario es requerido');
    }

    if (!ctmData.password) {
      errors.push('Contraseña es requerida');
    }

    if (!ctmData.domain) {
      errors.push('Dominio es requerido');
    }

    if (errors.length > 0) {
      throw new Error(`Datos de CTM inválidos: ${errors.join(', ')}`);
    }
  }
}

export default new CtmService();
