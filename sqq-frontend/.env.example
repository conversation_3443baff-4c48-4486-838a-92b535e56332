# API Configuration
# URL base de la API SQQ Backend
VITE_API_BASE_URL=http://localhost:3000

# Timeout para peticiones HTTP (en milisegundos)
VITE_API_TIMEOUT=30000

# Environment
VITE_NODE_ENV=development

# Opcional: Configuración adicional
# VITE_API_VERSION=v1
# VITE_DEBUG_MODE=true

# Para producción, cambia VITE_API_BASE_URL por tu URL de producción
# Ejemplos:
# VITE_API_BASE_URL=https://api.tudominio.com
# VITE_API_BASE_URL=https://tu-backend.herokuapp.com
# VITE_API_BASE_URL=https://tu-backend.vercel.app
