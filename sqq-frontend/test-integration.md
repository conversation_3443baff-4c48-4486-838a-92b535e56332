# 🧪 Guía de Pruebas de Integración CTM/HSM

## ✅ Errores Corregidos

### 1. JavaScript - Identificador duplicado
- **Error**: `Identifier 'clearError' has already been declared` en useKeys.js
- **Solución**: ✅ Eliminada declaración duplicada de `clearError`

### 2. Content Security Policy
- **Error**: `frame-ancestors` mal colocado en `connect-src`
- **Solución**: ✅ Movido `frame-ancestors` como directiva separada
- **Error**: `X-Frame-Options` en meta tag
- **Solución**: ✅ Eliminado (debe configurarse en el servidor)

## 🔧 Funcionalidades Implementadas

### Backend (sqq-api)
- ✅ Servicios CTM y HSM completos
- ✅ Endpoint `/hsm-keys/by-hsm/:hsmId` para llaves por HSM
- ✅ Asignación múltiple de CTMs/HSMs a usuarios
- ✅ Validaciones y encriptación de contraseñas

### Frontend (sqq-frontend)
- ✅ Servicios dinámicos (ctmService, hsmService, hsmKeyService)
- ✅ Hook useServices para gestión de servicios
- ✅ ServiceSelector con datos dinámicos
- ✅ UserCreateModal con selección múltiple
- ✅ Filtrado de llaves por servicio seleccionado

## 🧪 Plan de Pruebas

### 1. Pruebas de Backend
```bash
# Iniciar el backend
cd sqq-api
npm run start:dev

# Probar endpoints clave:
# GET /ctms - Lista de CTMs (Admin)
# GET /hsms - Lista de HSMs (Admin)
# GET /user/ctms - CTMs del usuario
# GET /user/hsms - HSMs del usuario
# POST /users - Crear usuario con ctmIds/hsmIds
# GET /keys/by-ctm?ctmId=xxx - Llaves por CTM
# GET /hsm-keys/by-hsm/:hsmId - Llaves por HSM
```

### 2. Pruebas de Frontend
```bash
# Iniciar el frontend
cd sqq-frontend
npm run dev

# Verificar en el navegador:
# - No hay errores de JavaScript en la consola
# - ServiceSelector carga servicios dinámicamente
# - Creación de usuarios con múltiples servicios
# - Filtrado de llaves por servicio
```

### 3. Flujo de Prueba Completo

#### Como Administrador:
1. **Crear CTMs y HSMs**
   - Ir a Panel Admin → CTM Management
   - Crear nuevo CTM con configuración completa
   - Ir a Panel Admin → HSM Management  
   - Crear nuevo HSM

2. **Crear Usuario con Servicios**
   - Ir a Panel Admin → User Management
   - Crear nuevo usuario
   - Seleccionar múltiples CTMs y HSMs
   - Verificar que se guarda correctamente

#### Como Usuario:
1. **Verificar Servicios Asignados**
   - Iniciar sesión como usuario creado
   - Verificar que ServiceSelector muestra solo servicios asignados
   - Cambiar entre diferentes servicios

2. **Gestión de Llaves por Servicio**
   - Subir llaves a CTM seleccionado
   - Crear llaves HSM en HSM seleccionado
   - Verificar filtrado correcto por servicio

## 🐛 Posibles Problemas y Soluciones

### Error: "CTM/HSM not found"
- **Causa**: IDs incorrectos o servicios no existentes
- **Solución**: Verificar que los CTMs/HSMs existen en la BD

### Error: "User does not have access"
- **Causa**: Usuario no asignado al servicio
- **Solución**: Verificar asignaciones en la tabla de relaciones

### ServiceSelector vacío
- **Causa**: Usuario sin servicios asignados
- **Solución**: Asignar servicios desde panel de admin

### Llaves no se filtran
- **Causa**: Endpoint de filtrado no funciona
- **Solución**: Verificar logs del backend y endpoints

## 📊 Verificaciones de Base de Datos

```sql
-- Verificar CTMs
SELECT * FROM ctm;

-- Verificar HSMs  
SELECT * FROM hsm;

-- Verificar relaciones usuario-CTM
SELECT u.email, c.name as ctm_name 
FROM user u 
JOIN user_ctms_ctm uc ON u.id = uc.userId 
JOIN ctm c ON uc.ctmId = c.id;

-- Verificar relaciones usuario-HSM
SELECT u.email, h.name as hsm_name 
FROM user u 
JOIN user_hsms_hsm uh ON u.id = uh.userId 
JOIN hsm h ON uh.hsmId = h.id;

-- Verificar llaves por CTM
SELECT k.*, c.name as ctm_name 
FROM key k 
JOIN ctm c ON k.ctmId = c.id;

-- Verificar llaves HSM
SELECT hk.*, h.name as hsm_name 
FROM hsm_key hk 
JOIN hsm h ON hk.hsmId = h.id;
```

## ✨ Estado Final

🎉 **Integración Completada**
- ✅ Todos los errores de JavaScript corregidos
- ✅ CSP configurado correctamente  
- ✅ Backend y frontend completamente conectados
- ✅ Funcionalidades de múltiples CTM/HSM implementadas
- ✅ Gestión dinámica de servicios funcionando
- ✅ Filtrado de llaves por servicio operativo

La aplicación está lista para pruebas completas y uso en producción.
