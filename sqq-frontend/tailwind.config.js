/**
 * @fileoverview Configuración de Tailwind CSS para el proyecto SQQ Frontend
 * @description Configuración personalizada de Tailwind CSS con modo oscuro,
 * breakpoints responsivos personalizados y optimizaciones para el sistema de diseño.
 * <AUTHOR>
 * @version 1.0.0
 */

/** @type {import('tailwindcss').Config} */
export default {
  /**
   * Archivos a escanear para clases de Tailwind CSS
   * @description Define los archivos donde Tailwind debe buscar clases CSS
   * para incluir en el bundle final, optimizando el tamaño del CSS generado
   */
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}"
  ],

  /**
   * Configuración del modo oscuro
   * @description Habilita modo oscuro basado en clase CSS 'dark' en lugar de
   * preferencias del sistema, permitiendo control manual del tema
   */
  darkMode: 'class',

  /**
   * Configuración del tema personalizado
   * @description Extensiones al tema base de Tailwind CSS para necesidades específicas
   */
  theme: {
    extend: {
      /**
       * Breakpoints responsivos personalizados
       * @description Agrega breakpoint 'xs' para dispositivos muy pequeños
       * mejorando el control del diseño responsivo en pantallas móviles
       */
      screens: {
        'xs': '475px', // Extra small devices (large phones, 475px and up)
      },
    },
  },

  /**
   * Plugins de Tailwind CSS
   * @description Array de plugins adicionales para extender funcionalidad
   * Actualmente vacío, listo para agregar plugins según necesidades futuras
   */
  plugins: [],
}
