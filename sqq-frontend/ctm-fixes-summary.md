# 🔧 Resumen de Correcciones CTM

## Problemas Identificados y Solucionados

### **1. Error "ctmId must be a UUID" en la ruta `keys/by-ctm?ctmId=`**

**Problema**: El parámetro `ctmId` en la query string estaba vacío o no era un UUID válido, causando errores de validación en el backend.

**✅ Soluciones Implementadas**:

1. **Validación en Frontend** (`sqq-frontend/src/services/keys/keyService.js`):
   ```javascript
   async getKeysByCtm(filters = {}) {
     // Validar que ctmId esté presente
     if (!filters.ctmId) {
       throw new Error('ctmId es requerido para obtener llaves por CTM');
     }
     // ... resto del código
   }
   ```

2. **Inclusión de ctmId en Query Parameters**:
   ```javascript
   buildQueryParams(filters) {
     // ... otros parámetros
     // Agregar ctmId para filtros de CTM
     if (filters.ctmId) {
       params.append('ctmId', filters.ctmId);
     }
   }
   ```

3. **Hook useKeys ya maneja correctamente el ctmId**:
   ```javascript
   const getKeysByCtm = useCallback(async (ctmId, filters = {}) => {
     const requestFilters = {
       page: filters.page || currentPage,
       limit: filters.limit || keysPerPage,
       ctmId, // ✅ Se pasa correctamente
       ...filters
     };
   });
   ```

### **2. Falta el campo `ctmId` en el payload al crear llaves CTM**

**Problema**: Cuando se creaba una llave CTM, el frontend no enviaba el `ctmId` en el payload, por lo que el backend no sabía qué configuración CTM usar.

**✅ Soluciones Implementadas**:

1. **Modificación del KeyUploadModal** (`sqq-frontend/src/components/dashboard/user/KeyUploadModal.jsx`):
   ```javascript
   const handleSubmit = async (e) => {
     // ... validaciones
     
     let keyData;
     
     if (isHSMService) {
       // Para HSM, incluir hsmId y no incluir exportable
       keyData = { 
         ...formData, 
         exportable: undefined,
         hsmId: selectedService?.id 
       };
     } else {
       // Para CTM, incluir ctmId y excluir message
       keyData = { 
         ...formData,
         message: undefined, // ✅ Excluir message para CTM
         ctmId: selectedService?.id  // ✅ Incluir ctmId para CTM
       };
     }
     
     await onUpload(keyData);
   };
   ```

2. **Backend ya estaba preparado**:
   - El DTO `UploadKeyDto` ya tenía el campo `ctmId` como requerido
   - El controlador ya pasaba `uploadKeyDto.ctmId` al servicio
   - Solo faltaba que el frontend enviara este campo

### **3. Eliminación del campo `message` para CTM**

**Problema**: Se estaba enviando un campo `message` que no existe en el DTO de CTM, causando errores de validación.

**✅ Solución**:
- Modificado el `KeyUploadModal` para excluir `message: undefined` en el payload de CTM
- El campo `message` solo se incluye para HSM donde es requerido

## Archivos Modificados

### Frontend (`sqq-frontend/`)
1. **`src/services/keys/keyService.js`**:
   - ✅ Agregada validación de `ctmId` en `getKeysByCtm()`
   - ✅ Incluido `ctmId` en `buildQueryParams()`

2. **`src/components/dashboard/user/KeyUploadModal.jsx`**:
   - ✅ Agregado `ctmId` al payload para servicios CTM
   - ✅ Excluido `message` del payload para CTM
   - ✅ Mantenido `hsmId` para servicios HSM

3. **`src/services/index.js`**:
   - ✅ Agregado `getByCtm` al índice de servicios

### Backend (ya estaba correcto)
- ✅ `sqq-api/src/keys/dto/upload-key.dto.ts` - Campo `ctmId` requerido
- ✅ `sqq-api/src/keys/keys.controller.ts` - Controlador pasa `ctmId` al servicio
- ✅ `sqq-api/src/keys/dto/get-ctm-keys.dto.ts` - DTO para filtros por CTM

## Flujo Corregido

### **Crear Llave CTM**:
1. Usuario selecciona servicio CTM en el selector global
2. Abre modal de creación de llave
3. Modal incluye `selectedService.id` como `ctmId` en el payload
4. Backend recibe `ctmId` y usa la configuración correcta

### **Obtener Llaves por CTM**:
1. Usuario selecciona servicio CTM
2. `getKeysByService()` llama a `getKeysByCtm(serviceId)`
3. `getKeysByCtm()` valida que `ctmId` esté presente
4. Se construye URL con `?ctmId=uuid-valido`
5. Backend recibe `ctmId` válido y retorna las llaves

## Validaciones Agregadas

1. **Frontend**: Validación de `ctmId` presente antes de hacer la llamada
2. **Frontend**: Exclusión de campos no válidos según el tipo de servicio
3. **Backend**: Ya tenía validación UUID para `ctmId` en los DTOs

## Pruebas

Se creó un script de prueba (`test-ctm-fixes.js`) que simula:
- ✅ Subida de llave CTM con `ctmId`
- ✅ Subida de llave HSM con `hsmId`
- ✅ Obtener llaves por CTM con `ctmId` válido
- ✅ Manejo de errores con `ctmId` vacío o inválido

## Estado Final

🎉 **Ambos problemas han sido resueltos**:
1. ✅ El error "ctmId must be a UUID" ya no debería ocurrir
2. ✅ Las llaves CTM ahora se crean con el `ctmId` correcto
3. ✅ Se eliminó el campo `message` innecesario para CTM
4. ✅ El flujo funciona tanto para CTM como para HSM

Los cambios son compatibles hacia atrás y no afectan la funcionalidad existente.
