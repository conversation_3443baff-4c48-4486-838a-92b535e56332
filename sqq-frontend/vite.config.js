/**
 * @fileoverview Configuración de Vite para el proyecto SQQ Frontend
 * @description Configuración completa de Vite con plugin personalizado para variables de entorno,
 * headers de seguridad para desarrollo y configuración de build optimizada para producción.
 * <AUTHOR>
 * @version 1.0.0
 */

import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'

/**
 * Plugin personalizado para reemplazar variables de entorno en index.html
 * @function htmlEnvReplace
 * @param {Object} env - Variables de entorno cargadas por Vite
 * @returns {Object} Plugin de Vite para transformación de HTML
 * @description Reemplaza placeholders de variables de entorno en el archivo index.html
 * durante el proceso de build, permitiendo configuración dinámica de CSP y otros headers.
 */

const htmlEnvReplace = (env) => {
  return {
    name: 'html-env-replace',
    enforce: 'post',
    // eslint-disable-next-line no-unused-vars
    transformIndexHtml(html, context) {
      const apiUrl = env.VITE_API_BASE_URL || 'http://localhost:3000'

      // Reemplazar variables de entorno en el HTML
      const result = html.replace(
        /%VITE_API_BASE_URL%/g,
        apiUrl
      )

      return result
    }
  }
}

/**
 * Configuración principal de Vite
 * @description Configuración que se adapta según el modo (development/production)
 * con optimizaciones específicas para cada entorno
 * @see https://vite.dev/config/
 */
export default defineConfig(({ mode }) => {
  // Cargar variables de entorno
  // eslint-disable-next-line no-undef
  const env = loadEnv(mode, process.cwd(), 'VITE_')

  return {
    plugins: [react(), htmlEnvReplace(env)],

    // 🛡️ Configuración de seguridad para desarrollo
    server: {
      headers: {
        // Previene clickjacking
        'X-Frame-Options': 'DENY',

        // Previene MIME type sniffing
        'X-Content-Type-Options': 'nosniff',

        // Controla información del referrer
        'Referrer-Policy': 'strict-origin-when-cross-origin',

        // Deshabilita APIs peligrosas
        'Permissions-Policy': 'camera=(), microphone=(), geolocation=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=()',

        // Previene DNS prefetching
        'X-DNS-Prefetch-Control': 'off',

        // Fuerza HTTPS en producción (solo para desarrollo local)
        'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',

        // Headers adicionales de seguridad
        'X-XSS-Protection': '1; mode=block',
        'X-Permitted-Cross-Domain-Policies': 'none',
        'Cross-Origin-Embedder-Policy': 'require-corp',
        'Cross-Origin-Opener-Policy': 'same-origin',
        'Cross-Origin-Resource-Policy': 'same-origin'
      }
    },

    // 🔒 Configuración de build para producción
    build: {
      // Minificar para ofuscar código
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: true, // Remover console.logs en producción
          drop_debugger: true // Remover debugger statements
        }
      },

      // Generar source maps solo en desarrollo
      sourcemap: false,

      // Configuración de chunks para mejor seguridad
      rollupOptions: {
        output: {
          // Ofuscar nombres de archivos
          entryFileNames: 'assets/[name]-[hash].js',
          chunkFileNames: 'assets/[name]-[hash].js',
          assetFileNames: 'assets/[name]-[hash].[ext]'
        }
      }
    }
  }
})
